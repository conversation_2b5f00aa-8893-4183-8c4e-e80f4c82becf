# Flutter Build Script with Obfuscation for Windows PowerShell
# Usage: .\build_release.ps1 [platform]
# Platforms: android, ios, web, windows, linux, macos

param(
    [string]$Platform = "android"
)

$BuildDir = "build/debug_info"

Write-Host "Building Flutter app for $Platform with obfuscation..." -ForegroundColor Green

# Create debug info directory if it doesn't exist
if (!(Test-Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir -Force
    Write-Host "Created directory: $BuildDir" -ForegroundColor Yellow
}

switch ($Platform.ToLower()) {
    "android" {
        Write-Host "Building Android APK with obfuscation..." -ForegroundColor Cyan
        flutter build apk --release --obfuscate --split-debug-info=$BuildDir
    }
    "android-bundle" {
        Write-Host "Building Android App Bundle with obfuscation..." -ForegroundColor Cyan
        flutter build appbundle --release --obfuscate --split-debug-info=$BuildDir
    }
    "ios" {
        Write-Host "Building iOS with obfuscation..." -ForegroundColor Cyan
        flutter build ios --release --obfuscate --split-debug-info=$BuildDir
    }
    "web" {
        Write-Host "Building Web with obfuscation..." -ForegroundColor Cyan
        flutter build web --release --obfuscate --split-debug-info=$BuildDir
    }
    "windows" {
        Write-Host "Building Windows with obfuscation..." -ForegroundColor Cyan
        flutter build windows --release --obfuscate --split-debug-info=$BuildDir
    }
    "linux" {
        Write-Host "Building Linux with obfuscation..." -ForegroundColor Cyan
        flutter build linux --release --obfuscate --split-debug-info=$BuildDir
    }
    "macos" {
        Write-Host "Building macOS with obfuscation..." -ForegroundColor Cyan
        flutter build macos --release --obfuscate --split-debug-info=$BuildDir
    }
    default {
        Write-Host "Unknown platform: $Platform" -ForegroundColor Red
        Write-Host "Available platforms: android, android-bundle, ios, web, windows, linux, macos" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "Build completed! Debug info saved to: $BuildDir" -ForegroundColor Green
Write-Host "Remember to keep the debug info files for crash reporting and debugging." -ForegroundColor Yellow

# Show build output location
if ($Platform -eq "android") {
    $ApkPath = "build\app\outputs\flutter-apk\app-release.apk"
    if (Test-Path $ApkPath) {
        Write-Host "APK location: $ApkPath" -ForegroundColor Magenta
    }
} elseif ($Platform -eq "android-bundle") {
    $AabPath = "build\app\outputs\bundle\release\app-release.aab"
    if (Test-Path $AabPath) {
        Write-Host "AAB location: $AabPath" -ForegroundColor Magenta
    }
}
