org.gradle.jvmargs=-Xmx4G
android.useAndroidX=true
android.enableJetifier=true

# AGP 8.5.1+ Enhanced 16KB page size support
android.enableR8.fullMode=true
android.experimental.enableArtProfiles=true
android.experimental.enableNewResourceShrinker=true

# AGP 8.5.1+ Native 16KB page size compatibility
android.native.useEmbeddedDexer=true
android.experimental.enableNewNativeSymbolTableFormat=true

# NDK and native library optimization for AGP 8.5.1+
android.nonTransitiveRClass=true
android.nonFinalResIds=true

# JVM target compatibility
kotlin.jvm.target.validation.mode=warning

# AGP 8.5.1+ 16KB page size optimizations
android.experimental.enableR8.fullMode=true
android.experimental.enableArtProfiles=true
