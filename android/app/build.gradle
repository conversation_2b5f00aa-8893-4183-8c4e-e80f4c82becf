plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.gestaltsystech.digitalCvCandidate"
    compileSdk 36
    ndkVersion flutter.ndkVersion

    // Support for 16KB page sizes
    buildFeatures {
        buildConfig true
    }

    // AGP 8.6.0+ native 16KB page size support
    androidResources {
        generateLocaleConfig false
    }



    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.gestaltsystech.digitalCvCandidate"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion flutter.minSdkVersion
        targetSdk flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
            // Force 16KB page alignment
            debugSymbolLevel 'SYMBOL_TABLE'
        }

        // Enable 16KB page size support
        manifestPlaceholders = [
            'applicationName': 'io.flutter.app.FlutterApplication'
        ]

        // Critical 16KB page size support configuration
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // AGP 8.5.1+ Enhanced 16KB page size support
        externalNativeBuild {
            cmake {
                arguments "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON",
                          "-DANDROID_PAGE_SIZE_AGNOSTIC=ON",
                          "-DCMAKE_ANDROID_STL_TYPE=c++_shared",
                          "-DANDROID_16KB_PAGE_SIZE_COMPAT=ON"
                cppFlags "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=1",
                         "-DANDROID_PAGE_SIZE_AGNOSTIC=1",
                         "-DANDROID_16KB_PAGE_SIZE_COMPAT=1",
                         "-fPIC"
            }
        }

        // AGP 8.5.1+ native 16KB page size configuration
        packaging {
            jniLibs {
                useLegacyPackaging = false
                // Force 16KB alignment for all native libraries
                pickFirsts += ["**/*.so"]
            }
        }

    }

    signingConfigs {
        release {
            storeFile file("../my-release-key.keystore")
            storePassword "digital_cv_candidate"
            keyAlias "my-key-alias"
            keyPassword "digital_cv_candidate"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Support for 16KB page sizes
            ndk {
                debugSymbolLevel 'full'
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
            }

            // Desugaring configuration
            multiDexEnabled true

            // Disable global synthetics to fix DEX error
            postprocessing {
                removeUnusedCode false
                removeUnusedResources false
                obfuscate false
                optimizeCode false
            }
        }

        debug {
            // Support for 16KB page sizes in debug builds
            ndk {
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
            }
            multiDexEnabled true
        }
    }


    // Enable 16KB page size support
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = false
        }
        abi {
            enableSplit = true
        }
    }

    // Ensure 16KB page size support for all ABIs
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
            universalApk true
        }
    }

}

flutter {
    source '../..'
}

dependencies {
    // ...existing dependencies...
    implementation 'androidx.multidex:multidex:2.0.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    implementation platform('com.google.firebase:firebase-bom:34.0.0')
    implementation 'com.google.firebase:firebase-analytics'
}
