import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    
    // Validate runtime signature and jailbreak detection
    if !RuntimeSignatureValidator.validateRuntimeSignature() {
      // Log security violation
      print("Security Alert: Invalid runtime signature detected")
      // In production, you might want to exit the app or show a warning
      // exit(EXIT_FAILURE)
    }
    
    if RuntimeSignatureValidator.isJailbroken() {
      // Log jailbreak detection
      print("Security Alert: Jailbroken device detected")
      // In production, you might want to limit functionality or exit
      // exit(EXIT_FAILURE)
    }
    
    // Setup Flutter method channel for security checks
    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    let securityChannel = FlutterMethodChannel(name: "com.gestaltsystech.digitalCvCandidate/security",
                                             binaryMessenger: controller.binaryMessenger)
    
    securityChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "validateSignature":
        result(RuntimeSignatureValidator.validateRuntimeSignature())
      case "isJailbroken":
        result(RuntimeSignatureValidator.isJailbroken())
      default:
        result(FlutterMethodNotImplemented)
      }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
