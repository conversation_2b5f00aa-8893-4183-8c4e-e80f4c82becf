<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>DigitalCV Candidate</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>DigitalCV Candidate</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.gestaltsystech.digitalCvCandidate</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>signinwithapple</string>
				<string>com.gestaltsystech.digitalCvCandidate</string>
				<string>com.googleusercontent.apps.787795230704-c1p9jh08k003efihkpsbl3irojbta70v</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>GIDClientID</key>
	<string>787795230704-c1p9jh08k003efihkpsbl3irojbta70v.apps.googleusercontent.com</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Kami memerlukan akses lokasi untuk membantu Anda menemukan lowongan pekerjaan di sekitar Anda dan menampilkan perusahaan terdekat yang sedang membuka lowongan, bahkan saat aplikasi berjalan di latar belakang.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Kami memerlukan akses lokasi untuk menampilkan lowongan pekerjaan dan perusahaan terdekat di sekitar lokasi Anda saat ini ketika Anda menggunakan aplikasi.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Kami memerlukan akses lokasi untuk membantu Anda menemukan lowongan pekerjaan terdekat dan menampilkan perusahaan di sekitar Anda yang sedang membuka lowongan, baik saat menggunakan aplikasi maupun saat berjalan di latar belakang.</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-apps</string>
		<string>itms</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Kami memerlukan akses ke galeri foto untuk mengambil dan mengunggah foto profil Anda serta dokumen CV yang dibutuhkan dalam proses lamaran kerja.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>http</string>
		<string>https</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>id</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>Kami memerlukan akses kamera untuk mengambil foto profil Anda dan memfoto dokumen CV yang dibutuhkan dalam proses lamaran kerja.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
