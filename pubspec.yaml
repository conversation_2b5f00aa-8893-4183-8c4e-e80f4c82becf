name: digital_cv_mobile
description: "Digitalcv merupakan sebuah platform digital yang merevolusi proses rekrutmen konvensional menjadi lebih sistematis. Digitalcv memungkinkan perusahaan penyedia lowongan kerja serta kandidatnya dapat melakukan proses rekrutmen dimanapun, kapanpun, dan data kandidat akan diterima secara real time dengan keamanan data yang dilindungi oleh teknologi tinggi."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.4+30

environment:
  sdk: ">=3.2.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  # Font Awesome icons for social media and other icons
  font_awesome_flutter: ^10.7.0
  get: ^4.6.6
  shared_preferences: ^2.2.3
  http: ^1.1.0
  package_info_plus: ^8.3.0
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  infinite_scroll_pagination: ^4.1.0
  # uni_links: ^0.5.1
  app_links: ^6.2.0
  dropdown_search: ^5.0.6
  in_app_review: ^2.0.8
  url_launcher: ^6.3.0
  webview_flutter: ^4.13.0
  image_picker: ^1.0.8
  shimmer: ^3.0.0
  intl: 0.20.2
  flutter_multi_formatter: ^2.13.7
  syncfusion_flutter_datepicker: ^29.2.10
  share_plus: ^7.2.2
  another_flushbar: ^1.12.30
  flutter_html: ^3.0.0-beta.2
  crop_your_image: ^2.0.0
  html_unescape: ^2.0.0
  device_info_plus: ^9.1.2
  network_info_plus: ^6.0.1
  geolocator: ^11.1.0
  firebase_messaging: ^15.2.7
  rxdart: ^0.28.0
  flutter_notification_channel: ^3.1.1
  flutter_local_notifications: ^18.0.1
  go_router: ^14.6.2
  translator: ^1.0.3+1
  flutter_launcher_icons: ^0.14.3
  skeletonizer: ^2.0.1
  mockito: ^5.4.6
  flutter_secure_storage: ^9.2.2
  logger: ^2.6.0
  flutter_inappwebview: ^6.1.5
  equatable: ^2.0.7
  http_certificate_pinning: ^3.0.1
  app_settings: ^6.1.1
  android_intent_plus: ^5.3.0
  flutter_jailbreak_detection_plus: ^1.10.6
  crypto: ^3.0.3
  encrypt: ^5.0.3
  showcaseview: ^4.0.1
  web_socket_channel: ^3.0.1
  flutter_spinkit: ^5.2.2
  upgrader: ^12.1.0
  file_picker: ^10.3.3
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  open_file: ^3.5.10

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/digital_cv_logo.png"
  remove_alpha_ios: true
  background_color: "#FFFFFF"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/
    - assets/images/
    - assets/lang/id-ID.json
    - assets/lang/en-EN.json
    - assets/lang/cn-CN.json
    - assets/ca/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-Italic.ttf
          style: italic
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  # Flutter build configuration for obfuscation
  # Note: These settings are for reference. Use build scripts for actual obfuscation.
  # build:
  #   # Enable code obfuscation for release builds
  #   obfuscate: true
  #   # Split debug info for better crash reporting (stored separately)
  #   split-debug-info: "build/debug_info"
  #   # Additional build optimization flags
  #   tree-shake-icons: true
  #   # Target platforms for optimized builds
  #   targets:
  #     - android-apk
  #     - android-appbundle
  #     - ios
  #     - web
