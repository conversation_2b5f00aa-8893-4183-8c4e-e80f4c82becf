# Flutter Code Obfuscation Guide

## Overview

Code obfuscation adalah proses mengubah kode Dart menjadi lebih sulit dibaca dan di-reverse engineer, sambil tetap mempertahankan fungsionalitas aplikasi. Flutter mendukung obfuscation untuk build release.

## Benefits

- **Security**: Melindungi logika bisnis dan algoritma sensitif
- **IP Protection**: Mencegah reverse engineering kode
- **Anti-Piracy**: Membuat aplikasi lebih sulit untuk diduplikasi
- **Performance**: Mengurangi ukuran aplikasi dengan menghapus informasi debug

## Configuration

### 1. Build Commands

Untuk menggunakan obfuscation, gunakan flag `--obfuscate` dan `--split-debug-info` saat build:

```bash
# Android APK
flutter build apk --release --obfuscate --split-debug-info=build/debug_info

# Android App Bundle
flutter build appbundle --release --obfuscate --split-debug-info=build/debug_info

# iOS
flutter build ios --release --obfuscate --split-debug-info=build/debug_info

# Web
flutter build web --release --obfuscate --split-debug-info=build/debug_info
```

### 2. Using Build Scripts

Gunakan script yang telah disediakan:

#### PowerShell (Windows):

```powershell
# Build Android APK
.\build_release.ps1 android

# Build Android App Bundle
.\build_release.ps1 android-bundle

# Build untuk platform lain
.\build_release.ps1 ios
.\build_release.ps1 web
.\build_release.ps1 windows
```

#### Bash (Linux/macOS):

```bash
# Make script executable
chmod +x build_release.sh

# Build Android APK
./build_release.sh android

# Build untuk platform lain
./build_release.sh ios
./build_release.sh web
```

## Understanding Obfuscation

### What Gets Obfuscated:

- ✅ Dart class names
- ✅ Method names
- ✅ Field names
- ✅ Variable names
- ✅ Function names

### What Doesn't Get Obfuscated:

- ❌ String literals
- ❌ Assets
- ❌ Native code (Android/iOS)
- ❌ External library APIs
- ❌ Platform channel names

## Debug Information

### Split Debug Info

File `--split-debug-info` berisi informasi yang diperlukan untuk:

- Crash reporting yang meaningful
- Performance profiling
- Stack trace analysis

### Location

Debug info disimpan di: `build/debug_info/`

### Important Notes

- **BACKUP**: Selalu backup folder debug_info untuk setiap release
- **VERSION CONTROL**: Jangan commit debug info ke git (sudah ada di .gitignore)
- **MAPPING**: Gunakan debug info untuk mapping obfuscated stack traces

## Crash Reporting Integration

### Firebase Crashlytics

```dart
// Upload debug symbols ke Crashlytics
// Script akan otomatis upload jika dikonfigurasi
```

### Sentry

```dart
// Konfigurasi untuk Sentry
import 'package:sentry_flutter/sentry_flutter.dart';

await SentryFlutter.init(
  (options) {
    options.dsn = 'YOUR_DSN';
    // Debug info akan digunakan otomatis
  },
);
```

## Testing Obfuscated Builds

### 1. Functionality Test

- Test semua fitur aplikasi
- Verify navigation works
- Check API calls
- Test platform-specific features

### 2. Performance Test

- Compare app size before/after
- Test startup time
- Memory usage analysis

### 3. Crash Test

- Induce crashes intentionally
- Verify stack traces are deobfuscated correctly
- Test crash reporting service

## Best Practices

### 1. Code Organization

```dart
// Good: Minimize sensitive logic exposure
class SecureService {
  String _apiKey = 'hidden_key';

  Future<String> _internalMethod() async {
    // Sensitive logic here
  }
}

// Avoid: Exposing sensitive data in class names
class MyCompanySecretAlgorithm {
  // This class name will be obfuscated but still avoid obvious naming
}
```

### 2. String Protection

```dart
// For highly sensitive strings, consider additional protection
class StringObfuscator {
  static String decode(List<int> encoded) {
    return String.fromCharCodes(encoded.map((e) => e ^ 42));
  }
}

// Usage
final apiKey = StringObfuscator.decode([65, 66, 67]); // Simple XOR
```

### 3. ProGuard/R8 Configuration (Android)

Di `android/app/proguard-rules.pro`:

```proguard
# Keep important classes
-keep class io.flutter.** { *; }
-keep class com.yourpackage.** { *; }

# Obfuscate everything else
-obfuscationdictionary proguard-keywords.txt
-classobfuscationdictionary proguard-keywords.txt
-packageobfuscationdictionary proguard-keywords.txt
```

## Troubleshooting

### Common Issues

1. **Reflection Errors**

   ```dart
   // Problematic
   Type type = Type.forName("com.example.MyClass");

   // Solution: Use alternatives or keep classes in ProGuard
   ```

2. **Platform Channel Issues**

   ```dart
   // Platform channel names are NOT obfuscated
   static const platform = MethodChannel('com.example/battery');
   ```

3. **JSON Serialization**
   ```dart
   // Use json_annotation to handle obfuscated field names
   @JsonSerializable()
   class User {
     @JsonKey(name: 'user_id')
     final String userId;
   }
   ```

### Debug Obfuscated Stack Traces

1. **Get stack trace** dari crash report
2. **Use flutter symbolize**:
   ```bash
   flutter symbolize -i crash_stack_trace.txt -d build/debug_info
   ```

## Security Considerations

### Limitations

- Obfuscation is NOT encryption
- Determined attackers can still reverse engineer
- Consider additional security measures:
  - Certificate pinning (already implemented)
  - API key rotation
  - Server-side validation
  - Runtime Application Self-Protection (RASP)

### Additional Security

```dart
// Example: Runtime integrity checks
class SecurityCheck {
  static bool isDebuggingDetected() {
    return kDebugMode; // Simple check
  }

  static void performIntegrityCheck() {
    if (isDebuggingDetected()) {
      // Handle debug detection
    }
  }
}
```

## Build Size Comparison

After implementing obfuscation, compare build sizes:

```bash
# Before obfuscation
flutter build apk --release
# Check size of build/app/outputs/flutter-apk/app-release.apk

# After obfuscation
flutter build apk --release --obfuscate --split-debug-info=build/debug_info
# Check size difference
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Build Obfuscated Release
on:
  push:
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2

      - name: Build obfuscated APK
        run: |
          flutter build apk --release --obfuscate --split-debug-info=build/debug_info

      - name: Archive debug info
        uses: actions/upload-artifact@v3
        with:
          name: debug-info-${{ github.sha }}
          path: build/debug_info/
```

## Monitoring & Analytics

Track obfuscation effectiveness:

- App size reduction percentage
- Crash report quality
- Performance impact
- Security incident reduction

## Conclusion

Code obfuscation menambah lapisan keamanan pada aplikasi Flutter Anda. Kombinasikan dengan:

- HTTP Certificate Pinning (sudah diimplementasi)
- Proper API security
- Server-side validation
- Regular security audits

Untuk hasil terbaik, lakukan testing menyeluruh pada obfuscated builds sebelum production release.
