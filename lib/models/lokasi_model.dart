class LokasiModel {
  final int id;
  final String nama;

  LokasiModel({
    required this.id,
    required this.nama,
  });

  factory LokasiModel.fromJson(Map<String, dynamic> json) {
    return LokasiModel(
      id: int.parse(json['id'] ?? '0'),
      nama: json['nama'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nama': nama,
    };
  }

  @override
  String toString() => nama;
}
