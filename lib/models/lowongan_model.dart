class LowonganModel {
  final String id;
  final String idReq;
  final String perusahaan;
  final String department;
  final String posisi;
  final String atasan;
  final String bawahan;
  final String jmlhKandidat;
  final String salary;
  final String lokasiKerja;
  final String sistemKerja;
  final String lamaKerja;
  final String kPendidikan;
  final String kUsia;
  final String kSim;
  final String kJk;
  final String kPengalaman;
  final String kPengalamanSama;
  final String kStatus;
  final String kKhusus;
  final String note;
  final String status;
  final String createAt;
  final String updateAt;
  final String ketUpdate;
  final String idKoordinator;
  final String kJurusan;
  final String divisi;
  final String kSekolah;
  final String tipePekerjaan;
  final String lokasiKerja2;
  final String waktuLalu;
  final String tglPosting;
  final String logoURL;
  bool isFavorit;
  bool isLamar;
  final String tempKode;
  final String alamatPerusahaan;
  final String tipePerusahaan;
  final List<dynamic> arrLokasi;
  final String link;
  final String linkPerusahaan;
  final String descPerusahaan;
  final String kk;
  final String kb;
  final String km;
  final String kbdu;
  final String rlp;
  final String expairedDate;
  String? checkLowongan;

  LowonganModel({
    required this.id,
    required this.idReq,
    required this.perusahaan,
    required this.department,
    required this.posisi,
    required this.atasan,
    required this.bawahan,
    required this.jmlhKandidat,
    required this.salary,
    required this.lokasiKerja,
    required this.sistemKerja,
    required this.lamaKerja,
    required this.kPendidikan,
    required this.kUsia,
    required this.kSim,
    required this.kJk,
    required this.kPengalaman,
    required this.kPengalamanSama,
    required this.kStatus,
    required this.kKhusus,
    required this.note,
    required this.status,
    required this.createAt,
    required this.updateAt,
    required this.ketUpdate,
    required this.idKoordinator,
    required this.kJurusan,
    required this.divisi,
    required this.kSekolah,
    required this.tipePekerjaan,
    required this.lokasiKerja2,
    required this.waktuLalu,
    required this.tglPosting,
    required this.logoURL,
    required this.isFavorit,
    required this.isLamar,
    required this.tempKode,
    required this.alamatPerusahaan,
    required this.tipePerusahaan,
    required this.arrLokasi,
    required this.link,
    required this.linkPerusahaan,
    required this.descPerusahaan,
    required this.kk,
    required this.kb,
    required this.km,
    required this.kbdu,
    required this.rlp,
    required this.expairedDate,
    this.checkLowongan,
  });

  factory LowonganModel.fromJson(Map<String, dynamic> json) {
    return LowonganModel(
        id: json['id'].toString(),
        idReq: json['id_req'].toString(),
        perusahaan: json['perusahaan'] ?? '',
        department: json['department'] ?? '',
        posisi: json['posisi'] ?? '',
        atasan: json['atasan'] ?? '',
        bawahan: json['bawahan'] ?? '',
        jmlhKandidat: json['jmlh_kandidat'] ?? '',
        salary: json['salary'] ?? '',
        lokasiKerja: json['lokasi_kerja'] ?? '',
        sistemKerja: json['sistem_kerja'] ?? '',
        lamaKerja: json['lama_kerja'] ?? '',
        kPendidikan: json['k_pendidikan'] ?? '',
        kUsia: json['k_usia'] ?? '',
        kSim: json['k_sim'] ?? '',
        kJk: json['k_jk'] ?? '',
        kPengalaman: json['k_pengalaman'] ?? '',
        kPengalamanSama: json['k_pengalaman_sama'] ?? '',
        kStatus: json['k_status'] ?? '',
        kKhusus: json['k_khusus'] ?? '',
        note: json['note'] ?? '',
        status: json['status'] ?? '',
        createAt: json['create_at'] ?? '',
        updateAt: json['update_at'] ?? '',
        ketUpdate: json['ket_update'] ?? '',
        idKoordinator: json['id_koordinator'] ?? '',
        kJurusan: json['k_jurusan'] ?? '',
        divisi: json['divisi'] ?? '',
        kSekolah: json['k_sekolah'] ?? '',
        tipePekerjaan: json['tipe_pekerjaan'] ?? '',
        lokasiKerja2: json['lokasi_kerja2'] ?? '',
        waktuLalu: json['waktu_lalu'] ?? '',
        tglPosting: json['tgl_posting'] ?? '',
        logoURL: json['logoURL'] ?? '',
        isFavorit: json['is_favorit'] ?? false,
        isLamar: json['is_lamar'] ?? false,
        tempKode: json['temp_kode'] ?? '',
        alamatPerusahaan: json['alamat'] ?? '',
        tipePerusahaan: json['tipe'] ?? '',
        arrLokasi: json['arr_lokasi'] ?? [],
        link: json['link'] ?? '',
        linkPerusahaan: json['link_perusahaan'] ?? '',
        descPerusahaan: json['deskripsi'] ?? '',
        kk: json['kk'] ?? '',
        kb: json['kb'] ?? '',
        km: json['km'] ?? '',
        kbdu: json['kbdu'] ?? '',
        rlp: json['rlp'] ?? '',
        checkLowongan: json['check_lowongan'] ?? '',
        expairedDate: json['expired_date'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'id_req': idReq,
      'perusahaan': perusahaan,
      'department': department,
      'posisi': posisi,
      'atasan': atasan,
      'bawahan': bawahan,
      'jmlh_kandidat': jmlhKandidat,
      'salary': salary,
      'lokasi_kerja': lokasiKerja,
      'sistem_kerja': sistemKerja,
      'lama_kerja': lamaKerja,
      'k_pendidikan': kPendidikan,
      'k_usia': kUsia,
      'k_sim': kSim,
      'k_jk': kJk,
      'k_pengalaman': kPengalaman,
      'k_pengalaman_sama': kPengalamanSama,
      'k_status': kStatus,
      'k_khusus': kKhusus,
      'note': note,
      'status': status,
      'create_at': createAt,
      'update_at': updateAt,
      'ket_update': ketUpdate,
      'id_koordinator': idKoordinator,
      'k_jurusan': kJurusan,
      'divisi': divisi,
      'k_sekolah': kSekolah,
      'tipe_pekerjaan': tipePekerjaan,
      'lokasi_kerja2': lokasiKerja2,
      'waktu_lalu': waktuLalu,
      'tgl_posting': tglPosting,
      'logoURL': logoURL,
      'is_favorit': isFavorit,
      'is_lamar': isLamar,
      'temp_kode': tempKode,
      'alamat': alamatPerusahaan,
      'tipe': tipePerusahaan,
      'arr_lokasi': arrLokasi,
      'link': link,
      'link_perusahaan': linkPerusahaan,
      'deksripsi': descPerusahaan,
      'kk': kk,
      'kb': kb,
      'km': km,
      'kbdu': kbdu,
      'rlp': rlp,
      'check_lowongan': checkLowongan,
    };
  }
}
