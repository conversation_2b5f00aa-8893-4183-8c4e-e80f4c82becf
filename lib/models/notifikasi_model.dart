class NotifikasiModel {
  final String logoUrl;
  final String label;
  final String isi;
  final String waktu;
  final String link;
  final String penerima;
  final String pengirim;
  final String tglKirim;
  String status;
  final String idReq;

  NotifikasiModel({
    required this.logoUrl,
    required this.label,
    required this.isi,
    required this.waktu,
    required this.link,
    required this.penerima,
    required this.pengirim,
    required this.tglKirim,
    required this.status,
    required this.idReq,
  });

  factory NotifikasiModel.fromJson(Map<String, dynamic> json) {
    return NotifikasiModel(
      logoUrl: json['logo_url'] ?? '',
      label: json['label'] ?? '',
      isi: json['isi'] ?? '',
      waktu: json['waktu'] ?? '',
      link: json['link'] ?? '',
      penerima: json['penerima'] ?? '',
      pengirim: json['pengirim'] ?? '',
      tglKirim: json['tgl_kirim'] ?? '',
      status: json['status'] ?? '',
      idReq: json['id_req'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logo_url': logoUrl,
      'label': label,
      'isi': isi,
      'waktu': waktu,
      'link': link,
      'penerima': penerima,
      'pengirim': pengirim,
      'tgl_kirim': tglKirim,
      'status': status,
      'id_req': idReq,
    };
  }

  @override
  String toString() => label;
}
