class OrganisasiModel {
  final String id;
  final String nama;
  final String jabatan;
  final String tempat;
  final String tahun;
  OrganisasiModel({
    required this.id,
    required this.nama,
    required this.jabatan,
    required this.tempat,
    required this.tahun,
  });

  factory OrganisasiModel.fromJson(Map<String, dynamic> json) {
    return OrganisasiModel(
      id: json['no'].toString(),
      nama: json['nama'] ?? '',
      jabatan: json['jabatan'] ?? '',
      tempat: json['tempat'] ?? '',
      tahun: json['tahun'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'no': id,
      'nama': nama,
      'jabatan': jabatan,
      'tempat': tempat,
      'tahun': tahun,
    };
  }

  @override
  String toString() => nama;
}
