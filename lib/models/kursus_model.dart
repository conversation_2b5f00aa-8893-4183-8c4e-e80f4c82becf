class KursusModel {
  final String id;
  final String nama;
  final String tempat;
  final String sertifikat;
  final String tglMulai;
  final String tglSelesai;

  KursusModel({
    required this.id,
    required this.nama,
    required this.tempat,
    required this.sertifikat,
    required this.tglMulai,
    required this.tglSelesai,
  });

  factory KursusModel.fromJson(Map<String, dynamic> json) {
    return KursusModel(
      id: json['no'].toString(),
      nama: json['nama'] ?? '',
      tempat: json['tempat'] ?? '',
      sertifikat: json['sertifikat'] ?? '',
      tglMulai: json['tgl_mulai'] ?? '',
      tglSelesai: json['tgl_selesai'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'no': id,
      'nama': nama,
      'tempat': tempat,
      'sertifikat': sertifikat,
      'tgl_mulai': tglMulai,
      'tgl_selesai': tglSelesai,
    };
  }

  @override
  String toString() => nama;
}
