class PendidikanModel {
  final String id;
  final String jenjang;
  final String namaSekolah;
  final String tahunMulai;
  final String tahunSelesai;
  final String jurusan;
  final String ket;

  PendidikanModel({
    required this.id,
    required this.jenjang,
    required this.namaSek<PERSON>h,
    required this.tahun<PERSON>ulai,
    required this.tahunSelesai,
    required this.jurus<PERSON>,
    required this.ket,
  });

  factory PendidikanModel.fromJson(Map<String, dynamic> json) {
    return PendidikanModel(
      id: json['no'].toString(),
      jenjang: json['jenjang'] ?? '',
      namaSekolah: json['nama_sekolah'] ?? '',
      tahunMulai: json['tahun_mulai'] ?? '',
      tahunSelesai: json['tahun_selesai'] ?? '',
      jurusan: json['jurusan'] ?? '',
      ket: json['ket'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'no': id,
      'jenjang': jenjang,
      'nama_sekolah': nama<PERSON><PERSON><PERSON>h,
      'tahun_mulai': tahun<PERSON>ulai,
      'tahun_selesai': tahun<PERSON><PERSON><PERSON>,
      'jurusan': jurusan,
      'ket': ket,
    };
  }

  @override
  String toString() => namaSekolah;
}
