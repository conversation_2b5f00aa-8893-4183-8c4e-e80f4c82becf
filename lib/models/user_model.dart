class UserModel {
  final String pin;
  final String nama;
  final String email;
  final String image;
  final String noTelp;
  final String tempatLahir;
  final String tglLahir;
  final String jk;
  final String alamat;
  final String accessToken;
  final String visibility;
  final String statusPekerjaan;
  final String refreshToken;

  UserModel(
      {required this.pin,
      required this.nama,
      required this.email,
      required this.image,
      required this.noTelp,
      required this.tempatLahir,
      required this.tglLahir,
      required this.jk,
      required this.alamat,
      required this.visibility,
      required this.statusPekerjaan,
      required this.accessToken,
      required this.refreshToken});

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      pin: json['pin'] ?? '',
      nama: json['nama'] ?? '',
      email: json['email'] ?? '',
      image: json['image'] ?? '',
      noTelp: json['no_telp'] ?? '',
      tempatLahir: json['tempat_lahir'] ?? '',
      tglLahir: json['tgl_lahir'] ?? '',
      jk: json['jenis_kelamin'] ?? '',
      alamat: json['alamat'] ?? '',
      visibility: json['visibilitas'] ?? '',
      statusPekerjaan: json['status_pekerjaan'] ?? '',
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pin': pin,
      'nama': nama,
      'email': email,
      'image': image,
      'no_telp': noTelp,
      'tempat_lahir': tempatLahir,
      'tgl_lahir': tglLahir,
      'jenis_kelamin': jk,
      'alamat': alamat,
      'access_token': accessToken,
      'visibility': visibility,
      'status_pekerjaan': statusPekerjaan,
      'refresh_token': refreshToken,
    };
  }

  @override
  String toString() => nama;
}
