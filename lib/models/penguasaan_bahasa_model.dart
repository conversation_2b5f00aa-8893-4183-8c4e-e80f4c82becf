class PenguasaanBahasaModel {
  final String id;
  final String bahasa;
  final String membaca;
  final String menulis;
  final String mendengar;
  final String berbicara;

  PenguasaanBahasaModel({
    required this.id,
    required this.bahasa,
    required this.membaca,
    required this.menulis,
    required this.mendengar,
    required this.berbicara,
  });

  factory PenguasaanBahasaModel.fromJson(Map<String, dynamic> json) {
    return PenguasaanBahasaModel(
      id: json['id'].toString(),
      bahasa: json['bahasa'] ?? '',
      membaca: json['membaca'] ?? '',
      menulis: json['menulis'] ?? '',
      mendengar: json['mendengar'] ?? '',
      berbicara: json['berbicara'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bahasa': bahasa,
      'membaca': membaca,
      'menulis': menulis,
      'mendengar': mendengar,
      'berbicara': berbicara,
    };
  }
}
