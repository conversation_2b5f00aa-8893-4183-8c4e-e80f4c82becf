class BahasaModel {
  final String id;
  final String text;

  BahasaModel({
    required this.id,
    required this.text,
  });

  factory BahasaModel.fromJson(Map<String, dynamic> json) {
    return BahasaModel(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
    };
  }

  @override
  String toString() => text;
}
