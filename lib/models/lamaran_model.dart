class LamaranModel {
  final String idLamar;
  final String idReq;
  final String perusahaan;
  final String posisi;
  final String lokasi;
  final String tgl;
  final String status;
  final String idKoordinator;
  final String image;
  final String lastStatus;
  final String waktuLalu;
  final int notifChatRef;
  final String isPaketChat;

  LamaranModel({
    required this.idLamar,
    required this.idReq,
    required this.perusahaan,
    required this.posisi,
    required this.lokasi,
    required this.tgl,
    required this.status,
    required this.idKoordinator,
    required this.image,
    required this.lastStatus,
    required this.waktuLalu,
    required this.notifChatRef,
    required this.isPaketChat,
  });

  factory LamaranModel.fromJson(Map<String, dynamic> json) {
    return LamaranModel(
      idLamar: json['id_lamar'].toString(),
      idReq: json['id_req'].toString(),
      perusahaan: json['perusahaan'] ?? '',
      posisi: json['posisi'] ?? '',
      lokasi: json['lokasi'] ?? '',
      tgl: json['tgl'] ?? '',
      status: json['status'] ?? '',
      idKoordinator: json['id_koordinator'] ?? '',
      image: json['logoURL'] ?? '',
      lastStatus: json['last_status'] ?? '',
      waktuLalu: json['waktu_lalu'] ?? '',
      notifChatRef: json['notif_chat_ref'] ?? 0,
      isPaketChat: json['is_paket_chat'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id_lamar': idLamar,
      'id_req': idReq,
      'perusahaan': perusahaan,
      'posisi': posisi,
      'lokasi': lokasi,
      'tgl': tgl,
      'status': status,
      'id_koordinator': idKoordinator,
      'logoURL': image,
      'last_status': lastStatus,
      'waktu_lalu': waktuLalu,
      'notif_chat_ref': notifChatRef,
      'is_paket_chat': isPaketChat,
    };
  }
}
