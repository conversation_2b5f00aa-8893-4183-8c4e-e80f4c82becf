class PekerjaanModel {
  final String id;
  final String namaPerusahaan;
  final String jabatan;
  final String statusKerja;
  final String gaji;
  final String tahunMulai;
  final String tahunSelesai;
  final String alasanBerhenti;

  PekerjaanModel({
    required this.id,
    required this.namaPerusahaan,
    required this.jabatan,
    required this.statusKerja,
    required this.gaji,
    required this.tahunMulai,
    required this.tahunSelesai,
    required this.alasanBerhenti,
  });

  factory PekerjaanModel.fromJson(Map<String, dynamic> json) {
    return PekerjaanModel(
      id: json['no'].toString(),
      namaPerusahaan: json['nama_perusahaan'] ?? '',
      jabatan: json['jabatan'] ?? '',
      statusKerja: json['status_kerja'] ?? '',
      gaji: json['gaji'] ?? '',
      tahunMulai: json['tahun_mulai'] ?? '',
      tahunSelesai: json['tahun_selesai'] ?? '',
      alasanBerhenti: json['alasan_berhenti'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'no': id,
      'nama_perusahaan': namaPerusahaan,
      'jabatan': jabatan,
      'status_kerja': statusKerja,
      'gaji': gaji,
      'tahun_mulai': tahunMulai,
      'tahun_selesai': tahunSelesai,
      'alasan_berhenti': alasanBerhenti,
    };
  }

  @override
  String toString() => namaPerusahaan;
}
