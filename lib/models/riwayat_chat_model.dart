import 'package:equatable/equatable.dart';
import 'package:digital_cv_mobile/models/chat_message_model.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';

class RiwayatChatModel extends Equatable {
  RiwayatChatModel({
    this.id,
    this.tanggal,
    this.pesan,
    this.jam,
    this.dari,
    this.timestamp,
  });

  final String? id;
  final DateTime? tanggal;
  final String? pesan;
  final String? jam;
  final String? dari;
  final DateTime? timestamp;

  factory RiwayatChatModel.fromJson(Map<String, dynamic> json) {
    return RiwayatChatModel(
      id: json["id"]?.toString() ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      tanggal: DateTime.tryParse(json["tanggal"] ?? ""),
      pesan: json["pesan"] ?? "",
      jam: json["jam"] ?? "",
      dari: json["dari"] ?? "",
      timestamp: _buildTimestampFromDateAndTime(
          json["tanggal"], json["jam"], json["timestamp"]),
    );
  }

  /// Parse timestamp from various formats
  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      if (timestamp is String) {
        if (timestamp.isEmpty) return null;

        // Try parsing as ISO string first
        try {
          final parsed = DateTime.parse(timestamp);
          // Ensure we have a valid date
          if (parsed.year > 1970 && parsed.year < 2100) {
            return parsed;
          }
        } catch (_) {
          // If ISO parsing fails, try parsing as milliseconds string
          final millis = int.tryParse(timestamp);
          if (millis != null) {
            return _parseTimestampFromInt(millis);
          }
        }
      } else if (timestamp is int) {
        return _parseTimestampFromInt(timestamp);
      } else if (timestamp is double) {
        return _parseTimestampFromInt(timestamp.round());
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  /// Parse timestamp from integer (handles both seconds and milliseconds)
  static DateTime? _parseTimestampFromInt(int timestamp) {
    try {
      if (timestamp > 1000000000000) {
        // Looks like milliseconds (13+ digits)
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      } else if (timestamp > 1000000000) {
        // Looks like seconds (10+ digits)
        return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  /// Build timestamp from separate date and time fields
  static DateTime _buildTimestampFromDateAndTime(
      dynamic tanggal, dynamic jam, dynamic timestamp) {
    LogService.log.i(
        'Building timestamp from: tanggal=$tanggal, jam=$jam, timestamp=$timestamp');

    // First try to use existing timestamp if available
    final parsedTimestamp = _parseTimestamp(timestamp);
    if (parsedTimestamp != null) {
      LogService.log.i('Using existing timestamp: $parsedTimestamp');
      return parsedTimestamp;
    }

    // If no timestamp, try to combine tanggal and jam
    try {
      if (tanggal != null && jam != null) {
        final tanggalStr = tanggal.toString();
        final jamStr = jam.toString();

        if (tanggalStr.isNotEmpty && jamStr.isNotEmpty) {
          // Parse date
          DateTime? dateOnly;
          try {
            dateOnly = DateTime.parse(tanggalStr);
          } catch (e) {
            // Try different date formats
            try {
              // Try format: "2025-01-10"
              dateOnly = DateTime.parse(tanggalStr);
            } catch (e2) {
              // Try format: "10/01/2025" or "10-01-2025"
              final dateParts = tanggalStr.split(RegExp(r'[/-]'));
              if (dateParts.length == 3) {
                final day = int.tryParse(dateParts[0]);
                final month = int.tryParse(dateParts[1]);
                final year = int.tryParse(dateParts[2]);
                if (day != null && month != null && year != null) {
                  dateOnly = DateTime(year, month, day);
                }
              }
            }
          }

          if (dateOnly != null) {
            // Parse time
            final timeParts = jamStr.split(':');
            if (timeParts.length >= 2) {
              final hour = int.tryParse(timeParts[0]) ?? 0;
              final minute = int.tryParse(timeParts[1]) ?? 0;
              final second =
                  timeParts.length > 2 ? (int.tryParse(timeParts[2]) ?? 0) : 0;

              final combinedDateTime = DateTime(
                dateOnly.year,
                dateOnly.month,
                dateOnly.day,
                hour,
                minute,
                second,
              );
              LogService.log.i('Combined date and time: $combinedDateTime');
              return combinedDateTime;
            } else {
              // If jam format is invalid, use date only
              return dateOnly;
            }
          }
        }
      }

      // If only tanggal is available
      if (tanggal != null) {
        final tanggalStr = tanggal.toString();
        if (tanggalStr.isNotEmpty) {
          try {
            return DateTime.parse(tanggalStr);
          } catch (e) {
            // Try different formats as above
          }
        }
      }
    } catch (e) {
      // If all parsing fails, return current time
    }

    // Fallback to current time
    final fallbackTime = DateTime.now();
    LogService.log.w('Using fallback time: $fallbackTime');
    return fallbackTime;
  }

  /// Convert to ChatMessage for display in chat UI
  ChatMessage toChatMessage() {
    return ChatMessage(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      sender: _mapSenderFromServer(dari),
      message: pesan ?? '',
      timestamp: timestamp ?? DateTime.now(),
    );
  }

  /// Map server sender values to consistent sender types
  String _mapSenderFromServer(String? serverSender) {
    if (serverSender == null) return 'company';

    switch (serverSender.toLowerCase()) {
      case 'user':
      case 'candidate':
      case 'pelamar':
        return 'candidate';
      case 'hrd':
      case 'company':
      case 'koordinator':
      case 'admin':
        return 'company';
      default:
        // If unknown, assume it's from company/HRD
        return 'company';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tanggal': tanggal?.toIso8601String(),
      'pesan': pesan,
      'jam': jam,
      'dari': dari,
      'timestamp': timestamp?.millisecondsSinceEpoch,
    };
  }

  @override
  List<Object?> get props => [
        id,
        tanggal,
        pesan,
        jam,
        dari,
        timestamp,
      ];
}
