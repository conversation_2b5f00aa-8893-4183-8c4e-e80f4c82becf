class ChatMessage {
  final String id;
  final String sender; // "candidate", "company", "hrd", "user"
  final String message;
  final DateTime timestamp;

  ChatMessage({
    required this.id,
    required this.sender,
    required this.message,
    required this.timestamp,
  });

  /// Check if message is from candidate (should appear on right)
  bool get isFromCandidate {
    return sender.toLowerCase() == 'candidate' ||
        sender.toLowerCase() == 'user';
  }

  /// Check if message is from company/HRD (should appear on left)
  bool get isFromCompany {
    return sender.toLowerCase() == 'company' ||
        sender.toLowerCase() == 'hrd' ||
        sender.toLowerCase() == 'koordinator';
  }

  /// Get display name for sender
  String get senderDisplayName {
    switch (sender.toLowerCase()) {
      case 'candidate':
      case 'user':
        return 'You';
      case 'company':
      case 'hrd':
      case 'koordinator':
        return 'HRD';
      default:
        return sender;
    }
  }
}
