import 'package:equatable/equatable.dart';

class TimelineModel extends Equatable {
    TimelineModel({
        required this.tahap,
        required this.tglTahap,
    });

    final String tahap;
    final String tglTahap;

    factory TimelineModel.fromJson(Map<String, dynamic> json){ 
        return TimelineModel(
            tahap: json["tahap"] ?? "",
            tglTahap: json["tgl_tahap"] ?? "",
        );
    }

    @override
    List<Object?> get props => [
    tahap, tglTahap, ];
}
