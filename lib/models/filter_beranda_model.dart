class FilterBerandaModel {
  List<String> jenis<PERSON>ekerjaan;
  List<String> spesialisasi;
  List<String> pendidikan;
  String? waktuPosting;

  FilterBerandaModel({
    List<String>? jenisPekerjaan,
    List<String>? spesialisasi,
    List<String>? pendidikan,
    this.waktuPosting,
  })  : jenisPekerjaan = jenisPekerjaan ?? [],
        spesialisasi = spesialisasi ?? [],
        pendidikan = pendidikan ?? [];

  factory FilterBerandaModel.fromJson(Map<String, dynamic> json) =>
      FilterBerandaModel(
        jenisPekerjaan:
            List<String>.from(json["jenis_pekerjaan"].map((x) => x)),
        spesialisasi: List<String>.from(json["spesialisasi"].map((x) => x)),
        pendidikan: List<String>.from(json["pendidikan"].map((x) => x)),
        waktuPosting: json["waktu_posting"],
      );

  Map<String, dynamic> toJson() => {
        "jenis_pekerjaan": j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        "spesialisasi": spesialisasi,
        "pendidikan": pendidikan,
        "waktu_posting": waktuPosting,
      };
}
