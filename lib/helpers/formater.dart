import 'package:intl/intl.dart';

String formatKhusus(String raw) {
  final items = raw.split('|');

  final cleanedItems = items.map((e) {
    // Hapus "1.'" atau "2.'" dst.
    return e.replaceAll(RegExp(r"\d+\.\'"), '').trim();
  }).toList();

  final result =
      List.generate(cleanedItems.length, (i) => "${i + 1}. ${cleanedItems[i]}");

  return result.join('\n');
}

String formatRupiah(String angka) {
  final formatter = NumberFormat("#,###", "id_ID");
  int nominal = int.tryParse(angka) ?? 0;
  return formatter.format(nominal);
}

String ubahFormatBulan(String input) {
  // Pecah input jadi bulan dan tahun
  final parts = input.split('-');
  if (parts.length != 2) return input;

  final bulan = int.tryParse(parts[0]);
  final tahun = parts[1];

  if (bulan == null || bulan < 1 || bulan > 12) return input;

  // Dapatkan nama bulan lokal (Indonesia)
  final namaBulan = DateFormat.MMMM('id_ID').format(DateTime(0, bulan));

  return '$namaBulan $tahun';
}

String ubahFormatDate(String input) {
  if (input == "" || input.isEmpty) {
    return input;
  }

  try {
    // Check if input is in YYYY-MM format (year-month only)
    if (RegExp(r'^\d{4}-\d{2}$').hasMatch(input)) {
      // For year-month format, add day as 01 to make it parseable
      DateTime date = DateTime.parse('$input-01');
      String formatted = DateFormat('MMM yyyy', 'id_ID').format(date);
      return formatted;
    }

    // For full date format
    DateTime date = DateTime.parse(input);
    String formatted = DateFormat('dd MMM yyyy', 'id_ID').format(date);
    return formatted;
  } catch (e) {
    // If parsing fails, return original input
    return input;
  }
}
