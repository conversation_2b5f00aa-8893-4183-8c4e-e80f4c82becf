import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AppTranslations extends Translations {
  static Map<String, dynamic> _id = {};
  static Map<String, dynamic> _en = {};
  static Map<String, dynamic> _cn = {};

  static Future<void> loadTranslations() async {
    final String idJson = await rootBundle.loadString('assets/lang/id-ID.json');
    final String enJson = await rootBundle.loadString('assets/lang/en-EN.json');
    final String cnJson = await rootBundle.loadString('assets/lang/cn-CN.json');

    _id = jsonDecode(idJson);
    _en = jsonDecode(enJson);
    _cn = jsonDecode(cnJson);
  }

  @override
  Map<String, Map<String, String>> get keys => {
        'id': _flattenMap(_id),
        'en': _flattenMap(_en),
        'cn': _flattenMap(_cn),
      };

  static Map<String, String> _flattenMap(Map<String, dynamic> map,
      [String prefix = '']) {
    Map<String, String> flatMap = {};
    map.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        flatMap.addAll(_flattenMap(value, '$prefix$key.'));
      } else {
        flatMap['$prefix$key'] = value.toString();
      }
    });
    return flatMap;
  }
}
