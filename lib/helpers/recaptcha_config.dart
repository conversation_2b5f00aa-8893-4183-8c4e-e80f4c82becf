import 'package:digital_cv_mobile/helpers/log_service.dart';

/// Configuration constants for reCAPTCHA v3 implementation
class RecaptchaConfig {
  // ✅ Site Key untuk production (ganti dengan site key Anda)
  static const String productionSiteKey =
      "6Le_GP4qAAAAABmlLz8R_CfXKDtwvdJeBUSJhiS7";

  // 🧪 Site Key untuk development/testing
  static const String developmentSiteKey =
      "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Test key dari Google

  // 🔐 Secret Key (harus disimpan di backend, bukan di frontend!)
  // Ini hanya contoh, dalam production secret key harus di server
  static const String secretKey = "YOUR_SECRET_KEY_HERE";

  // 🎯 Actions yang dapat digunakan
  static const String actionLogin = "login";
  static const String actionGoogleLogin = "google_login";
  static const String actionRegister = "register";
  static const String actionForgotPassword = "forgot_password";
  static const String actionSubmitForm = "submit_form";

  // ⚙️ Threshold configuration
  static const double humanScoreThreshold =
      0.7; // Increased minimum score untuk dianggap human
  static const int loginAttemptsBeforeRecaptcha =
      1; // Trigger reCAPTCHA after first failed attempt
  static const int googleLoginAttemptsBeforeRecaptcha =
      1; // Untuk Google login tetap ketat

  // 🕒 Timeout configuration
  static const int recaptchaTimeoutSeconds = 30;

  // 🌍 Environment detection
  static bool get isProduction {
    // Deteksi apakah ini production atau development
    // Anda bisa customize logic ini sesuai kebutuhan
    return const bool.fromEnvironment('dart.vm.product');
  }

  // 🔑 Get current site key based on environment
  static String get currentSiteKey {
    return isProduction ? productionSiteKey : developmentSiteKey;
  }

  // 📊 Logging configuration
  static const bool enableRecaptchaLogging = true;

  // 🎨 UI Configuration
  static const String verificationTitle = "🛡️ Verifikasi Keamanan";
  static const String verificationMessage =
      "Kami terdeteksi aktivitas yang tidak biasa. Mohon lakukan verifikasi untuk melanjutkan.";
  static const String verificationProcessingMessage =
      "Memverifikasi keamanan...";
  static const String verificationFailedMessage =
      "Verifikasi keamanan gagal. Silakan coba lagi.";
  static const String verificationRequiredMessage =
      "Verifikasi keamanan diperlukan untuk melanjutkan";
  static const String robotDetectedMessage =
      "Akses ditolak. Anda terdeteksi sebagai robot. Silakan coba lagi nanti.";

  // 🎲 Simulation settings (untuk development)
  static const bool enableRandomVerification =
      false; // Set true untuk simulasi random
  static const int randomVerificationPercentage =
      30; // 30% kemungkinan perlu verifikasi
}

/// Extension untuk memberikan method helper pada RecaptchaConfig
extension RecaptchaConfigHelper on RecaptchaConfig {
  /// Check if reCAPTCHA should be triggered based on login attempts
  static bool shouldTriggerForLogin(int loginAttempts) {
    return loginAttempts >= RecaptchaConfig.loginAttemptsBeforeRecaptcha;
  }

  /// Check if reCAPTCHA should be triggered for Google login
  static bool shouldTriggerForGoogleLogin(int loginAttempts) {
    return loginAttempts >= RecaptchaConfig.googleLoginAttemptsBeforeRecaptcha;
  }

  /// Get appropriate action string based on login type
  static String getActionForLogin(bool isGoogleLogin) {
    return isGoogleLogin
        ? RecaptchaConfig.actionGoogleLogin
        : RecaptchaConfig.actionLogin;
  }

  /// Check if a score indicates human behavior
  static bool isHumanScore(double score) {
    return score >= RecaptchaConfig.humanScoreThreshold;
  }

  /// Log reCAPTCHA events if logging is enabled
  static void logEvent(String message) {
    if (RecaptchaConfig.enableRecaptchaLogging) {
      LogService.log.i("🔒 reCAPTCHA: $message");
    }
  }
}
