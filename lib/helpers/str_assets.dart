import 'dart:ui';

import 'package:get/get.dart';
import 'package:translator/translator.dart';

class StrAssets {
  static String tncPengenalan = 'tnc.tncPengenalan';
  static String tncLamaData = 'tnc.tncLamaData';
  static String tncLayananWebLain = 'tnc.tncLayananWebLain';
  static String tncPerubahan = 'tnc.tncPerubahan';
  static String tncForceMajeur1 = 'tnc.tncForceMajeur1';
  static String tncForceMajeur6 = 'tnc.tncForceMajeur6';
  static String tncHukuman = 'tnc.tncHukuman';
  static String tncKontak = 'tnc.tncKontak';

  static List<String> listInfoUmum = [
    "tnc.tncInfoUmum1",
    'tnc.tncInfoUmum2',
    'tnc.tncInfoUmum3',
  ];
  static List<String> listKetLayanan = [
    'tnc.tncKetentuanLayanan1',
    'tnc.tncKetentuanLayanan2',
    'tnc.tncKetentuanLayanan3',
  ];

  static List<String> listInfoPribadi = [
    'tnc.tncDataPribadi1',
    'tnc.tncDataPribadi2',
    'tnc.tncDataPribadi3',
    'tnc.tncDataPribadi4',
    'tnc.tncDataPribadi5',
  ];

  static List<String> listKelolaData = [
    'tnc.tncKelolaData1',
    'tnc.tncKelolaData2',
    'tnc.tncKelolaData3',
    'tnc.tncKelolaData4',
    'tnc.tncKelolaData5',
    'tnc.tncKelolaData6',
    'tnc.tncKelolaData7',
    'tnc.tncKelolaData8',
    'tnc.tncKelolaData9',
    'tnc.tncKelolaData10',
    'tnc.tncKelolaData11',
  ];

  static List<String> listPersetujuan = [
    'tnc.tncPersetujuan1',
    'tnc.tncPersetujuan2',
    'tnc.tncPersetujuan3',
    'tnc.tncPersetujuan4',
    'tnc.tncPersetujuan5',
    'tnc.tncPersetujuan6',
    'tnc.tncPersetujuan7',
  ];

  static List<String> listInfoPihakTiga = [
    'tnc.tncInfoTeknik1',
    'tnc.tncInfoTeknik2',
    'tnc.tncInfoTeknik3',
  ];

  static List<String> listKeamananData = [
    'tnc.tncKeamananDat1',
    'tnc.tncKeamananDat2',
    'tnc.tncKeamananDat3',
    'tnc.tncKeamananDat4',
    'tnc.tncKeamananDat5',
  ];

  static List<String> listForceMajeur = [
    'tnc.tncForceMajeur2',
    'tnc.tncForceMajeur3',
    'tnc.tncForceMajeur4',
    'tnc.tncForceMajeur5',
  ];
}

bool isValidImageUrl(String url) {
  if (url.isEmpty) return false;
  final uri = Uri.tryParse(url);
  return uri != null &&
      uri.hasAbsolutePath &&
      (uri.isScheme("http") || uri.isScheme("https"));
}

List<String> translateToIndonesian(List<String> keys) {
  final currentLocale = Get.locale;
  Get.updateLocale(const Locale('id')); // Pakai ID sementara

  final translated = keys.map((key) => key.tr).toList();

  Get.updateLocale(currentLocale!); // Kembalikan locale
  return translated;
}

String? translateKey(String? key) {
  if (key == null) return null;
  final currentLocale = Get.locale;
  Get.updateLocale(const Locale('id'));
  final translated = key.tr;
  Get.updateLocale(currentLocale!);
  return translated;
}

Future<String?> translatorString(String entry) async {
  final translator = GoogleTranslator();
  final currentLocale = Get.locale;
  String result = "";
  if (entry.isNotEmpty || entry != "") {
    if (currentLocale?.languageCode.toLowerCase() == "en") {
      var translation = await translator.translate(entry, from: 'id', to: 'en');
      result = translation.text;
    } else {
      result = entry;
    }
  }
  return result;
}
