import 'package:dio/dio.dart';

/// Interceptor untuk menghapus/menyembunyikan informasi sensitif dari headers
class PrivacyHeaderInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Menghapus atau mengubah User-Agent default
    options.headers.remove('User-Agent');

    // Atau jika ingin menggantinya dengan custom value:
    options.headers['User-Agent'] = 'DigitalCV Kandidat';

    // Menghapus header lain yang mungkin membocorkan informasi
    options.headers.remove('X-Powered-By');
    options.headers.remove('Server');

    // Menambahkan headers privacy-friendly jika diperlukan
    options.headers['X-Requested-With'] = 'XMLHttpRequest';
    options.headers['X-Powered-By'] = 'DigitalCV Kandidat';

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Menghapus headers sensitif dari response
    response.headers.map.remove('server');
    response.headers.map.remove('x-powered-by');
    response.headers.map.remove('x-frame-options');

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Menghapus informasi sensitif dari error response headers
    if (err.response != null) {
      err.response!.headers.map.remove('server');
      err.response!.headers.map.remove('x-powered-by');
    }

    super.onError(err, handler);
  }
}

/// Helper untuk membuat Dio instance dengan privacy interceptor
class DioHelper {
  static Dio createDioInstance({
    required String baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) {
    final dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout ?? const Duration(minutes: 10),
      receiveTimeout: receiveTimeout ?? const Duration(minutes: 10),
      responseType: ResponseType.json,
      validateStatus: (status) {
        return status != null && status < 500;
      },
      // Menghapus User-Agent default dari BaseOptions
      headers: {
        // Tidak menambahkan User-Agent atau menggunakan custom
        'Accept': 'application/json',
      },
    ));

    // Menambahkan interceptor privacy
    dio.interceptors.add(PrivacyHeaderInterceptor());

    // Menambahkan logging interceptor untuk development (opsional)
    // dio.interceptors.add(LogInterceptor(
    //   requestBody: true,
    //   responseBody: true,
    //   requestHeader: false, // Tidak log headers untuk privacy
    //   responseHeader: false, // Tidak log headers untuk privacy
    // ));

    return dio;
  }
}
