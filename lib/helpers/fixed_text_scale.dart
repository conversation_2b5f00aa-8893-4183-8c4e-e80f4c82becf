// Contoh penggunaan wrapper untuk mencegah text scaling
// Tambahkan di bagian atas file dart yang memerlukan

import 'package:flutter/material.dart';

// Mixin untuk memudahkan penggunaan di berbagai screen
mixin NoTextScaleMixin {
  /// Wrap widget dengan MediaQuery untuk mencegah text scaling
  Widget noTextScale(Widget child) {
    return Builder(
      builder: (context) => MediaQuery(
        data: MediaQuery.of(context).copyWith(
          textScaler: TextScaler.linear(1.0),
        ),
        child: child,
      ),
    );
  }

  /// TextStyle yang tidak terpengaruh scaling
  TextStyle fixedTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    TextDecoration? decoration,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      decoration: decoration,
      height: height,
      fontFamily: 'Poppins',
      inherit: false, // Penting: jangan inherit dari parent
    );
  }
}

// Widget text yang sudah fix scaling
class FixedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? softWrap;

  const FixedText(
    this.text, {
    Key? key,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.softWrap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        textScaler: TextScaler.linear(1.0),
      ),
      child: Text(
        text,
        style: (style ?? const TextStyle()).copyWith(
          inherit: false,
          fontFamily: style?.fontFamily ?? 'Poppins',
        ),
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
        softWrap: softWrap,
      ),
    );
  }
}

// Widget untuk wrap seluruh screen
class FixedScaleScreen extends StatelessWidget {
  final Widget child;
  final double textScaleFactor;

  const FixedScaleScreen({
    Key? key,
    required this.child,
    this.textScaleFactor = 1.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        textScaler: TextScaler.linear(textScaleFactor),
      ),
      child: child,
    );
  }
}
