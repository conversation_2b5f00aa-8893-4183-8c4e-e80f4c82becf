import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';

class MyHttpOverrides extends HttpOverrides {
  static const String _pinnedSHA256 =
      'drJ7gKWAJ9w88dpo2sFwEO2TmX0LYD4vrb6FASSTtac=';
  static const String _pinnedSHA2562 =
      "KGibMOTDBqq1OwJ7KeNq1t0dz0uVOZRILKhL3B7KyZY=";

  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context ?? SecurityContext());

    client.badCertificateCallback =
        (X509Certificate cert, String host, int port) {
      try {
        // Allow localhost connections for development/testing
        if (host == 'localhost' ||
            host == '127.0.0.1' ||
            host == '::1' ||
            host == 'gestaltdev.digitalcv.id' ||
            host == 'digitalcv.id' ||
            host == 'play.google.com' ||
            host == 'dcv-umum-gestalt.s3.ap-southeast-3.amazonaws.com' ||
            host == 'itunes.apple.com') {
          LogService.log.i('Allowing localhost connection: $host:$port');
          return true;
        }

        // Allow local network connections (192.168.x.x, 10.x.x.x, etc.)
        if (host.startsWith('192.168.') ||
            host.startsWith('10.') ||
            host.startsWith('172.')) {
          LogService.log.i('Allowing local network connection: $host:$port');
          return true;
        }

        final Uint8List der = cert.der;
        final Digest digest = sha256.convert(der);
        final String sha256Fingerprint = base64.encode(digest.bytes);

        // LogService.log.i(
        //     "Server cert fingerprint: $sha256Fingerprint \n Expected fingerprint: $_pinnedSHA256");
        bool isValidCert = sha256Fingerprint == _pinnedSHA256 ||
            sha256Fingerprint == _pinnedSHA2562;

        if (!isValidCert) {
          LogService.log.w('Certificate validation failed for $host:$port');
          LogService.log.w('Received fingerprint: $sha256Fingerprint');
        }

        return isValidCert;
      } catch (e) {
        LogService.log.e('Error validating cert: $e');
        return false;
      }
    };

    return client;
  }
}
