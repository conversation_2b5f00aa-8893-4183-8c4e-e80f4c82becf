import 'package:flutter/material.dart';

/// Helper class untuk memastikan text tidak terpengaruh oleh accessibility text scaling
class TextHelper {
  /// Wrap widget Text untuk memastikan tidak terpengaruh sistem font scaling
  static Widget noScaleText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
    bool? softWrap,
  }) {
    return MediaQuery(
      data: MediaQueryData(textScaler: TextScaler.linear(1.0)),
      child: Text(
        text,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
        softWrap: softWrap,
      ),
    );
  }

  /// Wrap widget RichText untuk memastikan tidak terpengaruh sistem font scaling
  static Widget noScaleRichText(
    InlineSpan text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
    bool? softWrap,
    StrutStyle? strutStyle,
  }) {
    return MediaQuery(
      data: MediaQueryData(textScaler: TextScaler.linear(1.0)),
      child: RichText(
        text: text,
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.clip,
        textAlign: textAlign ?? TextAlign.start,
        softWrap: softWrap ?? true,
        strutStyle: strutStyle,
      ),
    );
  }

  /// Wrap widget TextField untuk memastikan tidak terpengaruh sistem font scaling
  static Widget noScaleTextField({
    required TextEditingController controller,
    String? hintText,
    TextStyle? style,
    InputDecoration? decoration,
    TextInputType? keyboardType,
    bool? readOnly,
    int? maxLines,
    int? minLines,
    VoidCallback? onTap,
    ValueChanged<String>? onChanged,
    String? Function(String?)? validator,
  }) {
    return MediaQuery(
      data: MediaQueryData(textScaler: TextScaler.linear(1.0)),
      child: TextFormField(
        controller: controller,
        style: style,
        decoration: decoration?.copyWith(hintText: hintText) ??
            InputDecoration(hintText: hintText),
        keyboardType: keyboardType,
        readOnly: readOnly ?? false,
        maxLines: maxLines,
        minLines: minLines,
        onTap: onTap,
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }

  /// Wrap widget DropdownButton untuk memastikan tidak terpengaruh sistem font scaling
  static Widget noScaleDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?>? onChanged,
    String? hint,
    TextStyle? style,
    bool isExpanded = false,
    bool isDense = false,
    Widget? icon,
    double? iconSize,
    InputDecoration? decoration,
  }) {
    return MediaQuery(
      data: MediaQueryData(textScaler: TextScaler.linear(1.0)),
      child: DropdownButtonFormField<T>(
        value: value,
        items: items,
        onChanged: onChanged,
        hint: hint != null ? Text(hint) : null,
        style: style,
        isExpanded: isExpanded,
        isDense: isDense,
        icon: icon,
        iconSize: iconSize ?? 24.0,
        decoration: decoration,
      ),
    );
  }

  /// Method untuk mendapatkan TextStyle yang tidak terpengaruh scaling
  static TextStyle getFixedTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    String? fontFamily,
    TextDecoration? decoration,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: fontFamily ?? 'Poppins',
      decoration: decoration,
      height: height,
      // Pastikan inherit false untuk tidak mengikuti parent scaling
      inherit: false,
    );
  }
}
