import 'package:get/get.dart';

class Utilities {
  bool isValidEmail(String email) {
    // Regex sederhana sesuai format umum email
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  final List<Map<String, String>> timelineData = [
    {
      "group": "dcv.info_pribadi.judul".tr,
    },
    {
      "group": "dcv.pendidikan.judul".tr,
    },
    {
      "group": "dcv.pelatihan.judul".tr,
    },
    {
      "group": "dcv.pengalaman_kerja.judul".tr,
    },
    {
      "group": "dcv.info_kerja.judul".tr,
    },
    {
      "group": "dcv.pengalaman_org.judul".tr,
    },
    {
      "group": "dcv.minat.judul".tr,
    },
  ];

  static const List<Map<String, String>> countryCodes = [
    {
      'code': '+62',
      'flag': '🇮🇩',
      'name': 'Indonesia',
    },
    {
      'code': '+1',
      'flag': '🇺🇸',
      'name': 'USA',
    },
    {
      'code': '+91',
      'flag': '🇮🇳',
      'name': 'India',
    },
    {
      'code': '+44',
      'flag': '🇬🇧',
      'name': 'United Kingdom',
    },
    {
      'code': '+81',
      'flag': '🇯🇵',
      'name': 'Japan',
    },
    {
      'code': '+86',
      'flag': '🇨🇳',
      'name': 'China',
    },
    {
      'code': '+49',
      'flag': '🇩🇪',
      'name': 'Germany',
    },
    {
      'code': '+33',
      'flag': '🇫🇷',
      'name': 'France',
    },
    {
      'code': '+61',
      'flag': '🇦🇺',
      'name': 'Australia',
    },
    {
      'code': '+82',
      'flag': '🇰🇷',
      'name': 'South Korea',
    },
    {
      'code': '+7',
      'flag': '🇷🇺',
      'name': 'Russia',
    },
    {
      'code': '+39',
      'flag': '🇮🇹',
      'name': 'Italy',
    },
    {
      'code': '+34',
      'flag': '🇪🇸',
      'name': 'Spain',
    },
    {
      'code': '+55',
      'flag': '🇧🇷',
      'name': 'Brazil',
    },
    {
      'code': '+41',
      'flag': '🇨🇭',
      'name': 'Switzerland',
    },
    {
      'code': '+31',
      'flag': '🇳🇱',
      'name': 'Netherlands',
    },
    {
      'code': '+46',
      'flag': '🇸🇪',
      'name': 'Sweden',
    },
    {
      'code': '+47',
      'flag': '🇳🇴',
      'name': 'Norway',
    },
    {
      'code': '+45',
      'flag': '🇩🇰',
      'name': 'Denmark',
    },
    {
      'code': '+64',
      'flag': '🇳🇿',
      'name': 'New Zealand',
    },
    {
      'code': '+351',
      'flag': '🇵🇹',
      'name': 'Portugal',
    },
    {
      'code': '+30',
      'flag': '🇬🇷',
      'name': 'Greece',
    },
    {
      'code': '+90',
      'flag': '🇹🇷',
      'name': 'Turkey',
    },
    {
      'code': '+966',
      'flag': '🇸🇦',
      'name': 'Saudi Arabia',
    },
    {
      'code': '+20',
      'flag': '🇪🇬',
      'name': 'Egypt',
    },
    {
      'code': '+234',
      'flag': '🇳🇬',
      'name': 'Nigeria',
    },
    {
      'code': '+63',
      'flag': '🇵🇭',
      'name': 'Philippines',
    },
    {
      'code': '+84',
      'flag': '🇻🇳',
      'name': 'Vietnam',
    },
    {
      'code': '+66',
      'flag': '🇹🇭',
      'name': 'Thailand',
    },
    {
      'code': '+65',
      'flag': '🇸🇬',
      'name': 'Singapore',
    },
    {
      'code': '+60',
      'flag': '🇲🇾',
      'name': 'Malaysia',
    },
    {
      'code': '+92',
      'flag': '🇵🇰',
      'name': 'Pakistan',
    },
    {
      'code': '+880',
      'flag': '🇧🇩',
      'name': 'Bangladesh',
    },
    {
      'code': '+972',
      'flag': '🇮🇱',
      'name': 'Israel',
    },
    {
      'code': '+27',
      'flag': '🇿🇦',
      'name': 'South Africa',
    },
    {
      'code': '+380',
      'flag': '🇺🇦',
      'name': 'Ukraine',
    },
    {
      'code': '+351',
      'flag': '🇵🇹',
      'name': 'Portugal',
    },
    {
      'code': '+43',
      'flag': '🇦🇹',
      'name': 'Austria',
    },
    {
      'code': '+48',
      'flag': '🇵🇱',
      'name': 'Poland',
    },
    {
      'code': '+420',
      'flag': '🇨🇿',
      'name': 'Czech Republic',
    },
    {
      'code': '+36',
      'flag': '🇭🇺',
      'name': 'Hungary',
    },
    {
      'code': '+386',
      'flag': '🇸🇮',
      'name': 'Slovenia',
    },
    {
      'code': '+421',
      'flag': '🇸🇰',
      'name': 'Slovakia',
    },
    {
      'code': '+40',
      'flag': '🇷🇴',
      'name': 'Romania',
    },
    {
      'code': '+353',
      'flag': '🇮🇪',
      'name': 'Ireland',
    },
    {
      'code': '+358',
      'flag': '🇫🇮',
      'name': 'Finland',
    },
    {
      'code': '+32',
      'flag': '🇧🇪',
      'name': 'Belgium',
    },
    {
      'code': '+52',
      'flag': '🇲🇽',
      'name': 'Mexico',
    },
    {
      'code': '+56',
      'flag': '🇨🇱',
      'name': 'Chile',
    },
    {
      'code': '+57',
      'flag': '🇨🇴',
      'name': 'Colombia',
    },
    {
      'code': '+54',
      'flag': '🇦🇷',
      'name': 'Argentina',
    },
    {
      'code': '+98',
      'flag': '🇮🇷',
      'name': 'Iran',
    },
    {
      'code': '+964',
      'flag': '🇮🇶',
      'name': 'Iraq',
    },
    {
      'code': '+971',
      'flag': '🇦🇪',
      'name': 'United Arab Emirates',
    },
    {
      'code': '+94',
      'flag': '🇱🇰',
      'name': 'Sri Lanka',
    },
    {
      'code': '+977',
      'flag': '🇳🇵',
      'name': 'Nepal',
    },
    {
      'code': '+856',
      'flag': '🇱🇦',
      'name': 'Laos',
    },
    {
      'code': '+855',
      'flag': '🇰🇭',
      'name': 'Cambodia',
    },
    {
      'code': '+95',
      'flag': '🇲🇲',
      'name': 'Myanmar',
    },
    {
      'code': '+880',
      'flag': '🇧🇩',
      'name': 'Bangladesh',
    },
    {
      'code': '+998',
      'flag': '🇺🇿',
      'name': 'Uzbekistan',
    },
    {
      'code': '+994',
      'flag': '🇦🇿',
      'name': 'Azerbaijan',
    },
    {
      'code': '+374',
      'flag': '🇦🇲',
      'name': 'Armenia',
    },
    {
      'code': '+373',
      'flag': '🇲🇩',
      'name': 'Moldova',
    },
    {
      'code': '+380',
      'flag': '🇺🇦',
      'name': 'Ukraine',
    },
    {
      'code': '+216',
      'flag': '🇹🇳',
      'name': 'Tunisia',
    },
    {
      'code': '+212',
      'flag': '🇲🇦',
      'name': 'Morocco',
    },
    {
      'code': '+213',
      'flag': '🇩🇿',
      'name': 'Algeria',
    },
    {
      'code': '+94',
      'flag': '🇱🇰',
      'name': 'Sri Lanka',
    },
    {
      'code': '+256',
      'flag': '🇺🇬',
      'name': 'Uganda',
    },
    {
      'code': '+254',
      'flag': '🇰🇪',
      'name': 'Kenya',
    },
    {
      'code': '+255',
      'flag': '🇹🇿',
      'name': 'Tanzania',
    },
    {
      'code': '+251',
      'flag': '🇪🇹',
      'name': 'Ethiopia',
    },
    {
      'code': '+94',
      'flag': '🇱🇰',
      'name': 'Sri Lanka',
    },
    {
      'code': '+977',
      'flag': '🇳🇵',
      'name': 'Nepal',
    },
    {
      'code': '+856',
      'flag': '🇱🇦',
      'name': 'Laos',
    },
    {
      'code': '+855',
      'flag': '🇰🇭',
      'name': 'Cambodia',
    },
    {
      'code': '+95',
      'flag': '🇲🇲',
      'name': 'Myanmar',
    },
    {
      'code': '+880',
      'flag': '🇧🇩',
      'name': 'Bangladesh',
    },
    {
      'code': '+998',
      'flag': '🇺🇿',
      'name': 'Uzbekistan',
    },
    {
      'code': '+994',
      'flag': '🇦🇿',
      'name': 'Azerbaijan',
    },
    {
      'code': '+374',
      'flag': '🇦🇲',
      'name': 'Armenia',
    },
    {
      'code': '+373',
      'flag': '🇲🇩',
      'name': 'Moldova',
    },
    {
      'code': '+380',
      'flag': '🇺🇦',
      'name': 'Ukraine',
    },
    {
      'code': '+216',
      'flag': '🇹🇳',
      'name': 'Tunisia',
    },
    {
      'code': '+212',
      'flag': '🇲🇦',
      'name': 'Morocco',
    },
    {
      'code': '+213',
      'flag': '🇩🇿',
      'name': 'Algeria',
    },
    {
      'code': '+94',
      'flag': '🇱🇰',
      'name': 'Sri Lanka',
    },
    {
      'code': '+256',
      'flag': '🇺🇬',
      'name': 'Uganda',
    },
    {
      'code': '+254',
      'flag': '🇰🇪',
      'name': 'Kenya',
    },
    {
      'code': '+255',
      'flag': '🇹🇿',
      'name': 'Tanzania',
    },
    {
      'code': '+251',
      'flag': '🇪🇹',
      'name': 'Ethiopia',
    },
    {
      'code': '+358',
      'flag': '🇫🇮',
      'name': 'Finland',
    },
    {
      'code': '+32',
      'flag': '🇧🇪',
      'name': 'Belgium',
    },
    {
      'code': '+52',
      'flag': '🇲🇽',
      'name': 'Mexico',
    },
    {
      'code': '+56',
      'flag': '🇨🇱',
      'name': 'Chile',
    },
    {
      'code': '+57',
      'flag': '🇨🇴',
      'name': 'Colombia',
    },
    {
      'code': '+54',
      'flag': '🇦🇷',
      'name': 'Argentina',
    },
    {
      'code': '+98',
      'flag': '🇮🇷',
      'name': 'Iran',
    },
    {
      'code': '+964',
      'flag': '🇮🇶',
      'name': 'Iraq',
    },
    {
      'code': '+971',
      'flag': '🇦🇪',
      'name': 'United Arab Emirates',
    },
  ];
}
