import 'dart:convert';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class TranslationService extends GetxService {
  /// Inisialisasi awal: load dua file JSON
  // Menyimpan seluruh map bahasa
  static final Map<String, Map<String, dynamic>> _langMap = {};

  /// Inisialisasi awal: load file JSON dari assets/lang/
  static Future<void> init() async {
    final supportedLangs = ['id', 'en', 'cn'];

    for (String code in supportedLangs) {
      String fileName;

      switch (code) {
        case 'id':
          fileName = 'id-ID.json';
          break;
        case 'en':
          fileName = 'en-EN.json'; // pastikan file ini memang ada
          break;
        case 'cn':
          fileName = 'cn-CN.json';
          break;
        default:
          fileName = '$code.json'; // fallback jika perlu
      }

      try {
        final jsonStr = await rootBundle.loadString('assets/lang/$fileName');
        _langMap[code] = jsonDecode(jsonStr);
      } catch (e) {
        _langMap[code] = {}; // fallback kosong
      }
    }
  }

  /// Mengambil map bahasa berdasarkan kode (default kosong jika tidak ditemukan)
  static Map<String, dynamic> _getLangMap(String langCode) {
    return _langMap[langCode.toLowerCase()] ?? {};
  }

  static String translateBetweenLangs(
    String value,
    String groupKey,
    String fromLang,
    String toLang,
  ) {
    // Jika bahasa target adalah 'id' (bahasa default), kembalikan nilai asli
    final currentLocale = Get.locale?.languageCode.toLowerCase() ?? 'id';
    if (currentLocale == 'id') {
      return value;
    }

    final Map sourceGroup = _getNestedMap(_getLangMap(fromLang), groupKey);
    final Map targetGroup = _getNestedMap(_getLangMap(toLang), groupKey);

    try {
      String? matchedKey;
      sourceGroup.forEach((key, val) {
        if (val.toString().toLowerCase() == value.toLowerCase()) {
          matchedKey = key;
        }
      });

      if (matchedKey != null && targetGroup.containsKey(matchedKey)) {
        return targetGroup[matchedKey];
      }
    } catch (e) {
      LogService.log.e("Translation error: $e");
    }

    return value; // fallback jika gagal translasi
  }

  /// Fungsi bantu untuk mengambil nested map dari key string "dcv.minat.ruang_lingkup"
  static Map _getNestedMap(Map source, String keyPath) {
    List<String> keys = keyPath.split(".");
    dynamic result = source;

    for (String key in keys) {
      if (result is Map && result.containsKey(key)) {
        result = result[key];
      } else {
        return {};
      }
    }

    return result is Map ? result : {};
  }

  static String translateTimeAgo(String value) {
    final localeCode = Get.locale?.languageCode.toLowerCase() ?? 'id';

    if (localeCode == 'id') return value;

    final Map<String, Map<String, String>> timeTranslations = {
      'en': {
        "baru saja": "just now",
        "hari ini": "today",
        "detik": "seconds",
        "menit": "minutes",
        "jam": "hours",
        "hari": "days",
        "minggu": "weeks",
        "bulan": "months",
        "tahun": "years",
        "yang lalu": "ago",
      },
      'cn': {
        "baru saja": "刚刚",
        "hari ini": "今天",
        "detik": "秒",
        "menit": "分钟",
        "jam": "小时",
        "hari": "天",
        "minggu": "周",
        "bulan": "月",
        "tahun": "年",
        "yang lalu": "前",
      },
    };

    final translations = timeTranslations[localeCode];
    if (translations == null) return value;

    String result = value;
    translations.forEach((id, translated) {
      result = result.replaceAll(
        RegExp(r'\b' + RegExp.escape(id) + r'\b', caseSensitive: false),
        translated,
      );
    });

    return result;
  }

  static String translateDateString(String dateStr) {
    final localeCode = Get.locale?.languageCode.toLowerCase() ?? 'id';

    if (localeCode == 'id') return dateStr;

    final Map<String, Map<String, String>> monthTranslations = {
      'en': {
        "januari": "January",
        "februari": "February",
        "maret": "March",
        "april": "April",
        "mei": "May",
        "juni": "June",
        "juli": "July",
        "agustus": "August",
        "september": "September",
        "oktober": "October",
        "november": "November",
        "desember": "December",
      },
      'cn': {
        "januari": "一月",
        "februari": "二月",
        "maret": "三月",
        "april": "四月",
        "mei": "五月",
        "juni": "六月",
        "juli": "七月",
        "agustus": "八月",
        "september": "九月",
        "oktober": "十月",
        "november": "十一月",
        "desember": "十二月",
      },
    };

    final translations = monthTranslations[localeCode];
    if (translations == null) return dateStr;

    String result = dateStr;
    translations.forEach((id, translated) {
      result = result.replaceAllMapped(
        RegExp(r'\b' + id + r'\b', caseSensitive: false),
        (match) => translated,
      );
    });

    return result;
  }
}
