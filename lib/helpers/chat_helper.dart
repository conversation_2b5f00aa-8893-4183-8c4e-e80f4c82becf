import 'package:digital_cv_mobile/models/chat_message_model.dart';
import 'package:intl/intl.dart';

class ChatHelper {
  /// Groups chat messages by date
  /// Returns a map where key is the date string and value is list of messages for that date
  static Map<String, List<ChatMessage>> groupMessagesByDate(
      List<ChatMessage> messages) {
    Map<String, List<ChatMessage>> groupedMessages = {};

    for (var message in messages) {
      // Create date key (YYYY-MM-DD format)
      String dateKey = _getDateKey(message.timestamp);

      if (!groupedMessages.containsKey(dateKey)) {
        groupedMessages[dateKey] = [];
      }

      groupedMessages[dateKey]!.add(message);
    }

    // Sort messages within each date group by timestamp
    groupedMessages.forEach((date, messagesList) {
      messagesList.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    });

    return groupedMessages;
  }

  /// Converts grouped messages into a flat list with date separators
  /// This is used for ListView.builder
  static List<dynamic> createFlatListWithSeparators(
      Map<String, List<ChatMessage>> groupedMessages) {
    List<dynamic> flatList = [];

    // Sort dates in ascending order (oldest first, so today will be at bottom)
    var sortedDates = groupedMessages.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    for (String dateKey in sortedDates) {
      // Add date separator
      DateTime date = DateTime.parse(dateKey);
      flatList.add(date);

      // Add messages for this date in chronological order (oldest first within the day)
      List<ChatMessage> messagesForDate = groupedMessages[dateKey]!;
      messagesForDate.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      flatList.addAll(messagesForDate);
    }

    return flatList;
  }

  static String _getDateKey(DateTime dateTime) {
    // Normalize to start of day to ensure proper grouping
    final normalizedDate =
        DateTime(dateTime.year, dateTime.month, dateTime.day);
    return "${normalizedDate.year}-${normalizedDate.month.toString().padLeft(2, '0')}-${normalizedDate.day.toString().padLeft(2, '0')}";
  }

  /// Get formatted time for message display
  static String getMessageTime(DateTime timestamp) {
    return DateFormat('HH:mm', 'id_ID').format(timestamp);
  }

  /// Get relative date description
  static String getRelativeDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final inputDate = DateTime(date.year, date.month, date.day);
    final difference = today.difference(inputDate).inDays;

    if (difference == 0) {
      return "Hari Ini";
    } else if (difference == 1) {
      return 'Kemarin';
    } else if (difference < 7) {
      return DateFormat('EEEE', 'id_ID').format(date);
    } else if (date.year == now.year) {
      return DateFormat('d MMMM', 'id_ID').format(date);
    } else {
      return DateFormat('d MMMM yyyy', 'id_ID').format(date);
    }
  }

  /// Creates sample data for testing
  static List<ChatMessage> getSampleMessages() {
    // final now = DateTime.now();
    return [
      // Today's messages
      // ChatMessageModel(
      //   id: '1',
      //   message:
      //       'Halo, saya perlu bantuan untuk mencari kandidat untuk posisi Software Engineer',
      //   isFromUser: true,
      //   timestamp: now.subtract(Duration(hours: 2)),
      // ),
    ];
  }
}
