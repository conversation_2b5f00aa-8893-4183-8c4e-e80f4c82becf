import 'package:encrypt/encrypt.dart';

/// Helper class untuk keamanan password
/// Menyediakan fungsi hashing, salting, dan enkripsi password di sisi klien
class PasswordSecurityHelper {
  // Static key untuk enkripsi (dalam production, gunakan key yang lebih aman)
  static final _key =
      Key.fromBase64('vewCDRQayYKYw3LCN/DQdK1FegvPTMaduED2M5ibdnw=');
  static final _iv = IV.fromUtf8('e9256924d71fbcfa');
  static final _encrypter = Encrypter(AES(_key, mode: AESMode.cbc));

  /// Enkripsi password untuk transmisi ke server
  static String encryptPassword(String password) {
    final encrypted = _encrypter.encrypt(password, iv: _iv);
    return encrypted.base64;
  }

  /// Dekripsi password (untuk keperluan testing atau debugging)
  static String decryptPassword(String encryptedPassword) {
    final encrypted = Encrypted.fromBase64(encryptedPassword);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }
}
