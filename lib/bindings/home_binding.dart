import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/controllers/bottom_nav_controller.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:get/get.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<NavController>(() => NavController());
    Get.lazyPut<LoginController>(() => LoginController());
    Get.lazyPut<LowonganController>(() => LowonganController());
    Get.lazyPut<LamaranController>(() => LamaranController());
    Get.lazyPut<LocationController>(() => LocationController(), fenix: true);
  }
}
