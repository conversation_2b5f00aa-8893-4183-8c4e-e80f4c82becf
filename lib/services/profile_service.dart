import 'dart:io';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:http/http.dart' as http;

import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class ProfileService {
  late final HttpClient _httpApiClient;
  late final HttpClient _httpApiRootClient;
  final secureStorage = FlutterSecureStorage();

  ProfileService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
    _httpApiRootClient = HttpClient(baseUrl: rootUri);
  }

  Future<HttpResponse> ubahEmail(String email, String otp) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/profile.php", // Endpoint
        queryParameters: {"func": "updateProfile"},
        data: {"email": email, "otp": otp, "ket": "updateEmail"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      throw Exception("${"controller.server_error".tr}: $e");
    }
  }

  Future<HttpResponse> reqOTPUpdateEmail(String email, String noTelp) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiRootClient.post(
        "/sendOTP.php", // Endpoint
        data: {
          "email": email,
          "for": "candidate",
          "no_hp_pic": noTelp,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> ubahNoTelp(String noTelp, String pin) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/profile.php", // Endpoint
        queryParameters: {
          "func": "updateProfile",
        },
        data: {"no_telepon": noTelp, "ket": "updateNoTelepon"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> uploadPhoto(File imageFile, String pin) async {
    try {
      var token = await secureStorage.read(key: "access_token");

      // Prepare multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$api_uri/profile.php?func=uploadFotoProfil'),
      );

      request.headers.addAll({
        "Authorization": "Bearer $token",
      });

      // Add file
      String fileName = imageFile.path.split('/').last;
      request.files.add(
        await http.MultipartFile.fromPath('file', imageFile.path,
            filename: fileName),
      );

      // Send request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      return HttpResponse(
        statusCode: response.statusCode,
        data: HttpHelper.parseResponse(response),
        headers: response.headers,
      );
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getFotoProfil() async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("Fetching Foto Profil with token: $token");
      HttpResponse response = await _httpApiClient.get(
        "/profile.php",
        queryParameters: {
          "func": "getFotoProfil",
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getFotoProfil response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> ubahVisibilitas(String status) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/profile.php", // Endpoint
        queryParameters: {"func": "updateVisibilitas"},
        data: {"status": status},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      throw Exception("${"controller.server_error".tr}: $e");
    }
  }

  Future<HttpResponse> ubahStatusKerja(String status) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/profile.php", // Endpoint
        queryParameters: {"func": "updateStatusBekerja"},
        data: {"status": status},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      throw Exception("${"controller.server_error".tr}: $e");
    }
  }
}
