import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class ApplyService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  ApplyService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> applyLowongan(
    String idReq,
    String lokasi,
    List<String> pertanyaanKhusus,
    String pin,
  ) async {
    final Map<String, dynamic> requestData = {
      "id_req": idReq,
      "lokasi": lokasi,
    };

    if (pertanyaanKhusus.isNotEmpty) {
      for (int i = 0; i < pertanyaanKhusus.length; i++) {
        requestData["pertanyaan_khusus[$i]"] = pertanyaanKhusus[i];
      }
    }
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/apply_lowongan.php", // Endpoint untuk mendapatkan list job
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("applyLowongan response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
