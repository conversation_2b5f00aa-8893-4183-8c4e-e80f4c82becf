import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class GetInfoCVService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  GetInfoCVService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getRH(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getRH response: $token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getRH"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getRH response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getRiwayatPendidikan(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getRiwayatPendidikan"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getRiwayatKursus(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getRiwayatKursus"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getRiwayatPekerjaan(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getRiwayatPekerjaan"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getRiwayatOrganisasi(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getRiwayatOrganisasi"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getPenguasaanBahasa(
    String pin,
  ) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_cv.php", // Endpoint untuk mendapatkan list job
        queryParameters: {"func": "getPenguasaanBahasa"},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> postTentangSaya({required String tentangSaya}) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanTentangSaya"},
        data: {"tentang_saya": tentangSaya},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
