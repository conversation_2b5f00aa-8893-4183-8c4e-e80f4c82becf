import 'dart:async';
import 'dart:convert';
import 'package:digital_cv_mobile/controllers/chat_message_controller.dart';
import 'package:digital_cv_mobile/models/chat_message_model.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

enum WebSocketStatus { connecting, connected, disconnected, error }

class WebSocketService extends GetxService {
  WebSocketChannel? _channel;
  final Rx<WebSocketStatus> _status = WebSocketStatus.disconnected.obs;

  // Stream controller untuk incoming messages
  final StreamController<ChatMessage> _messageController =
      StreamController<ChatMessage>.broadcast();

  // Stream controller untuk chat notifications
  final StreamController<Map<String, dynamic>> _notificationController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Current user info
  String? _currentUserId;
  bool _autoConnectEnabled = false;

  // Getters
  WebSocketStatus get status => _status.value;
  Stream<ChatMessage> get messageStream => _messageController.stream;
  Stream<Map<String, dynamic>> get notificationStream =>
      _notificationController.stream;
  Rx<WebSocketStatus> get statusObservable => _status;
  bool get isConnected => _status.value == WebSocketStatus.connected;
  String? get currentUserId => _currentUserId;
  bool get autoConnectEnabled => _autoConnectEnabled;
  String get socketUrl => 'wss://digitalcv.id:443/ws';

  final secureStorage = Get.find<FlutterSecureStorage>();

  // WebSocket URL
  // static const String _wsUrl = 'ws://localhost:3000?userId=test-flutter-user';

  @override
  void onInit() {
    super.onInit();
    connect();
  }

  @override
  void onClose() {
    disconnect();
    _messageController.close();
    super.onClose();
  }

  /// Connect to WebSocket server with specific userId
  Future<void> connectWithUserId(String userId) async {
    if (_status.value == WebSocketStatus.connecting ||
        _status.value == WebSocketStatus.connected) {
      // If already connected with different userId, disconnect first
      if (_currentUserId != null && _currentUserId != userId) {
        LogService.log.i(
            'Switching user from $_currentUserId to $userId, disconnecting first');
        disconnect();
      } else if (_currentUserId == userId) {
        LogService.log.i('Already connected with userId: $userId');
        return;
      }
    }

    try {
      _status.value = WebSocketStatus.connecting;
      _currentUserId = userId;

      var url = '$socketUrl/?userId=$userId';
      LogService.log.i('Connecting to WebSocket with userId: $userId');
      LogService.log.i('WebSocket URL: $url');

      final uri = Uri.parse(url);
      _channel = WebSocketChannel.connect(uri);

      _status.value = WebSocketStatus.connected;
      LogService.log.i('✅ WebSocket connected successfully for user: $userId');

      // Listen for incoming messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
        cancelOnError: false,
      );

      // Send user identification message
      _sendUserIdentification(userId);
    } on TimeoutException catch (e) {
      LogService.log.e('❌ WebSocket connection timeout for user $userId: $e');
      _status.value = WebSocketStatus.error;
      _currentUserId = null;

      // Attempt automatic reconnection if auto-connect is enabled
      if (_autoConnectEnabled) {
        LogService.log.i('🔄 Scheduling reconnection attempt in 5 seconds...');
        Future.delayed(const Duration(seconds: 5), () {
          if (_autoConnectEnabled && _currentUserId == null) {
            LogService.log.i('🔄 Retrying connection for user: $userId');
            connectWithUserId(userId);
          }
        });
      }
    } catch (e) {
      LogService.log.e('❌ WebSocket connection error for user $userId: $e');
      LogService.log.e('Error type: ${e.runtimeType}');
      _status.value = WebSocketStatus.error;
      _currentUserId = null;
    }
  }

  /// Connect to WebSocket server (legacy method for backward compatibility)
  Future<void> connect() async {
    try {
      var pin = await secureStorage.read(key: 'pin');
      if (pin != null && pin.isNotEmpty) {
        await connectWithUserId(pin);
      } else {
        LogService.log.w('No userId found in secure storage, cannot connect');
      }
    } catch (e) {
      LogService.log.e('Error reading userId from secure storage: $e');
    }
  }

  /// Disconnect from WebSocket server
  void disconnect() {
    if (_channel != null) {
      // Send disconnect message before closing
      if (_currentUserId != null) {
        _sendDisconnectMessage(_currentUserId!);
      }

      _channel!.sink.close();
      _channel = null;
    }

    final previousUserId = _currentUserId;
    _currentUserId = null;
    _autoConnectEnabled = false;
    _status.value = WebSocketStatus.disconnected;

    LogService.log.i('🔌 WebSocket disconnected for user: $previousUserId');
  }

  /// Send user identification message to server
  void _sendUserIdentification(String userId) {
    try {
      final identificationData = {
        'type': 'user_identification',
        'data': {
          'userId': userId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'platform': 'flutter',
          'version': '1.0.0',
        }
      };

      _channel?.sink.add(jsonEncode(identificationData));
      LogService.log.i('👤 User identification sent for: $userId');
    } catch (e) {
      LogService.log.e('Error sending user identification: $e');
    }
  }

  /// Send disconnect message to server
  void _sendDisconnectMessage(String userId) {
    try {
      final disconnectData = {
        'type': 'user_disconnect',
        'data': {
          'userId': userId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }
      };

      _channel?.sink.add(jsonEncode(disconnectData));
      LogService.log.i('👋 Disconnect message sent for: $userId');
    } catch (e) {
      LogService.log.e('Error sending disconnect message: $e');
    }
  }

  /// Send a chat message
  void sendMessage(ChatMessage message) {
    if (_status.value != WebSocketStatus.connected || _channel == null) {
      LogService.log.w('Cannot send message: WebSocket not connected');
      return;
    }

    try {
      final messageData = {
        'type': 'chat_message',
        'data': {
          'id': message.id,
          'sender': message.sender,
          'message': message.message,
          'timestamp': message.timestamp.millisecondsSinceEpoch,
        }
      };

      _channel!.sink.add(jsonEncode(messageData));
      LogService.log.i('Message sent: ${message.message}');
    } catch (e) {
      LogService.log.e('Error sending message: $e');
    }
  }

  /// Handle incoming messages
  void _onMessage(dynamic data) {
    try {
      LogService.log.i('Raw WebSocket data received: $data');

      // Handle different data formats
      Map<String, dynamic> messageData;

      if (data is String) {
        try {
          messageData = jsonDecode(data);
        } catch (jsonError) {
          // If not JSON, treat as plain text message
          messageData = {
            'type': 'chat_message',
            'data': {
              'id': DateTime.now().millisecondsSinceEpoch.toString(),
              'sender': 'company',
              'message': data,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
            }
          };
        }
      } else {
        LogService.log.w('Unknown data format: ${data.runtimeType}');
        return;
      }

      // Handle different message types
      final messageType = messageData['type']?.toString() ?? 'chat_message';

      switch (messageType) {
        case 'chat_message':
          _handleChatMessage(messageData);
          break;
        case 'chat_notification':
          _handleChatNotification(messageData);
          break;
        case 'notification_update':
          _handleNotificationUpdate(messageData);
          break;
        default:
          _handleChatMessage(messageData); // Default to chat message
      }
    } catch (e) {
      LogService.log.e('Error parsing incoming message: $e');
    }
  }

  /// Handle regular chat messages
  void _handleChatMessage(Map<String, dynamic> messageData) {
    try {
      final chatData = messageData['data'] ?? messageData;
      final parsedTimestamp = _parseTimestamp(chatData['timestamp']);

      final message = ChatMessage(
        id: chatData['id']?.toString() ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        sender: _mapSenderFromWebSocket(chatData['sender']?.toString()),
        message: chatData['message']?.toString() ??
            chatData['text']?.toString() ??
            '',
        timestamp: parsedTimestamp,
      );

      LogService.log.i(
          'Broadcasting chat message: ${message.sender} - ${message.message}');
      _messageController.add(message);

      // If message is from company/HRD, also trigger notification update
      if (message.isFromCompany) {
        _triggerChatNotification(chatData, message);
      }
    } catch (e) {
      LogService.log.e('Error handling chat message: $e');
    }
  }

  /// Trigger chat notification for incoming company messages
  void _triggerChatNotification(
      Map<String, dynamic> chatData, ChatMessage message) {
    try {
      // Extract lamaran and koordinator info from chat data or message
      final idLamaran = chatData['id_lamaran']?.toString() ??
          chatData['id_lamar']?.toString() ??
          _getCurrentChatLamaran();
      final idKoordinator = chatData['id_koordinator']?.toString() ??
          _getCurrentChatKoordinator();

      if (idLamaran != null) {
        // Check if user is currently viewing this specific chat
        final isCurrentChatActive =
            _isCurrentChatActive(idLamaran, idKoordinator);

        if (isCurrentChatActive) {
          // If chat screen is open for this conversation, mark as read immediately
          LogService.log.i(
              '✅ Chat screen is active for lamaran: $idLamaran, marking message as read');

          // Send read receipt notification to clear any existing notifications
          final readNotificationData = {
            'type': 'chat_notification',
            'data': {
              'id_lamaran': idLamaran,
              'id_koordinator': idKoordinator,
              'notif_count': 0, // Clear notification count
              'message': message.message,
              'sender': message.sender,
              'timestamp': message.timestamp.millisecondsSinceEpoch,
              'is_chat_active': true,
              'action': 'mark_as_read', // Explicit action
            }
          };

          _notificationController.add(readNotificationData);

          // Also send a separate read receipt to ensure consistency
          final readReceiptData = {
            'type': 'notification_update',
            'data': {
              'id_lamaran': idLamaran,
              'id_koordinator': idKoordinator,
              'notif_count': 0,
              'action': 'auto_read',
            }
          };

          _notificationController.add(readReceiptData);
        } else {
          // Chat screen is not active, increment notification normally
          LogService.log.i(
              '📱 Chat screen not active for lamaran: $idLamaran, incrementing notification');

          final notificationData = {
            'type': 'chat_notification',
            'data': {
              'id_lamaran': idLamaran,
              'id_koordinator': idKoordinator,
              'notif_count': 1, // Increment by 1 for new message
              'message': message.message,
              'sender': message.sender,
              'timestamp': message.timestamp.millisecondsSinceEpoch,
              'is_chat_active': false,
              'action': 'new_message',
            }
          };

          _notificationController.add(notificationData);
        }
      } else {
        LogService.log
            .w('Cannot trigger notification: missing id_lamaran in chat data');
      }
    } catch (e) {
      LogService.log.e('Error triggering chat notification: $e');
    }
  }

  /// Get current chat lamaran ID from ChatMessageController
  String? _getCurrentChatLamaran() {
    try {
      final chatController = Get.find<ChatMessageController>();
      return chatController.currentIdLamaran;
    } catch (e) {
      return null;
    }
  }

  /// Get current chat koordinator ID from ChatMessageController
  String? _getCurrentChatKoordinator() {
    try {
      final chatController = Get.find<ChatMessageController>();
      return chatController.currentIdKoordinator;
    } catch (e) {
      return null;
    }
  }

  /// Check if the current chat screen is active for specific lamaran
  bool _isCurrentChatActive(String idLamaran, String? idKoordinator) {
    try {
      // First check if ChatMessageController exists and has matching parameters
      if (!Get.isRegistered<ChatMessageController>()) {
        LogService.log
            .i('ChatMessageController not registered, chat not active');
        return false;
      }

      final chatController = Get.find<ChatMessageController>();
      final currentLamaran = chatController.currentIdLamaran;
      final currentKoordinator = chatController.currentIdKoordinator;

      // Check if the chat parameters match exactly
      final isMatchingChat = currentLamaran != null &&
          currentLamaran == idLamaran &&
          currentKoordinator == idKoordinator;

      // Additional check: verify if we're actually on a chat-related route
      final currentRoute = Get.currentRoute;
      final isChatRoute = currentRoute.contains('ChatScreen') ||
          currentRoute.contains('/chat') ||
          currentRoute == '/ChatScreen';

      // Chat is active only if both conditions are met
      final isChatActive = isMatchingChat && isChatRoute;

      LogService.log.i('🔍 Chat active check:');
      LogService.log.i('  - Route: $currentRoute (isChatRoute: $isChatRoute)');
      LogService.log.i(
          '  - Controller registered: ${Get.isRegistered<ChatMessageController>()}');
      LogService.log.i('  - Current lamaran: $currentLamaran');
      LogService.log.i('  - Target lamaran: $idLamaran');
      LogService.log.i('  - Current koordinator: $currentKoordinator');
      LogService.log.i('  - Target koordinator: $idKoordinator');
      LogService.log.i('  - Parameters match: $isMatchingChat');
      LogService.log.i('  - Final result: $isChatActive');

      return isChatActive;
    } catch (e) {
      LogService.log.e('Error checking if chat is active: $e');
      return false;
    }
  }

  /// Enable auto-connect for user login
  void enableAutoConnect(String userId) {
    LogService.log.i('🔄 Enabling auto-connect for user: $userId');
    _autoConnectEnabled = true;
    connectWithUserId(userId);
  }

  /// Disable auto-connect for user logout
  void disableAutoConnect() {
    LogService.log.i('🔄 Disabling auto-connect');
    _autoConnectEnabled = false;
    disconnect();
  }

  /// Switch user (disconnect current and connect with new userId)
  Future<void> switchUser(String newUserId) async {
    LogService.log.i('🔄 Switching user to: $newUserId');

    if (_currentUserId != null && _currentUserId != newUserId) {
      LogService.log.i('Disconnecting current user: $_currentUserId');
      disconnect();

      // Wait a bit before connecting with new user
      await Future.delayed(const Duration(milliseconds: 500));
    }

    await connectWithUserId(newUserId);
  }

  /// Check if connected with specific userId
  bool isConnectedWithUser(String userId) {
    return isConnected && _currentUserId == userId;
  }

  /// Get connection info
  Map<String, dynamic> getConnectionInfo() {
    return {
      'status': _status.value.toString(),
      'currentUserId': _currentUserId,
      'autoConnectEnabled': _autoConnectEnabled,
      'isConnected': isConnected,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Reconnect with current user (if any)
  Future<void> reconnect() async {
    if (_currentUserId != null) {
      LogService.log.i('🔄 Reconnecting with user: $_currentUserId');
      disconnect();
      await Future.delayed(const Duration(milliseconds: 1000));
      await connectWithUserId(_currentUserId!);
    } else {
      LogService.log.w('Cannot reconnect: no current user');
    }
  }

  /// Handle chat notifications (for updating notifChatRef)
  void _handleChatNotification(Map<String, dynamic> messageData) {
    try {
      final notificationData = messageData['data'] ?? messageData;

      LogService.log.i('Broadcasting chat notification: $notificationData');
      _notificationController.add({
        'type': 'chat_notification',
        'data': notificationData,
      });
    } catch (e) {
      LogService.log.e('Error handling chat notification: $e');
    }
  }

  /// Handle notification updates (for real-time notif count updates)
  void _handleNotificationUpdate(Map<String, dynamic> messageData) {
    try {
      final updateData = messageData['data'] ?? messageData;

      LogService.log.i('Broadcasting notification update: $updateData');
      _notificationController.add({
        'type': 'notification_update',
        'data': updateData,
      });
    } catch (e) {
      LogService.log.e('Error handling notification update: $e');
    }
  }

  /// Parse timestamp from various formats
  DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();

    try {
      if (timestamp is String) {
        if (timestamp.isEmpty) return DateTime.now();

        // Try parsing as ISO string first
        try {
          final parsed = DateTime.parse(timestamp);
          // Ensure we have a valid date (not too far in past/future)
          if (parsed.year > 1970 && parsed.year < 2100) {
            return parsed;
          }
        } catch (_) {
          // If ISO parsing fails, try parsing as milliseconds string
          final millis = int.tryParse(timestamp);
          if (millis != null) {
            return _parseTimestampFromInt(millis);
          }
        }
      } else if (timestamp is int) {
        return _parseTimestampFromInt(timestamp);
      } else if (timestamp is double) {
        return _parseTimestampFromInt(timestamp.round());
      }
    } catch (e) {
      LogService.log.w('Failed to parse timestamp: $timestamp, error: $e');
    }

    LogService.log.w('Using current time for invalid timestamp: $timestamp');
    return DateTime.now();
  }

  /// Parse timestamp from integer (handles both seconds and milliseconds)
  DateTime _parseTimestampFromInt(int timestamp) {
    // Handle both seconds and milliseconds
    if (timestamp > 1000000000000) {
      // Looks like milliseconds (13+ digits)
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp > 1000000000) {
      // Looks like seconds (10+ digits)
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    } else {
      // Too small, probably invalid - use current time
      LogService.log.w('Timestamp too small, using current time: $timestamp');
      return DateTime.now();
    }
  }

  /// Handle WebSocket errors
  void _onError(error) {
    LogService.log.e('❌ WebSocket error: $error');
    LogService.log.e('Error type: ${error.runtimeType}');
    LogService.log.e('Error details: ${error.toString()}');
    _status.value = WebSocketStatus.error;

    // Attempt automatic reconnection after delay
    if (_autoConnectEnabled && _currentUserId != null) {
      LogService.log.i('🔄 Scheduling automatic reconnection in 5 seconds...');
      Future.delayed(const Duration(seconds: 5), () {
        if (_status.value == WebSocketStatus.error && _autoConnectEnabled) {
          LogService.log.i('🔄 Attempting automatic reconnection...');
          reconnect();
        }
      });
    }
  }

  /// Handle WebSocket disconnection
  void _onDisconnected() async {
    LogService.log.i('WebSocket disconnected');
    if (_channel != null) {
      _channel = null;
    }
    _status.value = WebSocketStatus.disconnected;
    await Future.delayed(const Duration(seconds: 1));
    legacyReconnect();
  }

  /// Manual reconnect (legacy method)
  void legacyReconnect() {
    disconnect();
    connect();
  }

  /// Test method to simulate incoming message (for debugging)
  void simulateIncomingMessage(String message, String sender) {
    final testMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      sender: sender,
      message: message,
      timestamp: DateTime.now(),
    );

    LogService.log.i('Simulating incoming message: $message from $sender');
    _messageController.add(testMessage);
  }

  /// Test timestamp parsing with different formats
  void testTimestampFormats() {
    final now = DateTime.now();
    final testFormats = [
      now.toIso8601String(), // ISO string
      now.millisecondsSinceEpoch, // Milliseconds int
      now.millisecondsSinceEpoch ~/ 1000, // Seconds int
      now.millisecondsSinceEpoch.toString(), // Milliseconds string
      (now.millisecondsSinceEpoch ~/ 1000).toString(), // Seconds string
      (now.millisecondsSinceEpoch / 1000), // Seconds double
    ];

    LogService.log.i('🧪 Testing timestamp formats:');
    LogService.log.i('Current time: $now');

    for (var format in testFormats) {
      final parsed = _parseTimestamp(format);
      LogService.log.i('Format: $format (${format.runtimeType}) -> $parsed');
    }
  }

  /// Diagnostic method to test WebSocket connectivity
  Future<void> testConnection(String userId) async {
    LogService.log.i('🧪 Starting WebSocket connection diagnostic test...');
    LogService.log
        .i('Target URL: ws://gestaltdev.digitalcv.id:3000?userId=$userId');
    LogService.log.i('Current status: ${_status.value}');
    LogService.log.i('Current user: $_currentUserId');
    LogService.log.i('Auto-connect enabled: $_autoConnectEnabled');

    try {
      LogService.log.i('📡 Attempting connection...');
      await connectWithUserId(userId);
      LogService.log.i('✅ Connection test successful!');
    } catch (e) {
      LogService.log.e('❌ Connection test failed: $e');
      LogService.log.e('Error type: ${e.runtimeType}');
    }
  }

  /// Map WebSocket sender values to consistent sender types
  String _mapSenderFromWebSocket(String? webSocketSender) {
    if (webSocketSender == null) return 'company';

    switch (webSocketSender.toLowerCase()) {
      case 'user':
      case 'candidate':
      case 'pelamar':
        return 'candidate';
      case 'hrd':
      case 'company':
      case 'koordinator':
      case 'admin':
      case 'server':
        return 'company';
      default:
        // If unknown, assume it's from company/HRD
        return 'company';
    }
  }
}
