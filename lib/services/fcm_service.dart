import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class FCMService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  FCMService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> updateToken(String pin, String fcmToken) async {
    try {
      final Map<String, dynamic> requestData = {
        "fcm_token": fcmToken,
      };
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/updateFCMToken.php", // Endpoint
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
