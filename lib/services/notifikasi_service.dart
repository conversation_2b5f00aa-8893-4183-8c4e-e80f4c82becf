import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class NotifikasiService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  NotifikasiService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getNotif(
    int page,
    int pageSize,
    String pin,
  ) async {
    // final Map<String, dynamic> requestData = {
    //   // "pin": pin,
    // };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/notifikasi.php", // Endpoint untuk mendapatkan list job
        queryParameters: {
          "func": "cekNotif",
          "page": page,
          "page_size": pageSize,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getNotif response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> countNotif(
    String pin,
  ) async {
    // final Map<String, dynamic> requestData = {
    //   // "pin": pin,
    // };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/notifikasi.php", // Endpoint untuk mendapatkan list job
        queryParameters: {
          "func": "countNewNotif",
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> updateNotif(
      {String? pengirim, String? penerima, String? tglKirim}) async {
    final Map<String, dynamic> requestData = {
      "penerima": penerima,
      "pengirim": pengirim,
      "tgl": tglKirim,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/notifikasi.php",
        data: requestData,
        queryParameters: {
          "func": "updateNotif",
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
