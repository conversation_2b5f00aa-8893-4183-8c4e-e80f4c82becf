import 'dart:async';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/recaptcha_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class RecaptchaService extends GetxController {
  // Menggunakan site key dari konfigurasi
  static String get siteKey => RecaptchaConfig.currentSiteKey;

  final RxBool isLoading = false.obs;
  final RxString recaptchaToken = "".obs;

  InAppWebViewController? webViewController;

  /// Mendapatkan token reCAPTCHA v3
  Future<String?> getRecaptchaToken({String action = "login"}) async {
    try {
      isLoading.value = true;

      // Reset token sebelumnya
      recaptchaToken.value = "";

      // Cek apakah perlu verifikasi berdasarkan behavior pattern
      final needsVerification = await _needsRobotVerification();

      if (!needsVerification) {
        // Jika tidak perlu verifikasi, dapatkan token secara silent
        final silentToken = await _getSilentRecaptchaToken(action);
        if (silentToken != null) {
          isLoading.value = false;
          recaptchaToken.value = silentToken;
          return silentToken;
        }
      }

      // Jika perlu verifikasi atau gagal mendapat token silent, tampilkan dialog
      return await _showRecaptchaDialog(action);
    } catch (e) {
      isLoading.value = false;
      LogService.log.e("Error getting reCAPTCHA token: $e");
      return null;
    }
  }

  /// Menentukan apakah perlu verifikasi robot berdasarkan pattern behavior
  Future<bool> _needsRobotVerification() async {
    try {
      // Faktor-faktor yang menentukan apakah user perlu verifikasi:
      // 1. Frequency login attempts
      // 2. Device information
      // 3. Time between actions
      // 4. IP reputation (simulasi)

      // Simulasi berdasarkan konfigurasi
      if (RecaptchaConfig.enableRandomVerification) {
        final random = DateTime.now().millisecondsSinceEpoch % 100;
        return random < RecaptchaConfig.randomVerificationPercentage;
      }

      // Default behavior: tidak perlu verifikasi kecuali dipaksa oleh controller
      return false;
    } catch (e) {
      LogService.log.e("Error checking robot verification need: $e");
      return true; // Default ke perlu verifikasi jika ada error
    }
  }

  /// Mendapatkan token reCAPTCHA secara silent (tanpa UI)
  Future<String?> _getSilentRecaptchaToken(String action) async {
    try {
      // Dalam implementasi nyata, ini akan menggunakan reCAPTCHA v3
      // yang berjalan di background tanpa user interaction

      // Simulasi token generation
      await Future.delayed(Duration(milliseconds: 800));

      // Generate mock token
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final mockToken = "mock_token_${action}_$timestamp";

      LogService.log.i("Silent reCAPTCHA token generated: $mockToken");
      return mockToken;
    } catch (e) {
      LogService.log.e("Error getting silent reCAPTCHA token: $e");
      return null;
    }
  }

  /// Menampilkan dialog reCAPTCHA ketika diperlukan
  Future<String?> _showRecaptchaDialog(String action) async {
    final Completer<String?> completer = Completer<String?>();

    // Tampilkan pesan info bahwa verifikasi diperlukan
    Get.snackbar(
      RecaptchaConfig.verificationTitle,
      RecaptchaConfig.verificationMessage,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      duration: Duration(seconds: 2),
    );

    // Tunggu sebentar sebelum menampilkan dialog
    await Future.delayed(Duration(milliseconds: 500));

    await Get.dialog(
      RecaptchaDialog(
        onTokenReceived: (token) {
          recaptchaToken.value = token ?? "";
          if (!completer.isCompleted) {
            completer.complete(token);
          }
        },
        onError: (error) {
          // Jika gagal diverifikasi (kemungkinan robot), tampilkan pesan
          Get.snackbar(
            "Verifikasi Gagal",
            RecaptchaConfig.robotDetectedMessage,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
            duration: Duration(seconds: 4),
          );
          if (!completer.isCompleted) {
            completer.complete(null);
          }
        },
        action: action,
      ),
      barrierDismissible: false,
    );

    final token = await completer.future;
    isLoading.value = false;
    return token;
  }

  /// Validasi token reCAPTCHA di server
  Future<bool> verifyRecaptchaToken(String token,
      {String action = "login"}) async {
    try {
      // Di sini Anda bisa menambahkan validasi ke server
      // Untuk sementara return true jika token tidak kosong
      return token.isNotEmpty;
    } catch (e) {
      LogService.log.e("Error verifying reCAPTCHA token: $e");
      return false;
    }
  }
}

class RecaptchaDialog extends StatefulWidget {
  final Function(String?) onTokenReceived;
  final Function(String) onError;
  final String action;

  const RecaptchaDialog({
    Key? key,
    required this.onTokenReceived,
    required this.onError,
    this.action = "login",
  }) : super(key: key);

  @override
  State<RecaptchaDialog> createState() => _RecaptchaDialogState();
}

class _RecaptchaDialogState extends State<RecaptchaDialog> {
  InAppWebViewController? webViewController;
  bool isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: Get.width * 0.9,
        height: 450,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  RecaptchaConfig.verificationTitle,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                  onPressed: () {
                    Get.back();
                    widget.onError("User cancelled verification");
                  },
                ),
              ],
            ),
            Divider(),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                RecaptchaConfig.verificationMessage,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 10),
            Expanded(
              child: Stack(
                children: [
                  InAppWebView(
                    initialOptions: InAppWebViewGroupOptions(
                      crossPlatform: InAppWebViewOptions(
                        useShouldOverrideUrlLoading: false,
                        mediaPlaybackRequiresUserGesture: false,
                        javaScriptEnabled: true,
                        transparentBackground: true,
                      ),
                      android: AndroidInAppWebViewOptions(
                        useHybridComposition: true,
                      ),
                      ios: IOSInAppWebViewOptions(
                        allowsInlineMediaPlayback: true,
                      ),
                    ),
                    initialData: InAppWebViewInitialData(
                      data: _getRecaptchaHTML(),
                      mimeType: "text/html",
                      encoding: "utf8",
                    ),
                    onWebViewCreated: (controller) {
                      webViewController = controller;

                      // Handler untuk menerima token dari JavaScript
                      controller.addJavaScriptHandler(
                        handlerName: 'recaptchaToken',
                        callback: (args) {
                          if (args.isNotEmpty) {
                            final token = args[0] as String;
                            LogService.log.i("reCAPTCHA token received: $token");
                            Get.back();
                            widget.onTokenReceived(token);
                          }
                        },
                      );

                      // Handler untuk error
                      controller.addJavaScriptHandler(
                        handlerName: 'recaptchaError',
                        callback: (args) {
                          if (args.isNotEmpty) {
                            final error = args[0] as String;
                            LogService.log.e("reCAPTCHA error: $error");
                            // Tampilkan dialog jika terdeteksi robot
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (_) => AlertDialog(
                                title: Row(
                                  children: const [
                                    Icon(Icons.warning, color: Colors.red),
                                    SizedBox(width: 8),
                                    Text("Verifikasi Gagal"),
                                  ],
                                ),
                                content: Text(
                                    "Maaf, verifikasi gagal. Anda mungkin terdeteksi sebagai robot. Silakan coba lagi nanti atau hubungi support jika masalah berlanjut."),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context)
                                          .pop(); // Tutup AlertDialog
                                      Get.back(); // Tutup RecaptchaDialog
                                      widget.onError(error);
                                    },
                                    child: Text("Tutup",
                                        style: TextStyle(color: Colors.red)),
                                  ),
                                ],
                              ),
                            );
                          }
                        },
                      );
                    },
                    onLoadStop: (controller, url) {
                      setState(() {
                        isLoading = false;
                      });
                    },
                    onLoadError: (controller, url, code, message) {
                      LogService.log.e("WebView load error: $message");
                      Get.back();
                      widget.onError(message);
                    },
                  ),
                  if (isLoading)
                    Container(
                      color: Colors.white.withOpacity(0.9),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                            SizedBox(height: 16),
                            Text(
                              "Memuat verifikasi keamanan...",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getRecaptchaHTML() {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Verification</title>
    <script src="https://www.google.com/recaptcha/api/siteverify?render=${RecaptchaService.siteKey}"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .shield-icon {
            color: white;
            font-size: 24px;
        }
        h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 20px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #4CAF50;
            font-weight: 500;
        }
        .error {
            color: #f44336;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <span class="shield-icon">🛡️</span>
        </div>
        <h2>Verifikasi Keamanan</h2>
        <p>Kami sedang memverifikasi bahwa Anda bukan robot. Mohon tunggu sebentar...</p>
        <div id="status" class="loading">
            <div class="spinner"></div>
            <span>Memverifikasi...</span>
        </div>
    </div>

    <script>
        let retryCount = 0;
        const maxRetries = 3;
        
        function executeRecaptcha() {
            const statusEl = document.getElementById('status');
            
            grecaptcha.ready(function() {
                statusEl.innerHTML = '<div class="spinner"></div><span>Memverifikasi...</span>';
                
                grecaptcha.execute('${RecaptchaService.siteKey}', { action: '${widget.action}' })
                    .then(function(token) {
                        console.log('reCAPTCHA token generated:', token);
                        statusEl.innerHTML = '<span class="success">✓ Verifikasi berhasil!</span>';
                        
                        // Kirim token ke Flutter
                        setTimeout(() => {
                            window.flutter_inappwebview.callHandler('recaptchaToken', token);
                        }, 500);
                    })
                    .catch(function(error) {
                        console.error('reCAPTCHA error:', error);
                        retryCount++;
                        
                        if (retryCount < maxRetries) {
                            statusEl.innerHTML = '<span class="error">Gagal verifikasi, mencoba lagi...</span>';
                            setTimeout(() => {
                                executeRecaptcha();
                            }, 2000);
                        } else {
                            statusEl.innerHTML = '<span class="error">Verifikasi gagal. Silakan coba lagi.</span>';
                            setTimeout(() => {
                                window.flutter_inappwebview.callHandler('recaptchaError', 'Max retries exceeded');
                            }, 2000);
                        }
                    });
            });
        }
        
        // Auto-execute when page loads
        window.addEventListener('load', function() {
            setTimeout(executeRecaptcha, 1000);
        });
    </script>
</body>
</html>
    ''';
  }
}
