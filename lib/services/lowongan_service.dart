import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LowonganService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  LowonganService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getListJob(
    String search,
    String lokasi,
    List<String> jenisPekerjaan,
    List<String> spesialisasi,
    List<String> pendidikan,
    String waktuPosting,
    int page,
    int pageSize,
    String pin,
  ) async {
    final Map<String, dynamic> requestData = {
      // "pin": pin,
      "pencarian": search,
      "lokasi": lokasi,
      "page": page,
      "page_size": pageSize,
    };

// Tambahkan field hanya jika tidak kosong
    if (jenisPekerjaan.isNotEmpty) {
      for (int i = 0; i < jenisPekerjaan.length; i++) {
        requestData["jenis_pekerjaan[$i]"] = jenisPekerjaan[i];
      }
    }
    if (spesialisasi.isNotEmpty) {
      for (int i = 0; i < spesialisasi.length; i++) {
        requestData["spesialisasi[$i]"] = spesialisasi[i];
      }
    }
    if (pendidikan.isNotEmpty) {
      for (int i = 0; i < pendidikan.length; i++) {
        requestData["pendidikan[$i]"] = pendidikan[i];
      }
    }
    if (waktuPosting.isNotEmpty || waktuPosting != "") {
      requestData["waktu_posting"] = waktuPosting;
    }
    try {
      var token = await secureStorage.read(key: "access_token");
      final prefs = Get.find<SharedPreferences>();
      final isLoggedIn = prefs.getBool("auth") ?? false;
      HttpResponse response;
      if (isLoggedIn == false) {
        response = await _httpApiClient.post(
          "/get_explore_jobs.php",
          data: requestData,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": "Bearer $token",
          },
        );
      } else {
        response = await _httpApiClient.post(
          "/get_jobs.php", // Endpoint untuk mendapatkan list job
          data: requestData,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": "Bearer $token",
          },
        );
      }
      LogService.log.i("getListJob isLoggedIn: $isLoggedIn");
      LogService.log.i("getListJob response: $requestData");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getListJobCompany(
    String idKoordinator,
    int page,
    int pageSize,
    String pin,
  ) async {
    final Map<String, dynamic> requestData = {
      "q": idKoordinator,
      // "pin": pin,
      "page": page,
      "page_size": pageSize,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_jobs_company.php", // Endpoint untuk mendapatkan list job
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getDetailJob(
    String idRequest,
    int page,
    int pageSize,
    String pin,
  ) async {
    final Map<String, dynamic> requestData = {
      // "pin": pin,
      "id_req": idRequest,
      "page": page,
      "page_size": pageSize,
    };
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/get_detail_lowongan.php", // Endpoint untuk mendapatkan list job
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getDetailJob response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getLowonganTersimpan(
    String search,
    int page,
    int pageSize,
    String pin,
  ) async {
    final Map<String, dynamic> requestData = {
      // "pin": pin,
      "pencarian": search,
      "page": page,
      "page_size": pageSize,
    };

    LogService.log.i("getLowonganTersimpan requestData: $requestData");

    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getLowonganTersimpan token: $token");
      HttpResponse response = await _httpApiClient.post(
        "/get_lowongan_tersimpan.php",
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getLowonganTersimpan response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveLowongan(String q, bool check, String pin) async {
    final Map<String, dynamic> requestData = {
      "q": q,
      "check": check,
      // "pin": pin
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/simpan_lowongan.php", // Endpoint untuk mendapatkan list job
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
