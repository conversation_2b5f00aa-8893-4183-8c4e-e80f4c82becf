import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

/// Service to manage WebSocket connection based on user authentication
class AuthWebSocketService extends GetxService {
  late WebSocketService _webSocketService;
  late FlutterSecureStorage _secureStorage;
  
  // Observable user state
  final RxString currentUserId = ''.obs;
  final RxBool isUserLoggedIn = false.obs;
  final RxBool isWebSocketConnected = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _setupListeners();
  }

  void _initializeServices() {
    _webSocketService = Get.find<WebSocketService>();
    _secureStorage = Get.find<FlutterSecureStorage>();
    LogService.log.i('🔧 AuthWebSocketService initialized');
  }

  void _setupListeners() {
    // Listen to WebSocket status changes
    _webSocketService.statusObservable.listen((status) {
      isWebSocketConnected.value = status == WebSocketStatus.connected;
      LogService.log.i('📡 WebSocket status changed: $status');
    });
  }

  /// Handle user login - connect WebSocket with userId
  Future<void> onUserLogin(String userId) async {
    try {
      LogService.log.i('🔐 User login detected: $userId');
      
      // Update user state
      currentUserId.value = userId;
      isUserLoggedIn.value = true;
      
      // Store userId in secure storage
      await _secureStorage.write(key: 'pin', value: userId);
      await _secureStorage.write(key: 'current_user_id', value: userId);
      
      // Enable auto-connect and connect WebSocket
      _webSocketService.enableAutoConnect(userId);
      
      LogService.log.i('✅ User login processed successfully for: $userId');
    } catch (e) {
      LogService.log.e('❌ Error processing user login: $e');
    }
  }

  /// Handle user logout - disconnect WebSocket
  Future<void> onUserLogout() async {
    try {
      final previousUserId = currentUserId.value;
      LogService.log.i('🔓 User logout detected: $previousUserId');
      
      // Update user state
      currentUserId.value = '';
      isUserLoggedIn.value = false;
      
      // Clear user data from secure storage
      await _secureStorage.delete(key: 'pin');
      await _secureStorage.delete(key: 'current_user_id');
      await _secureStorage.delete(key: 'token');
      
      // Disable auto-connect and disconnect WebSocket
      _webSocketService.disableAutoConnect();
      
      LogService.log.i('✅ User logout processed successfully for: $previousUserId');
    } catch (e) {
      LogService.log.e('❌ Error processing user logout: $e');
    }
  }

  /// Handle user switch - disconnect current and connect with new user
  Future<void> onUserSwitch(String newUserId) async {
    try {
      final previousUserId = currentUserId.value;
      LogService.log.i('🔄 User switch detected: $previousUserId → $newUserId');
      
      // Update user state
      currentUserId.value = newUserId;
      isUserLoggedIn.value = true;
      
      // Update secure storage
      await _secureStorage.write(key: 'pin', value: newUserId);
      await _secureStorage.write(key: 'current_user_id', value: newUserId);
      
      // Switch WebSocket connection
      await _webSocketService.switchUser(newUserId);
      
      LogService.log.i('✅ User switch processed successfully: $previousUserId → $newUserId');
    } catch (e) {
      LogService.log.e('❌ Error processing user switch: $e');
    }
  }

  /// Check and restore user session on app start
  Future<void> checkAndRestoreSession() async {
    try {
      LogService.log.i('🔍 Checking for existing user session...');
      
      final storedUserId = await _secureStorage.read(key: 'current_user_id');
      final storedPin = await _secureStorage.read(key: 'pin');
      final token = await _secureStorage.read(key: 'token');
      
      if (storedUserId != null && storedUserId.isNotEmpty && token != null) {
        LogService.log.i('📱 Found existing session for user: $storedUserId');
        
        // Restore user state
        currentUserId.value = storedUserId;
        isUserLoggedIn.value = true;
        
        // Auto-connect WebSocket
        _webSocketService.enableAutoConnect(storedUserId);
        
        LogService.log.i('✅ Session restored successfully for: $storedUserId');
      } else {
        LogService.log.i('❌ No valid session found');
        await onUserLogout(); // Clean up any partial data
      }
    } catch (e) {
      LogService.log.e('❌ Error checking user session: $e');
      await onUserLogout(); // Clean up on error
    }
  }

  /// Force reconnect WebSocket for current user
  Future<void> reconnectWebSocket() async {
    if (isUserLoggedIn.value && currentUserId.value.isNotEmpty) {
      LogService.log.i('🔄 Force reconnecting WebSocket for: ${currentUserId.value}');
      await _webSocketService.reconnect();
    } else {
      LogService.log.w('⚠️ Cannot reconnect: no user logged in');
    }
  }

  /// Get current connection status
  Map<String, dynamic> getConnectionStatus() {
    return {
      'currentUserId': currentUserId.value,
      'isUserLoggedIn': isUserLoggedIn.value,
      'isWebSocketConnected': isWebSocketConnected.value,
      'webSocketUserId': _webSocketService.currentUserId,
      'webSocketStatus': _webSocketService.status.toString(),
      'autoConnectEnabled': _webSocketService.autoConnectEnabled,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Debug connection state
  void debugConnectionState() {
    final status = getConnectionStatus();
    LogService.log.i('🔍 AuthWebSocket Connection Debug:');
    LogService.log.i('  Current User ID: ${status['currentUserId']}');
    LogService.log.i('  User Logged In: ${status['isUserLoggedIn']}');
    LogService.log.i('  WebSocket Connected: ${status['isWebSocketConnected']}');
    LogService.log.i('  WebSocket User ID: ${status['webSocketUserId']}');
    LogService.log.i('  WebSocket Status: ${status['webSocketStatus']}');
    LogService.log.i('  Auto Connect Enabled: ${status['autoConnectEnabled']}');
  }

  /// Test login with sample user
  Future<void> testLogin(String testUserId) async {
    LogService.log.i('🧪 Testing login with user: $testUserId');
    await onUserLogin(testUserId);
  }

  /// Test logout
  Future<void> testLogout() async {
    LogService.log.i('🧪 Testing logout');
    await onUserLogout();
  }

  /// Test user switch
  Future<void> testUserSwitch(String newUserId) async {
    LogService.log.i('🧪 Testing user switch to: $newUserId');
    await onUserSwitch(newUserId);
  }

  @override
  void onClose() {
    // Clean up when service is disposed
    if (isUserLoggedIn.value) {
      _webSocketService.disableAutoConnect();
    }
    super.onClose();
  }
}
