import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/user_model.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageAuthService {
  //Map<String, dynamic> data
  static Future<void> saveLoginData(List<UserModel> data) async {
    final pref = await SharedPreferences.getInstance();
    await pref.setBool("auth", true);
    await pref.setBool("isGoogle", false);
    // await pref.setString("pin", data[0].pin);
    // await pref.setString("nama", data[0].nama);
    // await pref.setString("email", data[0].email);
    // await pref.setString("image", data[0].image);
    // await pref.setString("no_telp", data[0].noTelp);
    // await pref.setString("tempat_lahir", data[0].tempatLahir);
    // await pref.setString("tgl_lahir", data[0].tglLahir);
    // await pref.setString("jenis_kelamin", data[0].jk);
    // await pref.setString("alamat", data[0].alamat);

    const secureStorage = FlutterSecureStorage();
    await secureStorage.write(key: "pin", value: data[0].pin);
    await secureStorage.write(key: "access_token", value: data[0].accessToken);
    await secureStorage.write(
        key: "refresh_token", value: data[0].refreshToken);
    await secureStorage.write(key: "nama", value: data[0].nama);
    await secureStorage.write(key: "email", value: data[0].email);
    await secureStorage.write(key: "image", value: data[0].image);
    await secureStorage.write(key: "no_telp", value: data[0].noTelp);
    await secureStorage.write(key: "tempat_lahir", value: data[0].tempatLahir);
    await secureStorage.write(key: "tgl_lahir", value: data[0].tglLahir);
    await secureStorage.write(key: "jenis_kelamin", value: data[0].jk);
    await secureStorage.write(key: "alamat", value: data[0].alamat);
    await secureStorage.write(
        key: "visibility", value: data[0].visibility.toString());
    await secureStorage.write(
        key: "status_pekerjaan", value: data[0].statusPekerjaan);
    Get.put(WebSocketService()).connect();
    // WebSocketService().enableAutoConnect(data[0].pin);
  }

  static Future<void> saveLoginDataGoogle(List<UserModel> data) async {
    final pref = await SharedPreferences.getInstance();
    await pref.setBool("auth", true);
    await pref.setBool("isGoogle", true);
    // await pref.setString("pin", data[0].pin);
    // await pref.setString("nama", data[0].nama);
    // await pref.setString("email", data[0].email);
    // await pref.setString("image", data[0].image);
    // await pref.setString("no_telp", data[0].noTelp);
    // await pref.setString("tempat_lahir", data[0].tempatLahir);
    // await pref.setString("tgl_lahir", data[0].tglLahir);
    // await pref.setString("jenis_kelamin", data[0].jk);
    // await pref.setString("alamat", data[0].alamat);

    const secureStorage = FlutterSecureStorage();
    await secureStorage.write(key: "pin", value: data[0].pin);
    await secureStorage.write(key: "access_token", value: data[0].accessToken);
    await secureStorage.write(
        key: "refresh_token", value: data[0].refreshToken);
    await secureStorage.write(key: "nama", value: data[0].nama);
    await secureStorage.write(key: "email", value: data[0].email);
    await secureStorage.write(key: "image", value: data[0].image);
    await secureStorage.write(key: "no_telp", value: data[0].noTelp);
    await secureStorage.write(key: "tempat_lahir", value: data[0].tempatLahir);
    await secureStorage.write(key: "tgl_lahir", value: data[0].tglLahir);
    await secureStorage.write(key: "jenis_kelamin", value: data[0].jk);
    await secureStorage.write(key: "alamat", value: data[0].alamat);
    await secureStorage.write(
        key: "visibility", value: data[0].visibility.toString());
    await secureStorage.write(
        key: "status_pekerjaan", value: data[0].statusPekerjaan);
    // WebSocketService().enableAutoConnect(data[0].pin);
    Get.put(WebSocketService()).connect();
  }

  static Future<Map<String, String?>> getLoginData() async {
    final pref = await SharedPreferences.getInstance();
    final secureStorage = FlutterSecureStorage();
    final pin = await secureStorage.read(key: "pin");
    final accessToken = await secureStorage.read(key: "access_token");
    final refreshToken = await secureStorage.read(key: "refresh_token");
    final nama = await secureStorage.read(key: "nama");
    final email = await secureStorage.read(key: "email");
    final image = await secureStorage.read(key: "image");
    final noTelp = await secureStorage.read(key: "no_telp");
    final tempatLahir = await secureStorage.read(key: "tempat_lahir");
    final tglLahir = await secureStorage.read(key: "tgl_lahir");
    final jenisKelamin = await secureStorage.read(key: "jenis_kelamin");
    final alamat = await secureStorage.read(key: "alamat");
    final visibility = await secureStorage.read(key: "visibility");
    final statusPekerjaan = await secureStorage.read(key: "status_pekerjaan");

    return {
      "pin": pin,
      "nama": nama,
      "email": email,
      "image": image,
      "no_telp": noTelp,
      "tempat_lahir": tempatLahir,
      "tgl_lahir": tglLahir,
      "jenis_kelamin": jenisKelamin,
      "alamat": alamat,
      "access_token": accessToken,
      "visibility": visibility,
      "status_pekerjaan": statusPekerjaan,
      "refresh_token": refreshToken,
    };
  }

  static Future<void> clearLoginData() async {
    final pref = await SharedPreferences.getInstance();
    await pref.clear();
    const secureStorage = FlutterSecureStorage();
    await secureStorage.delete(key: "pin");
    await secureStorage.delete(key: "access_token");
    await secureStorage.delete(key: "refresh_token");
    await secureStorage.delete(key: "nama");
    await secureStorage.delete(key: "email");
    await secureStorage.delete(key: "image");
    await secureStorage.delete(key: "no_telp");
    await secureStorage.delete(key: "tempat_lahir");
    await secureStorage.delete(key: "tgl_lahir");
    await secureStorage.delete(key: "jenis_kelamin");
    await secureStorage.delete(key: "alamat");
    await secureStorage.delete(key: "visibility");
    await secureStorage.delete(key: "status_pekerjaan");
    LogService.log.i("Login data cleared");
    WebSocketService().disableAutoConnect();
  }
}
