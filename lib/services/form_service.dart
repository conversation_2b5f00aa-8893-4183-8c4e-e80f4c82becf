import 'dart:io';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;

class FormService {
  late final HttpClient _httpApiClient;
  late final HttpClient _httpApiCVAI;
  final secureStorage = FlutterSecureStorage();

  FormService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
    _httpApiCVAI = HttpClient(baseUrl: cvaiUri);
  }

  Future<HttpResponse> saveInfoPribadi(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "nama": data[0]['nama'],
      "tempat_lahir": data[0]['tempat_lahir'],
      "tgl_lahir": data[0]['tgl_lahir'],
      "jk": data[0]['jk'],
      "status_pernikahan": data[0]['status_pernikahan'],
      "ktp": data[0]['ktp'],
      "no_telepon": data[0]['no_telepon'],
      "email": data[0]['email'],
      "provinsi": data[0]['provinsi'],
      "kota_tinggal": data[0]['kota_tinggal'],
      "kec_tinggal": data[0]['kec_tinggal'],
      "rt_tinggal": data[0]['rt_tinggal'],
      "rw_tinggal": data[0]['rw_tinggal'],
      "alamat_tinggal": data[0]['alamat_tinggal'],
      "pos_tinggal": data[0]['pos_tinggal'],
    };

    if (data[0]['sim'] != null) {
      List<String> cleanSIMs = (data[0]['sim'] as List<String>)
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      for (int i = 0; i < cleanSIMs.length; i++) {
        requestData["sim[$i]"] = cleanSIMs[i];
      }
    }

    LogService.log.i("REQ save Info : $requestData");

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanIdentitasDiri"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response save Info Pribadi: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveDataCVATS(List<Map<String, dynamic>> data) async {
    try {
      var token = await secureStorage.read(key: "access_token");

      var requestData = {
        "nama": data[0]['nama'],
        "minat_posisi": data[0]['minat_posisi'],
        "no_telepon": data[0]['no_telepon'],
        "email": data[0]['email'],
        "alamat_tinggal": data[0]['alamat_tinggal'],
        "linkLinkedin": data[0]['linkLinkedin'],
        "linkPortofolio": data[0]['linkPortofolio'],
        "akun_instagram": data[0]['akun_instagram'],
        "akun_fb": data[0]['akun_fb'],
        "akun_x": data[0]['akun_x'],
        "akun_tiktok": data[0]['akun_tiktok'],
      };

      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanPersonalData"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response save Info Pribadi: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveRiwayatPendidikan(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "pendidikan_tinggi": data[0]['pendidikan_tinggi'],
      "cek_diploma": data[0]['cek_diploma'],
      "jenjang_pendidikan": data[0]['jenjang_pendidikan'],
      "nama_sekolah_pendidikan": data[0]['nama_sekolah_pendidikan'],
      "jurusan_pendidikan": data[0]['jurusan_pendidikan'],
      "tahun_mulai_pendidikan": data[0]['tahun_mulai_pendidikan'],
      "tahun_selesai_pendidikan": data[0]['tahun_selesai_pendidikan'],
      "ket_pendidikan": data[0]['ket_pendidikan'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanRiwayatPendidikan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response save Pendidikan: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> updateRiwayatPendidikan(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_riwayat": data[0]['id_riwayat'],
      "pendidikan_tinggi": data[0]['pendidikan_tinggi'],
      "cek_diploma": data[0]['cek_diploma'],
      "jenjang_pendidikan": data[0]['jenjang_pendidikan'],
      "nama_sekolah_pendidikan": data[0]['nama_sekolah_pendidikan'],
      "jurusan_pendidikan": data[0]['jurusan_pendidikan'],
      "tahun_mulai_pendidikan": data[0]['tahun_mulai_pendidikan'],
      "tahun_selesai_pendidikan": data[0]['tahun_selesai_pendidikan'],
      "ket_pendidikan": data[0]['ket_pendidikan'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "updateRiwayatPendidikan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> deleteRiwayatPendidikan(
      String idRiwayat, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_riwayat": idRiwayat,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "deleteRiwayatPendidikan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveRiwayatKursus(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "kursus": data[0]['kursus'],
      "nama_kursus": data[0]['nama_kursus'],
      "sertifikat_kursus": data[0]['sertifikat_kursus'],
      "tempat_kursus": data[0]['tempat_kursus'],
      "tgl_mulai_kursus": data[0]['tgl_mulai_kursus'],
      "tgl_selesai_kursus": data[0]['tgl_selesai_kursus'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanRiwayatKursus"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> updateRiwayatKursus(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_kursus": data[0]['id_kursus'],
      "kursus": data[0]['kursus'],
      "nama_kursus": data[0]['nama_kursus'],
      "sertifikat_kursus": data[0]['sertifikat_kursus'],
      "tempat_kursus": data[0]['tempat_kursus'],
      "tgl_mulai_kursus": data[0]['tgl_mulai_kursus'],
      "tgl_selesai_kursus": data[0]['tgl_selesai_kursus'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "updateRiwayatKursus"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> deleteRiwayatKursus(String idKursus, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_kursus": idKursus,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "deleteRiwayatKursus"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveRiwayatPekerjaan(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "pengalaman_kerja": data[0]['pengalaman_kerja'],
      "total_pengalaman_kerja": data[0]['total_pengalaman_kerja'],
      "pengalaman_posisi_sama": data[0]['pengalaman_posisi_sama'],
      "nama": data[0]['nama'],
      "jabatan": data[0]['jabatan'],
      "status": data[0]['status'],
      "gaji": data[0]['gaji'],
      "tahun_mulai": data[0]['tahun_mulai'],
      "tahun_selesai": data[0]['tahun_selesai'],
      "alasan": data[0]['alasan'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanRiwayatPekerjaan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response save Pengalaman kerja: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> updateRiwayatPekerjaan(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_pekerjaan": data[0]['id_pekerjaan'],
      "pengalaman_kerja": data[0]['pengalaman_kerja'],
      "total_pengalaman_kerja": data[0]['total_pengalaman_kerja'],
      "pengalaman_posisi_sama": data[0]['pengalaman_posisi_sama'],
      "nama": data[0]['nama'],
      "jabatan": data[0]['jabatan'],
      "status": data[0]['status'],
      "gaji": data[0]['gaji'],
      "tahun_mulai": data[0]['tahun_mulai'],
      "tahun_selesai": data[0]['tahun_selesai'],
      "alasan": data[0]['alasan'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "updateRiwayatPekerjaan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response update Pengalaman kerja: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> deleteRiwayatPekerjaan(
      String idPekerjaan, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_pekerjaan": idPekerjaan,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "deleteRiwayatPekerjaan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveInfoPekerjaan(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "perjalanan_dinas": data[0]['perjalanan_dinas'],
      "minat_lokasi_kerja": data[0]['minat_lokasi_kerja'],
      "temp_gaji": data[0]['temp_gaji'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanInformasiPekerjaan"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveRiwayatOrganisasi(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "organisasi": data[0]['organisasi'],
      "nama": data[0]['nama'],
      "jabatan": data[0]['jabatan'],
      "tahun": data[0]['tahun'],
      "tempat": data[0]['tempat'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanRiwayatOrganisasi"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("Response save Organisasi: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> updateRiwayatOrganisasi(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_organisasi": data[0]['id_organisasi'],
      "organisasi": data[0]['organisasi'],
      "nama": data[0]['nama'],
      "jabatan": data[0]['jabatan'],
      "tahun": data[0]['tahun'],
      "tempat": data[0]['tempat'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "updateRiwayatOrganisasi"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> deleteRiwayatOrganisasi(
      String idOrganisasi, String pin) async {
    final Map<String, dynamic> requestData = {
      "id_organisasi": idOrganisasi,
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "deleteRiwayatOrganisasi"},
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> saveMinatKonsep(
      List<Map<String, dynamic>> data, String pin) async {
    final Map<String, dynamic> requestData = {
      "penguasaan_bahasa": data[0]['penguasaan_bahasa'],
      "bahasa_asing": data[0]['bahasa_asing'],
      "kelebihan": data[0]['kelebihan'],
      "kekurangan": data[0]['kekurangan'],
      "komputerisasi": data[0]['komputerisasi'],
      "memimpin_tim": data[0]['memimpin_tim'],
      "presentasi": data[0]['presentasi'],
      "rlp": data[0]['rlp'],
    };

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/form_cv.php",
        queryParameters: {"func": "simpanMinatKonsep"},
        data: requestData,
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> analisaCVAI() async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiCVAI.post(
        "/ai.php",
        queryParameters: {"func": "analisaCVKandidat"},
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> uploadCv(File cvFile) async {
    try {
      var token = await secureStorage.read(key: "access_token");

      LogService.log.i("Uploading CV file: ${cvFile.path}");

      HttpResponse response = await _httpApiCVAI.postMultipart(
        "/ai.php",
        queryParameters: {"func": "uploadCV"},
        files: {
          "myfile[]": cvFile,
        },
        headers: {
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("Response upload CV: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<String> downloadCv() async {
    try {
      var token = await secureStorage.read(key: "access_token");

      if (token == null) {
        throw "Token not found";
      }

      LogService.log.i("Getting download URL for CV");

      // Request permission untuk storage
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        if (!status.isGranted) {
          throw "Storage permission denied";
        }
      }

      // Step 1: Hit API untuk mendapatkan download URL
      HttpResponse response = await _httpApiClient.get(
        "/form_cv.php",
        queryParameters: {"func": "downloadCV"},
        headers: {
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("Response get download URL: ${response.data}");

      if (response.statusCode != 200) {
        throw "Failed to get download URL: ${response.data}";
      }

      // Parse response untuk mendapatkan file_url
      final responseData = response.data;
      if (responseData['status'] != 'success') {
        throw responseData['message'] ?? "Failed to get download URL";
      }

      final fileUrl = responseData['data']['file_url'];
      if (fileUrl == null || fileUrl.isEmpty) {
        throw "Download URL not found in response";
      }

      LogService.log.i("Download URL: $fileUrl");

      // Step 2: Download file dari URL yang didapat
      final downloadResponse = await http.get(Uri.parse(fileUrl));

      if (downloadResponse.statusCode == 200) {
        // Get download directory
        Directory? directory;
        if (Platform.isAndroid) {
          directory = await getExternalStorageDirectory();
        } else {
          directory = await getApplicationDocumentsDirectory();
        }

        if (directory == null) {
          throw "Could not access storage directory";
        }

        // Create file path
        final fileName = "CV_ATS_${DateTime.now().millisecondsSinceEpoch}.pdf";
        final filePath = "${directory.path}/$fileName";

        // Write file
        final file = File(filePath);
        await file.writeAsBytes(downloadResponse.bodyBytes);

        LogService.log.i("CV downloaded successfully: $filePath");
        return filePath;
      } else {
        LogService.log
            .e("Download failed with status: ${downloadResponse.statusCode}");
        throw "Failed to download CV file: ${downloadResponse.statusCode}";
      }
    } catch (e) {
      LogService.log.e("Download Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "Download failed: $e";
      }
    }
  }
}
