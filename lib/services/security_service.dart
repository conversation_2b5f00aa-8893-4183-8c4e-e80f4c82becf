import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:flutter_jailbreak_detection_plus/flutter_jailbreak_detection_plus.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecurityService {
  static const int maxGlobalAttempts = 3; // Maximum attempts per device
  static const int lockoutDurationMinutes = 30; // Increased lockout time
  static const int maxAttemptsPerEmailPerDay = 5; // Per email per day limit
  static const String deviceFingerprintKey = 'device_fingerprint';
  static const String globalAttemptsKey = 'global_failed_attempts';
  static const String lastAttemptTimeKey = 'last_attempt_time';
  static const String lockedUntilKey = 'locked_until_time';
  static const String dailyAttemptsKey = 'daily_attempts_';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Generate unique device fingerprint
  static Future<String> generateDeviceFingerprint() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      String deviceId = '';
      String model = '';
      String os = '';

      if (GetPlatform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id;
        model = androidInfo.model;
        os = 'Android ${androidInfo.version.release}';
      } else if (GetPlatform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown';
        model = iosInfo.model;
        os = 'iOS ${iosInfo.systemVersion}';
      }

      // Create unique fingerprint
      String fingerprint = '$deviceId-$model-$os';
      var bytes = utf8.encode(fingerprint);
      var digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      LogService.log.e("Failed to generate device fingerprint: $e");
      // Fallback fingerprint
      return sha256
          .convert(
              utf8.encode('fallback-${DateTime.now().millisecondsSinceEpoch}'))
          .toString();
    }
  }

  /// Check if device is secure (not jailbroken)
  static Future<bool> isDeviceSecure() async {
    try {
      final jailbroken = await FlutterJailbreakDetectionPlus.jailbroken;
      final developerMode = await FlutterJailbreakDetectionPlus.developerMode;

      if (jailbroken) {
        LogService.log
            .w("⚠️ Perangkat terdeteksi telah di-jailbreak / developer mode.");
        return false;
      }

      return true;
    } catch (e) {
      LogService.log.w("⚠️ Gagal memeriksa jailbreak: $e");
      return false; // Anggap tidak aman jika tidak bisa diperiksa
    }
  }

  /// Check if login is allowed based on comprehensive security checks
  static Future<SecurityCheckResult> checkLoginAllowed(String email) async {
    await init();

    // Check device security first
    if (!await isDeviceSecure()) {
      return SecurityCheckResult(
          allowed: false,
          reason: SecurityBlockReason.insecureDevice,
          message: "Login tidak diizinkan pada perangkat yang tidak aman");
    }

    String deviceFingerprint = await generateDeviceFingerprint();
    await _prefs!.setString(deviceFingerprintKey, deviceFingerprint);

    DateTime now = DateTime.now();

    // Check if device is currently locked
    int lockedUntil =
        _prefs!.getInt('${lockedUntilKey}_$deviceFingerprint') ?? 0;
    if (lockedUntil > now.millisecondsSinceEpoch) {
      DateTime unlockTime = DateTime.fromMillisecondsSinceEpoch(lockedUntil);
      int remainingMinutes = unlockTime.difference(now).inMinutes + 1;

      return SecurityCheckResult(
          allowed: false,
          reason: SecurityBlockReason.deviceLocked,
          message:
              "Perangkat terkunci karena terlalu banyak percobaan gagal. Coba lagi dalam",
          remainingTime: remainingMinutes);
    }

    // Check global attempts for this device
    // int globalAttempts =
    //     _prefs!.getInt('${globalAttemptsKey}_$deviceFingerprint') ?? 0;
    // if (globalAttempts >= maxGlobalAttempts) {
    //   await _lockDevice(deviceFingerprint);
    //   return SecurityCheckResult(
    //       allowed: false,
    //       reason: SecurityBlockReason.maxAttemptsReached,
    //       message:
    //           "Perangkat terkunci karena terlalu banyak percobaan login gagal");
    // }

    // Check daily attempts per email
    // String today = '${now.year}-${now.month}-${now.day}';
    // String dailyKey = '$dailyAttemptsKey${email}_$today';
    // int dailyAttempts = _prefs!.getInt(dailyKey) ?? 0;

    // if (dailyAttempts >= maxAttemptsPerEmailPerDay) {
    //   return SecurityCheckResult(
    //       allowed: false,
    //       reason: SecurityBlockReason.maxAttemptsReached,
    //       message:
    //           "Batas percobaan login harian untuk email ini telah tercapai",
    //       remainingTime: );
    // }

    // Check for suspicious rapid attempts
    int lastAttemptTime =
        _prefs!.getInt('${lastAttemptTimeKey}_$deviceFingerprint') ?? 0;
    int timeSinceLastAttempt = now.millisecondsSinceEpoch - lastAttemptTime;

    if (timeSinceLastAttempt < 5000) {
      // Less than 5 seconds
      return SecurityCheckResult(
          allowed: false,
          reason: SecurityBlockReason.tooFast,
          message:
              "Percobaan terlalu cepat. Tunggu beberapa detik sebelum mencoba lagi");
    }

    return SecurityCheckResult(
        allowed: true,
        reason: SecurityBlockReason.none,
        message: "Login diizinkan",
        remainingAttempts: maxGlobalAttempts);
  }

  /// Record failed login attempt
  static Future<void> recordFailedAttempt(String email) async {
    await init();

    String deviceFingerprint = await generateDeviceFingerprint();
    DateTime now = DateTime.now();

    // Increment global attempts
    int globalAttempts =
        _prefs!.getInt('${globalAttemptsKey}_$deviceFingerprint') ?? 0;
    globalAttempts++;
    await _prefs!
        .setInt('${globalAttemptsKey}_$deviceFingerprint', globalAttempts);

    // Increment daily attempts for this email
    String today = '${now.year}-${now.month}-${now.day}';
    String dailyKey = '$dailyAttemptsKey${email}_$today';
    int dailyAttempts = _prefs!.getInt(dailyKey) ?? 0;
    dailyAttempts++;
    await _prefs!.setInt(dailyKey, dailyAttempts);

    // Record attempt time
    await _prefs!.setInt(
        '${lastAttemptTimeKey}_$deviceFingerprint', now.millisecondsSinceEpoch);

    // Lock device if max attempts reached
    if (globalAttempts >= maxGlobalAttempts) {
      await _lockDevice(deviceFingerprint);
    }

    LogService.log.w(
        "Failed login attempt recorded. Global: $globalAttempts, Daily for $email: $dailyAttempts");
  }

  /// Record successful login (only reset global attempts, not daily)
  static Future<void> recordSuccessfulLogin(String email) async {
    await init();

    String deviceFingerprint = await generateDeviceFingerprint();

    // Reset global attempts only (keep daily attempts for security)
    await _prefs!.remove('${globalAttemptsKey}_$deviceFingerprint');
    await _prefs!.remove('${lastAttemptTimeKey}_$deviceFingerprint');
    await _prefs!.remove('${lockedUntilKey}_$deviceFingerprint');

    LogService.log.i("Successful login recorded for $email");
  }

  /// Lock device for specified duration
  static Future<void> _lockDevice(String deviceFingerprint) async {
    DateTime lockUntil =
        DateTime.now().add(Duration(minutes: lockoutDurationMinutes));
    await _prefs!.setInt('${lockedUntilKey}_$deviceFingerprint',
        lockUntil.millisecondsSinceEpoch);

    LogService.log.w("Device locked until ${lockUntil.toString()}");
  }

  /// Get security metrics for monitoring
  static Future<SecurityMetrics> getSecurityMetrics() async {
    await init();

    String deviceFingerprint = await generateDeviceFingerprint();

    int globalAttempts =
        _prefs!.getInt('${globalAttemptsKey}_$deviceFingerprint') ?? 0;
    int lastAttemptTime =
        _prefs!.getInt('${lastAttemptTimeKey}_$deviceFingerprint') ?? 0;
    int lockedUntil =
        _prefs!.getInt('${lockedUntilKey}_$deviceFingerprint') ?? 0;

    return SecurityMetrics(
      deviceFingerprint: deviceFingerprint,
      globalAttempts: globalAttempts,
      isLocked: lockedUntil > DateTime.now().millisecondsSinceEpoch,
      lastAttemptTime: lastAttemptTime > 0
          ? DateTime.fromMillisecondsSinceEpoch(lastAttemptTime)
          : null,
      lockedUntil: lockedUntil > 0
          ? DateTime.fromMillisecondsSinceEpoch(lockedUntil)
          : null,
    );
  }

  /// Reset all security data (for testing purposes only)
  static Future<void> resetAllSecurityData() async {
    await init();

    String deviceFingerprint = await generateDeviceFingerprint();

    await _prefs!.remove('${globalAttemptsKey}_$deviceFingerprint');
    await _prefs!.remove('${lastAttemptTimeKey}_$deviceFingerprint');
    await _prefs!.remove('${lockedUntilKey}_$deviceFingerprint');

    // Remove daily attempts (be careful with this in production)
    Set<String> keys = _prefs!.getKeys();
    for (String key in keys) {
      if (key.startsWith(dailyAttemptsKey)) {
        await _prefs!.remove(key);
      }
    }

    LogService.log.w("All security data reset");
  }
}

/// Result of security check
class SecurityCheckResult {
  final bool allowed;
  final SecurityBlockReason reason;
  final String message;
  final int? remainingTime;
  final int? remainingAttempts;

  SecurityCheckResult({
    required this.allowed,
    required this.reason,
    required this.message,
    this.remainingTime,
    this.remainingAttempts,
  });
}

/// Reasons why login might be blocked
enum SecurityBlockReason {
  none,
  insecureDevice,
  deviceLocked,
  maxAttemptsReached,
  dailyLimitReached,
  tooFast,
}

/// Security metrics for monitoring
class SecurityMetrics {
  final String deviceFingerprint;
  final int globalAttempts;
  final bool isLocked;
  final DateTime? lastAttemptTime;
  final DateTime? lockedUntil;

  SecurityMetrics({
    required this.deviceFingerprint,
    required this.globalAttempts,
    required this.isLocked,
    this.lastAttemptTime,
    this.lockedUntil,
  });
}

/// Password security functions for client-side hashing
class PasswordSecurity {
  /// Generate a salt for password hashing
  static String generateSalt() {
    final random = List<int>.generate(
        32, (i) => DateTime.now().millisecondsSinceEpoch + i);
    return base64.encode(random);
  }

  /// Hash password with multiple rounds of SHA-256
  static String hashPassword(String password, String salt) {
    try {
      // Combine password and salt
      String combined = password + salt;

      // Multiple rounds of hashing for security
      var digest = sha256.convert(utf8.encode(combined));

      // Additional rounds for strength
      for (int i = 0; i < 1000; i++) {
        digest = sha256.convert(utf8.encode(digest.toString() + salt));
      }

      return digest.toString();
    } catch (e) {
      LogService.log.e("Error hashing password: $e");
      // Fallback to simpler hashing
      return _fallbackHash(password, salt);
    }
  }

  /// Fallback hashing method using SHA-256
  static String _fallbackHash(String password, String salt) {
    final combined = password + salt;
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Hash password for transmission (client-side hashing)
  static String hashPasswordForTransmission(String password, String email) {
    try {
      // Use email and timestamp as entropy for salt
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final deviceInfo = DateTime.now().microsecondsSinceEpoch.toString();

      // Create a complex salt using email and timestamp
      final saltBase = email.toLowerCase() +
          timestamp.substring(0, 10) +
          deviceInfo.substring(0, 6);
      final saltBytes = utf8.encode(saltBase);
      final salt = base64.encode(sha256.convert(saltBytes).bytes);

      // First hash with salt
      final firstHash = hashPassword(password, salt);

      // Second hash for additional security
      final secondSalt = generateSalt().substring(0, 16);
      final finalHash = hashPassword(firstHash, secondSalt);

      // Return format: hash:salt:timestamp
      return "$finalHash:$secondSalt:${timestamp.substring(0, 10)}";
    } catch (e) {
      LogService.log.e("Error hashing password for transmission: $e");
      // Fallback to single hash
      final bytes = utf8.encode(password + email);
      final digest = sha256.convert(bytes);
      return digest.toString();
    }
  }

  /// Hash password with HMAC for extra security
  static String hashPasswordWithHMAC(String password, String key) {
    try {
      final keyBytes = utf8.encode(key);
      final passwordBytes = utf8.encode(password);

      final hmac = Hmac(sha256, keyBytes);
      final digest = hmac.convert(passwordBytes);

      return digest.toString();
    } catch (e) {
      LogService.log.e("Error creating HMAC hash: $e");
      return hashPassword(password, key);
    }
  }

  /// Validate password strength
  static bool isPasswordStrong(String password) {
    if (password.length < 8) return false;
    if (!RegExp(r'[A-Z]').hasMatch(password)) return false;
    if (!RegExp(r'[a-z]').hasMatch(password)) return false;
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;
    return true;
  }

  /// Generate secure random password
  static String generateSecurePassword({int length = 12}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*()';
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random =
        List<int>.generate(length, (i) => (timestamp + i * 37) % chars.length);
    return String.fromCharCodes(random.map((i) => chars.codeUnitAt(i)));
  }

  /// Create client-side authentication token
  static String createAuthToken(
      String hashedPassword, String email, String deviceId) {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final tokenData = "$hashedPassword:$email:$deviceId:$timestamp";
      final tokenBytes = utf8.encode(tokenData);
      final tokenHash = sha256.convert(tokenBytes);

      // Encode as base64 for transmission
      return base64.encode(utf8.encode(tokenHash.toString()));
    } catch (e) {
      LogService.log.e("Error creating auth token: $e");
      return base64.encode(utf8.encode(hashedPassword));
    }
  }
}

/// Legacy function for backward compatibility
Future<bool> isDeviceSecure() async {
  return await SecurityService.isDeviceSecure();
}
