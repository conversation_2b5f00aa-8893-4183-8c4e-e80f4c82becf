import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/password_security_helper.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class AuthService {
  late final HttpClient _httpClient;
  late final HttpClient _httpApiClient;
  late final HttpClient _httpRootApiClient;
  final secureStorage = FlutterSecureStorage();

  AuthService() {
    _httpClient = HttpClient(baseUrl: uri);
    _httpApiClient = HttpClient(baseUrl: api_uri);
    _httpRootApiClient = HttpClient(baseUrl: rootUri);
  }

  Future<HttpResponse> login(String email, String password) async {
    try {
      String hashedPassword = PasswordSecurityHelper.encryptPassword(password);
      String hashedEmail = PasswordSecurityHelper.encryptPassword(email);
      LogService.log
          .i("Login Request: Email=$hashedEmail, Password=$hashedPassword");
      HttpResponse response = await _httpApiClient.post(
        "/login.php", // Endpoint login
        data: {
          "email": hashedEmail,
          "password": hashedPassword,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> loginGoogle(String email) async {
    try {
      HttpResponse response = await _httpApiClient.post(
        "/login_google.php", // Endpoint login
        data: {
          "email": email,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> loginWithApple(String email, String name) async {
    try {
      HttpResponse response = await _httpApiClient.post(
        "/login_apple.php", // Endpoint login Apple
        data: {
          "email": email,
          "nama": name,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      LogService.log.i("Login with Apple response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> registerWithApple({
    required String email,
    required String name,
  }) async {
    try {
      HttpResponse response = await _httpApiClient.post(
        "/register_apple.php", // Endpoint for Apple registration
        data: {
          "email": email,
          "name": name,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> register(String nama, String email, String password,
      String noHandphone, String fcmToken) async {
    try {
      HttpResponse response = await _httpApiClient.post(
        "/registerCandidate.php", // Endpoint register
        data: {
          "nama": nama,
          "email": email,
          "password": password,
          "no_tlp": noHandphone,
          "fcm_token": fcmToken,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> reqOtp(
      String email, String noHp, String? forParam) async {
    try {
      LogService.log
          .i("Requesting OTP for email: $email, phone: $noHp, for: $forParam");
      HttpResponse response = await _httpRootApiClient.post(
        "/sendOTP.php",
        data: {
          "email": email,
          "no_hp_pic": noHp,
          "for": forParam ?? "candidate",
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Accept": "application/json",
        },
      );

      // 🔹 Debugging: Cetak response sebelum dikembalikan
      LogService.log.i("Response Status Code: ${response.statusCode}");
      LogService.log.i("Response Body: ${response.data}");

      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> reqResetPassword(String email) async {
    try {
      HttpResponse response = await _httpApiClient.post(
        "/forgotPasswordCandidate.php", // Endpoint login
        data: {
          "email": email,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      LogService.log.i("reqResetPassword response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> resetPassword(String password, String token) async {
    try {
      HttpResponse response = await _httpClient.post(
        "/login/resetPasswordCandidate.php", // Endpoint login
        data: {"password": password, "token": token},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> changePassword(String pin, String passLama,
      String passBaru, String konfirmPassBaru, String otp) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/profile.php", // Endpoint
        queryParameters: {"func": "updateKataSandi"},
        data: {
          "password_lama": passLama,
          "password": passBaru,
          "kon_password": konfirmPassBaru,
          "otp": otp,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> logout() async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/logout.php",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> kirimUlangVerifikasi({
    required String email,
  }) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/verifikasi-akun.php",
        queryParameters: {"func": "kirimUlangVerifikasi"},
        data: {"email": email},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> checkStatusVerifikasi({
    required String email,
  }) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/verifikasi-akun.php",
        queryParameters: {"func": "cekStatus"},
        data: {"email": email},
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");

      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
