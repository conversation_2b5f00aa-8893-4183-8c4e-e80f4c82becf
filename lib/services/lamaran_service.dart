import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class LamaranService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  LamaranService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getRiwayatLamaran(
      int page, int pageSize, String pin, String q,
      {String? idLamar, String? idReq}) async {
    final Map<String, dynamic> requestData = {
      "page": page,
      "page_size": pageSize,
      "pencarian": q,
    };

    if (idLamar != null) {
      requestData['id_lamar'] = idLamar;
    }

    if (idReq != null) {
      requestData['id_req'] = idReq;
    }

    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.post(
        "/riwayat_lamaran.php",
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getTimeline(
    String idLamaran,
  ) async {
    final Map<String, dynamic> requestData = {
      "id_lamar": idLamaran,
    };
    LogService.log.i("ID Lamar : $idLamaran");
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getTimeline response: $token");
      HttpResponse response = await _httpApiClient.post(
        "/get_timeline.php", // Endpoint untuk mendapatkan list job
        data: requestData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      LogService.log.i("getTimeline response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
