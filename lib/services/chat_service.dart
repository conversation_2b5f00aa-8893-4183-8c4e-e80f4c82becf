import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:digital_cv_mobile/models/riwayat_chat_model.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class ChatService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  ChatService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getRiwayatChat({
    required String idKoordinator,
    required String idLamaran,
  }) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getRiwayat Chat token: $token");

      HttpResponse response = await _httpApiClient.post(
        "/chat.php",
        queryParameters: {"func": "riwayatChat"},
        data: {
          "id_koordinator": idKoordinator,
          "id_lamar": idLamaran,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("getRiwayat Chat response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  /// Parse riwayat chat response and convert to RiwayatChatModel list
  Future<List<RiwayatChatModel>> getRiwayatChatParsed({
    required String idKoordinator,
    required String idLamaran,
  }) async {
    try {
      HttpResponse response = await getRiwayatChat(
        idKoordinator: idKoordinator,
        idLamaran: idLamaran,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        if (data is Map<String, dynamic>) {
          if (data['status'].toString() == "true") {
            final chatData = data['data'] ?? data['chat'] ?? [];

            if (chatData is List) {
              return chatData
                  .map((item) => RiwayatChatModel.fromJson(item))
                  .toList();
            }
          } else {
            LogService.log
                .w("API returned error: ${data['message'] ?? 'Unknown error'}");
            return [];
          }
        } else if (data is List) {
          // Direct list response
          return data.map((item) => RiwayatChatModel.fromJson(item)).toList();
        }
      }

      LogService.log.w(
          "Unexpected response format or status code: ${response.statusCode}");
      return [];
    } catch (e) {
      LogService.log.e("Error parsing riwayat chat: $e");
      rethrow;
    }
  }

  Future<HttpResponse> sendChat({
    required String idKoordinator,
    required String idLamaran,
    required String message,
  }) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("send Message : $idKoordinator, $idLamaran, $message");

      HttpResponse response = await _httpApiClient.post(
        "/chat.php",
        queryParameters: {"func": "send"},
        data: {
          "id_koordinator": idKoordinator,
          "id_lamar": idLamaran,
          "message": message
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("getRiwayat Chat response: ${response.data}");
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<Map<String, dynamic>> checkNotif({required String referenceId}) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getRiwayat Chat token: $token");

      HttpResponse response = await _httpApiClient.post(
        "/chat.php",
        queryParameters: {"func": "cekNotifChat"},
        data: {
          "reference_id": referenceId,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("getRiwayat Chat response: ${response.data}");
      return response.data;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<Map<String, dynamic>> autoRead({required String referenceId}) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      LogService.log.i("getRiwayat Chat token: $token");

      HttpResponse response = await _httpApiClient.post(
        "/chat.php",
        queryParameters: {"func": "autoRead"},
        data: {
          "reference_id": referenceId,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );

      LogService.log.i("getRiwayat Chat response: ${response.data}");
      return response.data;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
