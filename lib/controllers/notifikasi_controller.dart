import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/notifikasi_model.dart';
import 'package:digital_cv_mobile/services/notifikasi_service.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotifikasiController extends GetxController {
  final NotifikasiService notifikasiService = NotifikasiService();
  final snackbar = Get.find<SnackBarService>();
  final prefs = Get.find<SharedPreferences>();
  String pin = "";
  var isLoading = false.obs;
  final RxInt totalData = 0.obs;
  final RxInt totalPage = 0.obs;
  RxInt jmlNotifikasiRead = 0.obs;
  RxInt jmlNotifikasiUnread = 0.obs;
  RxInt jmlNewNotif = 0.obs;
  RxString title = "".obs;
  RxList<NotifikasiModel> notifikasiList = <NotifikasiModel>[].obs;

  final refreshNotifikasi = false.obs;

  void triggerRefreshNotifikasi() {
    refreshNotifikasi.toggle(); // ini akan memicu listener-nya
  }

  Future<List<NotifikasiModel>> getNotif(int page, int pageSize) async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoading(true);
      var response = await notifikasiService.getNotif(page, pageSize, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["status"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<NotifikasiModel> result = dataList
                  .map((item) => NotifikasiModel.fromJson(item))
                  .toList();
              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              // ✅ Hitung jumlah notifikasi yang sudah dibaca
              jmlNotifikasiRead.value =
                  result.where((item) => item.status == 'Dibaca').length;

// ✅ Hitung jumlah notifikasi yang belum dibaca
              jmlNotifikasiUnread.value =
                  result.where((item) => item.status != 'Dibaca').length;

              return result;
            } else {
              return [];
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} NotifikasiController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<void> countNewNotif() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoading(true);
      var response = await notifikasiService.countNotif(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["status"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              jmlNewNotif.value = responseData['total_notif'];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} NotifikasiController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateNotif(
      {required String penerima,
      required String pengirim,
      required String tgl}) async {
    try {
      isLoading(true);
      var response = await notifikasiService.updateNotif(
        pengirim: pengirim,
        penerima: penerima,
        tglKirim: tgl,
      );

      if (response.statusCode == 200) {
        getNotif(1, 10);
        countNewNotif();
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }
}
