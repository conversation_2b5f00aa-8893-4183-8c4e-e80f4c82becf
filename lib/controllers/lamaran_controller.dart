import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/lamaran_model.dart';
import 'package:digital_cv_mobile/models/timeline_model.dart';
import 'package:digital_cv_mobile/services/lamaran_service.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LamaranController extends GetxController {
  final LamaranService lamaranService = LamaranService();
  final snackbar = Get.find<SnackBarService>();
  final prefs = Get.find<SharedPreferences>();
  String pin = "";
  final RxBool isLoading = false.obs;
  final RxInt totalData = 0.obs;
  final RxInt totalPage = 0.obs;

  RxList<LamaranModel> lamaranList = <LamaranModel>[].obs;

  final refreshRiwayatLamar = false.obs;

  void triggerRefreshRiwayatLamar() {
    refreshRiwayatLamar.toggle(); // ini akan memicu listener-nya
  }

  Future<List<LamaranModel>> getRiwayatLamaran(int page, int pageSize, String q,
      {String? idLamar, String? idReq}) async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoading(true);
      var response = await lamaranService.getRiwayatLamaran(
          page, pageSize, pin, q,
          idLamar: idLamar, idReq: idReq);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LamaranModel> result =
                  dataList.map((item) => LamaranModel.fromJson(item)).toList();

              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              lamaranList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lamaranList.clear();
            return [];
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LamaranController");
          lamaranList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lamaranList.clear();
        return [];
      } else {
        LogService.log.e(
            "${"controller.server_error".tr}: ${response.statusCode}");
        lamaranList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lamaranList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<List<TimelineModel>> getTimeline(String idLamar) async {
    try {
      isLoading(true);
      var response = await lamaranService.getTimeline(idLamar);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<TimelineModel> result =
                  dataList.map((item) => TimelineModel.fromJson(item)).toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              lamaranList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lamaranList.clear();
            return [];
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LamaranController");
          lamaranList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lamaranList.clear();
        return [];
      } else {
        LogService.log.e(
            "${"controller.server_error".tr}: ${response.statusCode}");
        lamaranList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lamaranList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }
}
