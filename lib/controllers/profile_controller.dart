import 'dart:io';

import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/services/profile_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class ProfileController extends GetxController {
  final ProfileService profileService = ProfileService();
  final snackbar = Get.find<SnackBarService>();
  late FlutterSecureStorage prefs;
  String pin = "";
  RxString noTelp = "".obs;
  RxString visibilitas = "".obs;
  RxString statusKerja = "".obs;
  RxString rxEmail = "".obs;
  RxString rxImage = "".obs;
  var isLoading = false.obs;
  var isLoadingOTP = false.obs;
  var codeOtp = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    prefs = Get.find<FlutterSecureStorage>();
    loadNoTelpFromPrefs();
    updateFotoProfil();
  }

  void updateFotoProfil() async {
    String? image = await getFotoProfil();
    rxImage.value = image;
    await prefs.write(key: "image", value: image);
    var status = await prefs.read(key: "visibility");
    visibilitas.value = status ?? '';
    var statusKesedianKerja = await prefs.read(key: "status_pekerjaan");
    statusKerja.value = statusKesedianKerja ?? '';
  }

  void loadNoTelpFromPrefs() async {
    noTelp.value = await prefs.read(key: "no_telp") ?? '';
    rxEmail.value = await prefs.read(key: "email") ?? '';
    rxImage.value = await prefs.read(key: "image") ?? '';
  }

  Future<String> getFotoProfil() async {
    try {
      var response = await profileService.getFotoProfil();

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];

              return dataList.isNotEmpty ? dataList[0]["foto"] ?? "" : "";
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            LogService.log
                .e("Error: ${responseData["message"] ?? "Unknown error"}");
            return "";
          }
        } else {
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return "";
    } finally {}
  }

  Future<void> ubahEmail(String email, String otp) async {
    try {
      isLoading(true);
      // pin = prefs.getString("pin") ?? "";
      // print("Login Request: Username=$username, Password=$password");

      var response = await profileService.ubahEmail(email, otp);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_email_success".tr;
              await prefs.write(key: "email", value: email);
              rxEmail.value = email;
              Get.back();
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.change_email_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_email_success".tr;
              await prefs.write(key: "email", value: email);
              rxEmail.value = email;
              Get.back();
              snackbar.showSuccess(message);
            } else {
              if (responseData["message"] == "OTP Salah.") {
                snackbar.showError("controller.wrong_otp".tr);
              } else if (responseData["message"] == "Email sudah terdaftar.") {
                snackbar.showError("controller.email_is_exist".tr);
              } else {
                snackbar.showError("controller.change_email_failed".tr);
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} ProfileController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showSuccess(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> ubahVisibility(String status) async {
    try {
      isLoading(true);
      // pin = prefs.getString("pin") ?? "";
      // print("Login Request: Username=$username, Password=$password");
      Get.generalDialog(pageBuilder: (context, animation, secondaryAnimation) {
        return const Center(child: CircularProgressIndicator());
      });

      var response = await profileService.ubahVisibilitas(status);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_visibility_success".tr;
              await prefs.write(
                  key: "visibility",
                  value: responseData["data"]['visibilitas']);
              visibilitas.value = responseData["data"]['visibilitas'];
              Get.back();
              Get.back();
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.change_visibility_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_email_success".tr;
              await prefs.write(
                  key: "visibility",
                  value: responseData["data"]['visibilitas']);
              visibilitas.value = responseData["data"]['visibilitas'];
              Get.back();
              Get.back();
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.change_visibility_failed".tr);
            }
          }
        } else {
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showSuccess(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> ubahStatusKerja(String status) async {
    try {
      isLoading(true);
      // pin = prefs.getString("pin") ?? "";
      // print("Login Request: Username=$username, Password=$password");
      Get.generalDialog(pageBuilder: (context, animation, secondaryAnimation) {
        return const Center(child: CircularProgressIndicator());
      });

      var response = await profileService.ubahStatusKerja(status);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_status_kerja_success".tr;
              await prefs.write(
                  key: "status_pekerjaan",
                  value: responseData["data"]['status_pekerjaan']);
              statusKerja.value = responseData["data"]['status_pekerjaan'];
              Get.back();
              Get.back();
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.change_status_kerja_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_email_success".tr;
              await prefs.write(
                  key: "status_pekerjaan",
                  value: responseData["data"]['status_pekerjaan']);
              statusKerja.value = responseData["data"]['status_pekerjaan'];
              Get.back();
              Get.back();
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.change_status_kerja_failed".tr);
            }
          }
        } else {
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showSuccess(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> reqOtp(String email) async {
    try {
      isLoadingOTP(true);
      noTelp.value = await prefs.read(key: "no_telp") ?? "";

      var response =
          await profileService.reqOTPUpdateEmail(email, noTelp.value);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];
            if (responseData["message"] == "Email sudah terdaftar.") {
              snackbar.showError("controller.email_is_exist".tr);
            } else {
              snackbar.showSuccess("controller.register_success".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.otp_success".tr;
              snackbar.showSuccess(message);
            } else {
              if (responseData["message"] == "Email sudah terdaftar.") {
                snackbar.showError("controller.email_is_exist".tr);
              } else {
                snackbar.showError("controller.otp_failed".tr);
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} ProfileController");
        }
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      isLoadingOTP(false);
    } finally {
      isLoadingOTP(false);
    }
  }

  Future<void> ubahNoTelp(String noTelepon) async {
    try {
      isLoading(true);
      pin = await prefs.read(key: "pin") ?? "";
      // print("Login Request: Username=$username, Password=$password");

      var response = await profileService.ubahNoTelp(noTelepon, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_phone_success".tr;
              await prefs.write(key: "no_telp", value: noTelepon);
              noTelp.value = noTelepon;
              Get.back();
              snackbar.showSuccess(message);
            } else {
              if (responseData["message"] == "Nomor telepon sudah terdaftar.") {
                snackbar.showError("controller.noTelp_is_exist".tr);
              } else {
                snackbar.showError("controller.change_phone_failed".tr);
              }
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.change_phone_success".tr;
              await prefs.write(key: "no_telp", value: noTelepon);
              noTelp.value = noTelepon;
              Get.back();
              snackbar.showSuccess(message);
            } else {
              if (responseData["message"] == "Nomor telepon sudah terdaftar.") {
                snackbar.showError("controller.noTelp_is_exist".tr);
              } else {
                snackbar.showError("controller.change_phone_failed".tr);
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} ProfileController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showSuccess(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> uploadPhoto(File imageFile) async {
    try {
      isLoading(true);
      pin = await prefs.read(key: "pin") ?? "";

      final response = await profileService.uploadPhoto(imageFile, pin);

      if (response.statusCode == 200) {
        final data = response.data;

        if (data is Map<String, dynamic>) {
          final success = data["status"] == true;
          final message = "controller.upload_success".tr;

          if (success && data.containsKey("data")) {
            final dataList = data["data"];

            if (dataList is List && dataList.isNotEmpty) {
              final urlFoto = dataList[0]["urlFoto"] ?? "";

              // Simpan ke Rx dan SharedPreferences
              rxImage.value = urlFoto;
              await prefs.write(key: "image", value: urlFoto);

              Get.back();
              snackbar.showSuccess(message);
              return;
            }
          }

          // Jika tidak success atau data kosong
          snackbar.showError("controller.upload_failed".tr);
        } else {
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${data.runtimeType}");
          snackbar
              .showError("${"controller.invalid_format".tr} ProfileController");
        }
      } else if (response.statusCode == 400) {
        final errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }
}
