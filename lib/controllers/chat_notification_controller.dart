import 'dart:async';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/lamaran_model.dart';
import 'package:digital_cv_mobile/services/chat_service.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:get/get.dart';

class ChatNotificationController extends GetxController {
  // Services
  late WebSocketService _webSocketService;
  late LamaranController _lamaranController;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _notificationSubscription;

  // Observable notification counts
  final RxMap<String, int> notificationCounts = <String, int>{}.obs;
  final RxInt totalUnreadChats = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeNotificationListener();
    _startPeriodicNotificationCheck();
    _setupNotificationCountListener();
  }

  @override
  void onClose() {
    _notificationSubscription?.cancel();
    super.onClose();
  }

  void _initializeServices() {
    _webSocketService = Get.find<WebSocketService>();
    _lamaranController = Get.find<LamaranController>();
    LogService.log.i('Chat notification controller initialized');
  }

  void _initializeNotificationListener() {
    // Listen for notification updates from WebSocket
    _notificationSubscription = _webSocketService.notificationStream.listen(
      (notificationData) {
        LogService.log.i('Received notification update: $notificationData');
        _handleNotificationUpdate(notificationData);
      },
      onError: (error) {
        LogService.log.e('Error in notification stream: $error');
      },
    );
  }

  void _startPeriodicNotificationCheck() {
    // Check notifications every 30 seconds to keep data fresh
    Timer.periodic(Duration(seconds: 30), (timer) {
      if (!Get.isRegistered<ChatNotificationController>()) {
        timer.cancel();
        return;
      }

      LogService.log.i('🔄 Periodic notification check...');
      checkAndUpdateTotalUnreadChats();
    });
  }

  void _setupNotificationCountListener() {
    // Listen to changes in notificationCounts and auto-update totalUnreadChats
    ever(notificationCounts, (_) {
      _updateTotalUnreadCount();
    });
  }

  void _handleNotificationUpdate(Map<String, dynamic> notificationData) {
    try {
      final type = notificationData['type']?.toString();
      final data = notificationData['data'] ?? notificationData;

      switch (type) {
        case 'chat_notification':
          _handleChatNotification(data);
          break;
        case 'notification_update':
          _handleNotificationCountUpdate(data);
          break;
        default:
          _handleChatNotification(data);
      }
    } catch (e) {
      LogService.log.e('Error handling notification update: $e');
    }
  }

  void _handleChatNotification(Map<String, dynamic> data) {
    try {
      final idLamaran = data['id_lamaran']?.toString();
      final idKoordinator = data['id_koordinator']?.toString();
      final notifCount = data['notif_count'] ?? data['count'] ?? 1;
      final isChatActive = data['is_chat_active'] ?? false;
      final action = data['action']?.toString() ?? '';

      if (idLamaran != null) {
        final key = '${idLamaran}_${idKoordinator ?? ''}';

        LogService.log.i('🔔 Handling chat notification:');
        LogService.log.i('  - Key: $key');
        LogService.log.i('  - Action: $action');
        LogService.log.i('  - Chat active: $isChatActive');
        LogService.log.i('  - Notif count: $notifCount');
        LogService.log.i('  - Current count: ${notificationCounts[key] ?? 0}');

        switch (action) {
          case 'mark_as_read':
          case 'auto_read':
            // Explicitly mark as read, set count to 0
            notificationCounts[key] = 0;
            LogService.log.i('✅ Marked as read for $key, count set to 0');
            break;

          case 'new_message':
            // New message when chat is not active
            if (!isChatActive) {
              notificationCounts[key] = (notificationCounts[key] ?? 0) + 1;
              LogService.log.i(
                  '📱 New message for $key, count: ${notificationCounts[key]}');
            } else {
              // Safety check: if chat is active, don't increment
              notificationCounts[key] = 0;
              LogService.log
                  .i('⚠️ New message but chat active for $key, count set to 0');
            }
            break;

          default:
            // Legacy handling for backward compatibility
            if (isChatActive) {
              notificationCounts[key] = 0;
              LogService.log
                  .i('🔄 Legacy: Chat active for $key, count set to 0');
            } else {
              if (notifCount == 1 && notificationCounts.containsKey(key)) {
                notificationCounts[key] = (notificationCounts[key] ?? 0) + 1;
                LogService.log.i(
                    '🔄 Legacy: Incremented count for $key: ${notificationCounts[key]}');
              } else {
                notificationCounts[key] = notifCount;
                LogService.log.i('🔄 Legacy: Set count for $key: $notifCount');
              }
            }
        }

        // Update total unread count
        _updateTotalUnreadCount();

        // Trigger refresh of riwayat lamaran to update UI
        _triggerRiwayatRefresh();
      }
    } catch (e) {
      LogService.log.e('Error handling chat notification: $e');
    }
  }

  void _handleNotificationCountUpdate(Map<String, dynamic> data) {
    try {
      final updates = data['updates'] ?? [data];

      if (updates is List) {
        for (final update in updates) {
          if (update is Map<String, dynamic>) {
            final idLamaran = update['id_lamaran']?.toString();
            final idKoordinator = update['id_koordinator']?.toString();
            final notifCount = update['notif_count'] ?? update['count'] ?? 0;

            if (idLamaran != null) {
              final key = '${idLamaran}_${idKoordinator ?? ''}';
              notificationCounts[key] = notifCount;

              LogService.log
                  .i('Updated notification count for $key: $notifCount');
            }
          }
        }
      } else {
        _handleChatNotification(data);
      }

      _updateTotalUnreadCount();
      _triggerRiwayatRefresh();
    } catch (e) {
      LogService.log.e('Error handling notification count update: $e');
    }
  }

  void _updateTotalUnreadCount() async {
    final total =
        notificationCounts.values.fold<int>(0, (sum, count) => sum + count);
    totalUnreadChats.value = total;
    LogService.log.i('📊 Updated total unread chats (local): $total');
  }

  void _triggerRiwayatRefresh() async {
    // Trigger refresh of riwayat lamaran list
    _lamaranController.triggerRefreshRiwayatLamar();
    LogService.log.i('Triggered riwayat lamaran refresh');
  }

  /// Get notification count for specific lamaran
  int getNotificationCount(String idLamaran, [String? idKoordinator]) {
    final key = '${idLamaran}_${idKoordinator ?? ''}';
    // notificationCounts.clear();
    return notificationCounts[key] ?? 0;
  }

  /// Clear notification count for specific lamaran
  void clearNotificationCount(String idLamaran, [String? idKoordinator]) {
    final key = '${idLamaran}_${idKoordinator ?? ''}';
    notificationCounts[key] = 0;
    _updateTotalUnreadCount();
    LogService.log.i('Cleared notification count for $key');
  }

  /// Mark chat as read (clear notification)
  void markChatAsRead(String idLamaran, [String? idKoordinator]) {
    LogService.log.i('Marking chat as read: $idLamaran, $idKoordinator');

    clearNotificationCount(idLamaran, idKoordinator);

    // Send read receipt to server via WebSocket
    _sendReadReceipt(idLamaran, idKoordinator);

    // Update total unread count after marking as read
    _updateTotalUnreadCount();

    // Force UI refresh
    _triggerRiwayatRefresh();
  }

  void _sendReadReceipt(String idLamaran, [String? idKoordinator]) {
    try {
      if (_webSocketService.isConnected) {
        final readReceiptData = {
          'type': 'read_receipt',
          'data': {
            'id_lamaran': idLamaran,
            'id_koordinator': idKoordinator,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          }
        };

        // Send notification update to clear count
        final notificationUpdate = {
          'type': 'notification_update',
          'data': {
            'id_lamaran': idLamaran,
            'id_koordinator': idKoordinator,
            'notif_count': 0,
          }
        };

        // Handle the notification update locally
        _handleNotificationUpdate(notificationUpdate);

        LogService.log.i('Sent read receipt for lamaran: $idLamaran');
      } else {
        LogService.log.w('WebSocket not connected, cannot send read receipt');
      }
    } catch (e) {
      LogService.log.e('Error sending read receipt: $e');
    }
  }

  /// Initialize notification counts from lamaran list
  void initializeFromLamaranList(List<LamaranModel> lamaranList) {
    notificationCounts.clear();

    for (final lamaran in lamaranList) {
      if (lamaran.notifChatRef > 0) {
        final key = '${lamaran.idLamar}_${lamaran.idKoordinator}';
        notificationCounts[key] = lamaran.notifChatRef;
      }
    }

    _updateTotalUnreadCount();
    LogService.log.i(
        'Initialized notification counts from ${lamaranList.length} lamaran items');

    // Also check server for the most up-to-date total count
    checkAndUpdateTotalUnreadChats();
  }

  /// Check and update total unread chats from server
  Future<void> checkAndUpdateTotalUnreadChats() async {
    try {
      LogService.log.i('🔍 Checking total unread chats from server...');

      // Get any reference ID from current notification counts
      String? referenceId;
      if (notificationCounts.isNotEmpty) {
        // Use the first available lamaran ID as reference
        final firstKey = notificationCounts.keys.first;
        referenceId = firstKey.split('_').first;
      }

      if (referenceId != null) {
        final chatService = ChatService();
        final response = await chatService.checkNotif(referenceId: referenceId);

        final notifChatAll = response['data']['notifChatAll'];

        if (notifChatAll != null) {
          final totalCount = int.tryParse(notifChatAll.toString()) ?? 0;
          totalUnreadChats.value = totalCount;

          LogService.log
              .i('✅ Updated total unread chats from server: $totalCount');
        } else {
          LogService.log.w('⚠️ notifChatAll not found in response');
        }
      } else {
        LogService.log
            .i('ℹ️ No reference ID available for checking notifications');
      }
    } catch (e) {
      LogService.log.e('❌ Error checking total unread chats: $e');
    }
  }

  /// Check and update total unread chats for specific lamaran
  Future<void> checkAndUpdateTotalUnreadChatsForLamaran(
      String idLamaran) async {
    try {
      LogService.log
          .i('🔍 Checking total unread chats for lamaran: $idLamaran');

      final chatService = ChatService();
      final response = await chatService.checkNotif(referenceId: idLamaran);

      final notifChatAll = response['notifChatAll'];

      if (notifChatAll != null) {
        final totalCount = int.tryParse(notifChatAll.toString()) ?? 0;
        totalUnreadChats.value = totalCount;

        LogService.log.i(
            '✅ Updated total unread chats for lamaran $idLamaran: $totalCount');

        // Also trigger UI refresh
        _triggerRiwayatRefresh();
      } else {
        LogService.log
            .w('⚠️ notifChatAll not found in response for lamaran: $idLamaran');
      }
    } catch (e) {
      LogService.log
          .e('❌ Error checking total unread chats for lamaran $idLamaran: $e');
    }
  }

  /// Manually refresh total unread chats (can be called from UI)
  Future<void> refreshTotalUnreadChats() async {
    LogService.log.i('🔄 Manual refresh of total unread chats requested');

    // First update from local counts for immediate feedback
    _updateTotalUnreadCount();

    // Then sync with server for accuracy
    await checkAndUpdateTotalUnreadChats();
  }

  /// Force update totalUnreadChats from current notification counts
  void forceTotalUnreadChatsUpdate() {
    _updateTotalUnreadCount();
    LogService.log.i('🔄 Forced total unread chats update');
  }

  /// Auto read messages when user enters chat screen
  Future<void> autoReadMessages(String idLamaran) async {
    try {
      LogService.log.i('📖 Auto reading messages for lamaran: $idLamaran');

      final chatService = ChatService();
      final response = await chatService.autoRead(referenceId: idLamaran);

      if (response['status'] == true) {
        LogService.log.i('✅ Auto read successful for lamaran: $idLamaran');

        // Clear local notification count for this chat
        final key = '${idLamaran}_${currentIdKoordinator ?? ""}';
        if (notificationCounts.containsKey(key)) {
          final previousCount = notificationCounts[key] ?? 0;
          notificationCounts[key] = 0;

          // Update total unread count locally first for immediate UI feedback
          _updateTotalUnreadCount();

          LogService.log
              .i('🔔 Cleared $previousCount notifications for chat: $key');
        }

        // Refresh total count from server to ensure accurate sync
        await checkAndUpdateTotalUnreadChats();

        LogService.log.i('� Total unread chats updated after auto read');

        // Debug state after auto read
        debugTotalUnreadChats();
      } else {
        LogService.log.w(
            '⚠️ Auto read failed: ${response['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      LogService.log.e('❌ Error in auto read: $e');
    }
  }

  // Helper to store current coordinator ID for auto read
  String? currentIdKoordinator;

  /// Force update notification when chat screen state changes
  void updateChatScreenState(
      String idLamaran, String? idKoordinator, bool isActive) {
    LogService.log
        .i('Updating chat screen state: $idLamaran, active: $isActive');

    if (isActive) {
      // Store current coordinator ID for auto read
      currentIdKoordinator = idKoordinator;

      // Chat screen opened, clear notifications for this chat
      markChatAsRead(idLamaran, idKoordinator);

      // Auto read messages on server
      autoReadMessages(idLamaran);

      // Force update total unread chats for immediate UI feedback
      forceTotalUnreadChatsUpdate();
    } else {
      // Chat screen closed, just trigger refresh
      currentIdKoordinator = null;
      _triggerRiwayatRefresh();

      // Update total unread chats when leaving chat
      forceTotalUnreadChatsUpdate();
    }
  }

  /// Get current notification state for debugging
  Map<String, dynamic> getCurrentNotificationState() {
    final localTotal =
        notificationCounts.values.fold<int>(0, (sum, count) => sum + count);

    return {
      'notification_counts': Map<String, int>.from(notificationCounts),
      'total_unread_reactive': totalUnreadChats.value,
      'total_unread_calculated': localTotal,
      'counts_match': totalUnreadChats.value == localTotal,
      'current_coordinator': currentIdKoordinator,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Debug method to log current totalUnreadChats state
  void debugTotalUnreadChats() {
    final state = getCurrentNotificationState();
    LogService.log.i('🐛 DEBUG totalUnreadChats State:');
    LogService.log.i('   - Reactive Value: ${state['total_unread_reactive']}');
    LogService.log
        .i('   - Calculated Value: ${state['total_unread_calculated']}');
    LogService.log.i('   - Values Match: ${state['counts_match']}');
    LogService.log
        .i('   - Notification Counts: ${state['notification_counts']}');
    LogService.log
        .i('   - Current Coordinator: ${state['current_coordinator']}');
  }
}
