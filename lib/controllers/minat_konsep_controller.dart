import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/bahasa_model.dart';
import 'package:digital_cv_mobile/models/penguasaan_bahasa_model.dart';
import 'package:digital_cv_mobile/services/minat_konsep_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MinatKonsepController extends GetxController {
  final snackbar = Get.find<SnackBarService>();
  final MinatKonsepService minatKonsepService = MinatKonsepService();
  final RxBool isLoading = false.obs;
  final RxBool isLoadingBahasa = false.obs;
  final TextEditingController bahasaAsingController = TextEditingController();
  final TextEditingController kelebihanController = TextEditingController();
  final TextEditingController kekuranganController = TextEditingController();

  final RxString selectedValue = 'Tidak'.obs;
  final RxBool isStillWorking = false.obs;
  RxList<PenguasaanBahasaModel> bahasaAsing = <PenguasaanBahasaModel>[].obs;

  final RxString selectedBaca = "".obs;
  final RxString selectedTulis = "".obs;
  final RxString selectedDengar = "".obs;
  final RxString selectedBicara = "".obs;

  RxList<String> listKelebihan = <String>[].obs;
  RxList<String> listKekurangan = <String>[].obs;

  RxString selectedjmlOrangOption = "".obs;
  RxString selectedjPenilaianOption = "".obs;

  final RxList<String> penilaianOptions = [
    "presentasi.kurang".tr,
    "presentasi.cukup".tr,
    "presentasi.sangat".tr,
  ].obs;

  final RxList<String> penilaianDetailOptions = [
    "presentasi_detail.tidak".tr,
    "presentasi_detail.sangat".tr,
    "presentasi_detail.diperlukan".tr,
  ].obs;

  String getPenilaianText(String? value) {
    int index = (int.tryParse(value ?? "") ?? 1);
    if (index >= 0 && index < penilaianDetailOptions.length) {
      return penilaianDetailOptions[index];
    }
    return "-";
  }

  final RxList<String> jmlOrangOptions = [
    "dcv.minat.jml_org.opt1".tr,
    "dcv.minat.jml_org.opt2".tr,
    "dcv.minat.jml_org.opt3".tr,
    "dcv.minat.jml_org.opt4".tr,
    "dcv.minat.jml_org.opt5".tr,
  ].obs;

  String getMemimpinText(String? value) {
    int index = (int.tryParse(value ?? "") ?? 1) - 1;
    if (index >= 0 && index < jmlOrangOptions.length) {
      return jmlOrangOptions[index];
    }
    return "-";
  }

  final komputerisasiOptions = <int, String>{
    1: "dcv.minat.komputerisasi.opt1".tr,
    2: "dcv.minat.komputerisasi.opt2".tr,
    3: "dcv.minat.komputerisasi.opt3".tr,
    4: "dcv.minat.komputerisasi.opt4".tr,
    5: "dcv.minat.komputerisasi.opt5".tr,
    6: "dcv.minat.komputerisasi.opt6".tr,
  }.obs;
  RxMap<int, bool> selectedKomputerisasi = <int, bool>{}.obs;
  final selectedKomputerisasiId = (-1).obs;

// Inisialisasi di constructor/controller init:
  void initSelectedKomputerisasi() {
    komputerisasiOptions.forEach((key, _) {
      selectedKomputerisasi[key] = false;
    });
  }

  final lingkupPekerjaanOptions = <int, String>{}.obs;
  RxMap<int, bool> selectedRuangLingkup = <int, bool>{}.obs;

// Inisialisasi di constructor/controller init:
  void initSelectedRuangLingkup() {
    lingkupPekerjaanOptions.forEach((key, _) {
      selectedRuangLingkup[key] = false;
    });
  }

  Future<void> getRuangLingkup({List<int>? preselected}) async {
    try {
      isLoading(true);
      var response = await minatKonsepService.getRuangLingkup();

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData["success"] == true && responseData["data"] is List) {
          List dataList = responseData["data"];

          lingkupPekerjaanOptions.clear();

          for (var item in dataList) {
            int value = int.tryParse(item["value"].toString()) ?? 0;
            String name = item["name"] ?? "";
            lingkupPekerjaanOptions[value] = name;
            // lingkupPekerjaanOptions[value] =
            //     TranslationService.translateFromValue(
            //         name, "dcv.minat.ruang_lingkup");
          }
          initSelectedRuangLingkup();

          // ✅ Set nilai yang terceklis
          if (preselected != null) {
            for (var id in preselected) {
              if (selectedRuangLingkup.containsKey(id)) {
                selectedRuangLingkup[id] = true;
              }
            }
          }
        } else {
          // snackbar.showError("controller.data_not_found".tr);
        }
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  RxList<BahasaModel> bahasaListAll = <BahasaModel>[].obs;
  Future<void> getBahasa(String search) async {
    try {
      isLoading(true);
      var response = await minatKonsepService.getBahasa(search, "1", "50");

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<BahasaModel> result =
                  dataList.map((item) => BahasaModel.fromJson(item)).toList();
              bahasaListAll.assignAll(result);
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              bahasaListAll.clear();
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            bahasaListAll.clear();
          }
        } else {
          bahasaListAll.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        bahasaListAll.clear();
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        bahasaListAll.clear();
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      bahasaListAll.clear();
    } finally {
      isLoading(false);
    }
  }

  void addBahasa() {
    String newItem = bahasaAsingController.text.trim();
    if (newItem.isNotEmpty &&
        selectedBaca.value.isNotEmpty &&
        selectedTulis.value.isNotEmpty &&
        selectedDengar.value.isNotEmpty &&
        selectedBicara.value.isNotEmpty) {
      PenguasaanBahasaModel penguasaanBahasaModel = PenguasaanBahasaModel(
          id: '',
          bahasa: newItem,
          membaca: selectedBaca.value,
          menulis: selectedTulis.value,
          mendengar: selectedDengar.value,
          berbicara: selectedBicara.value);
      bahasaAsing.add(penguasaanBahasaModel);
      selectedBaca.value = "";
      selectedTulis.value = "";
      selectedDengar.value = "";
      selectedBicara.value = "";
      bahasaAsingController.clear();
      update(); // Pastikan UI diperbarui
    }
  }

  void deleteBahasa(int index) {
    bahasaAsing.removeAt(index);
    update();
  }

  void addKelebihan() {
    String newItem = kelebihanController.text.trim();
    if (newItem.isNotEmpty) {
      listKelebihan.add(newItem);
      kelebihanController.clear();
      update();
    }
  }

  void deleteKelebihan(int index) {
    listKelebihan.removeAt(index);
    update();
  }

  void addKekurangan() {
    String newItem = kekuranganController.text.trim();
    if (newItem.isNotEmpty) {
      listKekurangan.add(newItem);
      kekuranganController.clear();
      update();
    }
  }

  void deleteKekurangan(int index) {
    listKekurangan.removeAt(index);
    update();
  }

  @override
  void onClose() {
    bahasaAsingController.dispose();
    kelebihanController.dispose();
    kekuranganController.dispose();
    super.onClose();
  }
}
