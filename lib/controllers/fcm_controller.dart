import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/services/auth_service.dart';
import 'package:digital_cv_mobile/services/fcm_service.dart';
import 'package:digital_cv_mobile/views/verikasi_akun.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FCMController extends GetxController {
  final snackbar = Get.find<SnackBarService>();
  final FCMService authService = FCMService();
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;
  final prefs = Get.find<SharedPreferences>();
  final secureStorage = Get.find<FlutterSecureStorage>();
  String pin = "";
  String model = "";
  String deviceId = "";
  String ip = "";
  late Position koordinat;

  Future<void> updateToken({required String fcmToken}) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: 'pin') ?? "";

      var response = await authService.updateToken(pin, fcmToken);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          final status = responseData["status"];
          final message = responseData["message"];

          if (status == true) {
            debugPrint("$message");
          } else {
            debugPrint("$message");
          }
        } else {
          debugPrint("Gagal update token!");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        debugPrint("Gagal update token!");
      }
    } catch (e) {
      debugPrint("Gagal update token! ${e.toString()}");
    } finally {
      isLoading(false);
    }
  }

  void checkStatus() async {
    var email = await secureStorage.read(key: 'email') ?? "";
    var response = await AuthService().checkStatusVerifikasi(email: email);
    if (response.data['data']['status'] == '') {
      Get.offAll(() => VerikasiAkun(email: email));
    }
  }
}
