import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:digital_cv_mobile/bindings/auth_binding.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/recaptcha_config.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/user_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/services/auth_service.dart';
import 'package:digital_cv_mobile/services/recaptcha_service.dart';
import 'package:digital_cv_mobile/services/security_service.dart';
import 'package:digital_cv_mobile/services/storage_auth_service.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:digital_cv_mobile/views/daftar_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginController extends GetxController {
  final snackbar = Get.find<SnackBarService>();
  final AuthService authService = AuthService();
  final RecaptchaService recaptchaService = Get.put(RecaptchaService());
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final prefs = Get.find<SharedPreferences>();
  String pin = "";
  String model = "";
  String deviceId = "";
  String ip = "";
  late Position koordinat;

  // Method untuk sign in dengan Apple
  Future<UserCredential?> signInWithApple() async {
    try {
      isLoading.value = true;
      LogService.log.i("Starting Apple Sign In process...");

      // Check platform and availability
      if (!Platform.isIOS && !Platform.isMacOS) {
        LogService.log
            .e("Sign In with Apple is only supported on iOS and macOS");
        snackbar.showError(
            "Sign In with Apple is only available on iOS and macOS devices");
        return null;
      }

      // Check if Sign In with Apple is available
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        LogService.log.e("Sign In with Apple is not available on this device");
        // snackbar
        //     .showError("Sign In with Apple is not available on this device");
        return null;
      }

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.gestaltsystech.digitalCvCandidate',
          redirectUri: Uri.parse(
              'https://digitalcv-2879c.firebaseapp.com/__/auth/handler'),
        ),
      ).timeout(
        const Duration(minutes: 1),
        onTimeout: () {
          throw TimeoutException('Sign In with Apple timed out');
        },
      );

      // Create an OAuthCredential from the credential
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: credential.identityToken,
        accessToken: credential.authorizationCode,
      );

      // Sign in dengan Firebase
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInWithCredential(oauthCredential);

      // Get user info
      final user = userCredential.user;
      if (user != null) {
        // Login ke backend dengan email dari Apple
        LogService.log.i("Apple Sign In successful: ${user.email}, $user");
        await loginApple(
          user.email ?? '',
          user.email ?? '',
        );
      }

      return userCredential;
    } catch (e) {
      LogService.log.i("❌ Error during Apple Sign In: $e");
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Method untuk login ke backend dengan credentials dari Apple
  Future<void> loginApple(String email, String name) async {
    try {
      isLoading(true);
      // print("Login Request: Username=$username, Password=$password");

      var response = await authService.loginWithApple(email, name);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            if (responseData["success"] == true) {
              List dataList = responseData["data"];
              final List<UserModel> result =
                  dataList.map((item) => UserModel.fromJson(item)).toList();

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              await StorageAuthService.saveLoginDataGoogle(result);
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            }
          } else {
            if (responseData["success"] == true) {
              final List<UserModel> result = [];

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              await StorageAuthService.saveLoginDataGoogle(result);
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LoginController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  // Login rate limiting variables
  final RxInt loginAttempts = 0.obs;
  final RxBool isLocked = false.obs;
  final RxInt lockoutTimeRemaining = 0.obs;
  static const int maxLoginAttempts = 5;
  static const int lockoutDuration = 60; // 1 minute in seconds
  Timer? _lockoutTimer;

  Future<String> getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.name;
    }
    return 'Unknown Model';
  }

  Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ?? "IOS Id Unknown";
    }
    return 'Unknown Id';
  }

  Future<String> getIpAddress() async {
    NetworkInfo networkInfo = NetworkInfo();
    String wifiIp = await networkInfo.getWifiIP() ?? "IP Unknown";
    return wifiIp;
  }

  // Login attempt management functions
  @override
  void onInit() {
    super.onInit();
    _loadLoginAttempts();
  }

  @override
  void onClose() {
    _lockoutTimer?.cancel();
    super.onClose();
  }

  /// Load login attempts from SharedPreferences
  Future<void> _loadLoginAttempts() async {
    final attempts = prefs.getInt('login_attempts') ?? 0;
    final lockoutEndTime = prefs.getInt('lockout_end_time') ?? 0;

    loginAttempts.value = attempts;

    if (lockoutEndTime > DateTime.now().millisecondsSinceEpoch) {
      final remainingTime =
          ((lockoutEndTime - DateTime.now().millisecondsSinceEpoch) / 1000)
              .ceil();
      isLocked.value = true;
      lockoutTimeRemaining.value = remainingTime;
      _startLockoutTimer();
    }
  }

  /// Save login attempts to SharedPreferences
  Future<void> _saveLoginAttempts() async {
    await prefs.setInt('login_attempts', loginAttempts.value);
  }

  /// Start lockout timer
  void _startLockoutTimer() {
    _lockoutTimer?.cancel();
    _lockoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (lockoutTimeRemaining.value <= 1) {
        timer.cancel();
        _resetLoginAttempts();
      } else {
        lockoutTimeRemaining.value--;
      }
    });
  }

  /// Reset login attempts
  Future<void> _resetLoginAttempts() async {
    loginAttempts.value = 0;
    isLocked.value = false;
    lockoutTimeRemaining.value = 0;
    await prefs.remove('login_attempts');
    await prefs.remove('lockout_end_time');
  }

  /// Handle failed login attempt
  Future<void> _handleFailedLogin() async {
    loginAttempts.value++;
    await _saveLoginAttempts();

    if (loginAttempts.value >= maxLoginAttempts) {
      isLocked.value = true;
      lockoutTimeRemaining.value = lockoutDuration;

      final lockoutEndTime =
          DateTime.now().millisecondsSinceEpoch + (lockoutDuration * 1000);
      await prefs.setInt('lockout_end_time', lockoutEndTime);

      _startLockoutTimer();

      LogService.log.w(
          "Akun terkunci karena terlalu banyak percobaan login gagal. Coba lagi dalam $lockoutDuration detik.");
    } else {
      final remaining = maxLoginAttempts - loginAttempts.value;
      LogService.log.w("Login failed. Remaining attempts: $remaining");
    }
  }

  /// Check if login is allowed
  bool _isLoginAllowed() {
    if (isLocked.value) {
      final minutes = (lockoutTimeRemaining.value / 60).ceil();
      final seconds = lockoutTimeRemaining.value % 60;
      LogService.log.w(
          "Akun terkunci karena terlalu banyak percobaan login gagal. Coba lagi dalam ${minutes > 0 ? '${minutes}m ' : ''}${seconds}s");
      return false;
    }
    return true;
  }

  Future<void> login(String email, String password) async {
    try {
      isLoading(true);
      // 🔒 Enhanced Security Check using SecurityService
      SecurityCheckResult securityCheck =
          await SecurityService.checkLoginAllowed(email);

      if (!securityCheck.allowed) {
        String message = securityCheck.message;
        if (securityCheck.remainingTime != null) {
          message += " (${securityCheck.remainingTime} menit)";
        }
        snackbar.showError(message);
        LogService.log.w("Login blocked: ${securityCheck.reason} - $message");
        return;
      }

      // Log remaining attempts if available
      if (securityCheck.remainingAttempts != null &&
          securityCheck.remainingAttempts! > 0) {
        LogService.log
            .i("Remaining login attempts: ${securityCheck.remainingAttempts}");
      }

      // 🔒 Implementasi reCAPTCHA v3 - Dapatkan token sebelum login
      String? recaptchaToken;

      // Periksa apakah user memerlukan verifikasi reCAPTCHA
      // Trigger reCAPTCHA setelah attempt pertama untuk keamanan maksimal
      bool needsRecaptcha =
          true; // Always require reCAPTCHA for maximum security

      if (needsRecaptcha) {
        RecaptchaConfigHelper.logEvent(
            "reCAPTCHA verification required for login");
        recaptchaToken = await recaptchaService.getRecaptchaToken(
            action: RecaptchaConfig.actionLogin);

        if (recaptchaToken == null || recaptchaToken.isEmpty) {
          snackbar.showError(RecaptchaConfig.verificationRequiredMessage);
          return;
        }

        // Verifikasi token reCAPTCHA
        bool isValidToken = await recaptchaService.verifyRecaptchaToken(
            recaptchaToken,
            action: RecaptchaConfig.actionLogin);

        if (!isValidToken) {
          snackbar.showError(RecaptchaConfig.verificationFailedMessage);
          await SecurityService.recordFailedAttempt(email);
          return;
        }

        RecaptchaConfigHelper.logEvent("reCAPTCHA verification successful");
      }

      var response = await authService.login(email, password);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            if (responseData["success"] == true) {
              List dataList = responseData["data"];
              final List<UserModel> result =
                  dataList.map((item) => UserModel.fromJson(item)).toList();

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              // Record successful login
              await SecurityService.recordSuccessfulLogin(email);

              await StorageAuthService.saveLoginData(result);
              await Get.put(WebSocketService()).connect();
              LogService.log.i("Login successful: $result");
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            } else {
              LogService.log.e("Message : ${responseData["message"]}");
              await SecurityService.recordFailedAttempt(email);
              if (responseData["message"].contains("Password salah!")) {
                snackbar.showError("controller.wrong_pass".tr);
              } else if (responseData["message"]
                  .contains("Email belum terdaftar.")) {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.login_failed".tr);
              }
            }
          } else {
            if (responseData["success"] == true) {
              final List<UserModel> result = [];

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              // Record successful login
              await SecurityService.recordSuccessfulLogin(email);

              await StorageAuthService.saveLoginData(result);
              await Get.put(WebSocketService()).connect();
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            } else {
              LogService.log.e("Message : ${responseData["message"]}");
              await SecurityService.recordFailedAttempt(email);
              if (responseData["message"] == "Password salah!") {
                snackbar.showError("controller.wrong_pass".tr);
              } else if (responseData["message"] == "Email belum terdaftar.") {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.login_failed".tr);
              }
            }
          }
        } else {
          // Handle failed login
          await SecurityService.recordFailedAttempt(email);
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        await SecurityService.recordFailedAttempt(email);
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      } else {
        await SecurityService.recordFailedAttempt(email);
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      await SecurityService.recordFailedAttempt(email);
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<GoogleSignInAccount?> signInWithGoogle() async {
    try {
      isLoading(true);
      LogService.log.i("🔄 Memulai proses login...");

      // // 🔒 Implementasi reCAPTCHA v3 untuk Google Sign In
      // String? recaptchaToken;

      // // Periksa apakah user memerlukan verifikasi reCAPTCHA untuk Google login
      // bool needsRecaptcha = RecaptchaConfigHelper.shouldTriggerForGoogleLogin(
      //     loginAttempts.value);

      // if (needsRecaptcha) {
      //   RecaptchaConfigHelper.logEvent(
      //       "reCAPTCHA verification required for Google login");
      //   recaptchaToken = await recaptchaService.getRecaptchaToken(
      //       action: RecaptchaConfig.actionGoogleLogin);

      //   if (recaptchaToken == null || recaptchaToken.isEmpty) {
      //     snackbar.showError(RecaptchaConfig.verificationRequiredMessage);
      //     return null;
      //   }

      //   // Verifikasi token reCAPTCHA
      //   bool isValidToken = await recaptchaService.verifyRecaptchaToken(
      //       recaptchaToken,
      //       action: RecaptchaConfig.actionGoogleLogin);

      //   if (!isValidToken) {
      //     snackbar.showError(RecaptchaConfig.verificationFailedMessage);
      //     return null;
      //   }

      //   RecaptchaConfigHelper.logEvent(
      //       "reCAPTCHA verification successful for Google login");
      // }

      await _googleSignIn.signOut(); // agar bisa pilih akun lagi
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        LogService.log.i("⚠️ Login dibatalkan oleh pengguna.");
        return null;
      }

      // Jika kamu butuh token untuk akses Google API:
      await googleUser.authentication;

      return googleUser;
    } catch (e) {
      LogService.log.e("Google Sign-In error: $e");
      return null;
    } finally {
      isLoading(false);
    }
  }

  Future<void> loginGoogle(String email, String nama) async {
    try {
      isLoading(true);
      // print("Login Request: Username=$username, Password=$password");
      LogService.log.i("Login Google Request: Email=$email, Nama=$nama");

      var response = await authService.loginGoogle(email);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            if (responseData["success"] == true) {
              List dataList = responseData["data"];
              final List<UserModel> result =
                  dataList.map((item) => UserModel.fromJson(item)).toList();

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              await StorageAuthService.saveLoginData(result);
              await Get.put(WebSocketService()).connect();
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            } else {
              if (responseData["message"] == "Email belum terdaftar.") {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.login_failed".tr);
              }

              Get.to(
                  DaftarScreen(
                    email: email,
                    nama: nama,
                  ),
                  binding: AuthBinding());
            }
          } else {
            if (responseData["success"] == true) {
              final List<UserModel> result = [];

              String message = "controller.login_success".tr;
              snackbar.showSuccess(message);

              await StorageAuthService.saveLoginData(result);
              await Get.put(WebSocketService()).connect();
              isLoggedIn(true);

              Get.offAllNamed(Routes.home);
            } else {
              if (responseData["message"] == "Email belum terdaftar.") {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.login_failed".tr);
              }

              Get.to(
                  DaftarScreen(
                    email: email,
                    nama: nama,
                  ),
                  binding: AuthBinding());
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LoginController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> logout() async {
    // Tampilkan loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false, // Mencegah user menutup dialog secara manual
    );

    final prefs = await SharedPreferences.getInstance();
    final isGoogle = prefs.getBool("isGoogle") ?? false;

    if (isGoogle) {
      LogService.log.i("🔄 Logout dari Google...");
      await GoogleSignIn().signOut();
    }

    LogService.log.i("🔄 Logout dari Firebase...");
    await FirebaseAuth.instance.signOut();

    var response = await AuthService().logout();

    if (response.statusCode == 200) {
      StorageAuthService.clearLoginData();
      WebSocketService().disableAutoConnect();
      await prefs.clear();
      isLoggedIn.value = false;
      Get.back(); // Tutup loading dialog
      snackbar.showSuccess("controller.logout_success".tr);
      Get.offAllNamed('/login');
    } else if (response.statusCode == 401) {
      StorageAuthService.clearLoginData();
      await prefs.clear();
      isLoggedIn.value = false;
      Get.back(); // Tutup loading dialog
      snackbar.showSuccess("controller.logout_success".tr);
      Get.offAllNamed('/login');
    }
  }

  Future<void> reqResetPassword(String email) async {
    try {
      isLoading(true);
      // print("Login Request: Username=$username, Password=$password");

      var response = await authService.reqResetPassword(email);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.check_email".tr;
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "Email tidak terdaftar.") {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.req_pass_failed".tr);
              }
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.check_email".tr;
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "Email tidak terdaftar.") {
                snackbar.showError("controller.email_not_found".tr);
              } else {
                snackbar.showError("controller.req_pass_failed".tr);
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LoginController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> resetPassword(String password, String token) async {
    try {
      isLoading(true);
      // print("Login Request: Username=$username, Password=$password");

      var response = await authService.resetPassword(password, token);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.reset_pass_success".tr;
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "Reset password gagal.") {
                snackbar.showError("controller.reset_pass_failed".tr);
              } else {
                snackbar.showError(TranslationService.translateBetweenLangs(
                  responseData["message"],
                  "controller",
                  "id",
                  Get.locale?.languageCode.toLowerCase() ?? "id",
                ));
              }
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.reset_pass_success".tr;
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "Reset password gagal.") {
                snackbar.showError("controller.reset_pass_failed".tr);
              } else {
                snackbar.showError(TranslationService.translateBetweenLangs(
                  responseData["message"],
                  "controller",
                  "id",
                  Get.locale?.languageCode.toLowerCase() ?? "id",
                ));
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} LoginController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> changePassword(String passLama, String passBaru,
      String konfirmPassBaru, String otp) async {
    try {
      isLoading(true);
      pin = prefs.getString("pin") ?? "";

      var response = await authService.changePassword(
          pin, passLama, passBaru, konfirmPassBaru, otp);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("success") &&
                responseData["success"] == "success") {
              String message = "controller.change_pass_success".tr;
              Get.back();
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
            } else {
              if (responseData["message"] ==
                      "Update kata sandi gagal dilakukan." ||
                  responseData["message"] == "Update kata sandi gagal!") {
                snackbar.showError("controller.change_pass_failed".tr);
              } else {
                snackbar.showError(TranslationService.translateBetweenLangs(
                    responseData["message"],
                    "controller",
                    "id",
                    Get.locale?.languageCode.toLowerCase() ?? ""));
              }
            }
          } else {
            if (responseData.containsKey("success") &&
                responseData["success"] == "success") {
              String message = "controller.change_pass_success".tr;
              Get.back();
              snackbar.showSuccess(message);
              // snackbar.showSuccess("Login successful");
            } else {
              if (responseData["message"] ==
                      "Update kata sandi gagal dilakukan." ||
                  responseData["message"] == "Update kata sandi gagal!") {
                snackbar.showError("controller.change_pass_failed".tr);
              } else {
                snackbar.showError(TranslationService.translateBetweenLangs(
                    responseData["message"],
                    "controller",
                    "id",
                    Get.locale?.languageCode.toLowerCase() ?? ""));
              }
            }
          }
        } else {
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  void saveCredentials(String email, String password) async {
    // final prefs = await SharedPreferences.getInstance();
    // await prefs.setString('email', email);
    // await prefs.setString('password', password);
    final prefs = FlutterSecureStorage();

    // 🔒 Hash password before saving to local storage
    String hashedPassword =
        PasswordSecurity.hashPasswordForTransmission(password, email);

    await prefs.write(key: 'email', value: email);
    await prefs.write(
        key: 'password_hash', value: hashedPassword); // Save hashed password
    await prefs.write(
        key: 'original_password',
        value: password); // Keep original for UI purposes only
  }

  void clearCredentials() async {
    final prefs = FlutterSecureStorage();
    await prefs.delete(key: 'email');
    await prefs.delete(key: 'password');
    await prefs.delete(key: 'password_hash'); // Clear hashed password
    await prefs.delete(key: 'original_password'); // Clear original password
  }

  Future<Map<String, String>> getSavedCredentials() async {
    final prefs = FlutterSecureStorage();
    String? email = await prefs.read(key: 'email');
    String? password = await prefs.read(
        key: 'original_password'); // Use original password for UI
    String? hashedPassword =
        await prefs.read(key: 'password_hash'); // Keep hashed for security

    if (email != null && password != null) {
      return {
        'email': email,
        'password': password, // Return original password for UI
        'password_hash': hashedPassword ?? '', // Include hash for internal use
      };
    } else {
      return {};
    }
  }
}
