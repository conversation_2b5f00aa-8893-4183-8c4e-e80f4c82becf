import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/services/apply_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class ApplyController extends GetxController {
  final ApplyService applyService = ApplyService();
  final snackbar = Get.find<SnackBarService>();
  late FlutterSecureStorage prefs;
  final secureStorage = Get.put(FlutterSecureStorage());
  String pin = "";
  RxString noTelp = "".obs;
  RxString rxEmail = "".obs;
  RxString rxImage = "".obs;
  var isLoading = false.obs;
  RxBool rxConfirm = false.obs;

  final formKey = GlobalKey<FormState>();
  final List<TextEditingController> controllers = [];
  final List<String> kkList = [];
  RxList<String> jawabanKhusus = <String>[].obs;

  void initKkList(List<String> newKkList) {
    kkList.clear();
    kkList.addAll(newKkList);
    kkList.removeWhere((item) => item.trim().isEmpty);
    // Buat controller sebanyak item di kkList
    controllers.clear();
    controllers
        .addAll(List.generate(kkList.length, (_) => TextEditingController()));
  }

  @override
  void onClose() {
    // Jangan lupa dispose semua controller
    for (final c in controllers) {
      c.dispose();
    }
    super.onClose();
  }

  @override
  void onInit() {
    super.onInit();
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    prefs = FlutterSecureStorage();
    await loadNoTelpFromPrefs();
  }

  Future<void> loadNoTelpFromPrefs() async {
    noTelp.value = await prefs.read(key: "no_telp") ?? "";
    rxEmail.value = await prefs.read(key: "email") ?? "";
    rxImage.value = await prefs.read(key: "image") ?? "";
  }

  Future<void> applyLowongan(
    String idReq,
    String lokasi,
    List<String> pertanyaanKhusus,
  ) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: 'pin') ?? "";

      var response = await applyService.applyLowongan(
          idReq, lokasi, pertanyaanKhusus, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.apply_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.apply_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.apply_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.apply_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr}ApplyController");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
    } finally {
      isLoading(false);
    }
  }
}
