import 'dart:async';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/chat_notification_controller.dart';
import 'package:digital_cv_mobile/helpers/chat_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/chat_message_model.dart';
import 'package:digital_cv_mobile/models/riwayat_chat_model.dart';
import 'package:digital_cv_mobile/services/chat_service.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class ChatMessageController extends GetxController {
  final messages = <dynamic>[].obs;
  final chatMessages = <ChatMessage>[].obs;
  final messageInput = TextEditingController();
  final RxList<ChatMessage> systemMessages = <ChatMessage>[].obs;
  final RxList<RiwayatChatModel> riwayatChatHistory = <RiwayatChatModel>[].obs;
  final RxBool isLoadingHistory = false.obs;
  final RxBool isSendingMessage = false.obs;

  // Chat parameters
  String? currentIdKoordinator;
  String? currentIdLamaran;

  // Services
  late WebSocketService _webSocketService;
  late ChatService _chatService;
  StreamSubscription<ChatMessage>? _messageSubscription;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeWebSocket();
  }

  void _initializeServices() {
    _chatService = ChatService();
    LogService.log.i('Chat service initialized');
  }

  @override
  void onClose() {
    // Notify notification controller that chat screen is closing
    if (currentIdLamaran != null) {
      try {
        final notificationController = Get.find<ChatNotificationController>();
        notificationController.updateChatScreenState(
          currentIdLamaran!,
          currentIdKoordinator,
          false,
        );
      } catch (e) {
        LogService.log
            .w('Could not find ChatNotificationController during close: $e');
      }
    }

    _messageSubscription?.cancel();
    super.onClose();
  }

  void _initializeWebSocket() {
    // Get existing WebSocket service instance
    try {
      _webSocketService = Get.find<WebSocketService>();
      LogService.log.i('Using existing WebSocket service');
    } catch (e) {
      // If not found, create new instance
      _webSocketService = Get.put(WebSocketService());
      LogService.log.i('Created new WebSocket service');
    }

    // Cancel any existing subscription
    _messageSubscription?.cancel();

    // Listen for incoming messages
    _messageSubscription = _webSocketService.messageStream.listen(
      (ChatMessage message) {
        LogService.log
            .i("✅ LISTENER RECEIVED: ${message.sender}: ${message.message}");

        // Add all incoming messages (including from other users and server responses)
        // But avoid duplicating messages we just sent
        bool isDuplicate = chatMessages.any((existingMessage) =>
            existingMessage.id == message.id &&
            existingMessage.timestamp
                    .difference(message.timestamp)
                    .abs()
                    .inSeconds <
                2);

        if (!isDuplicate) {
          chatMessages.add(message);
          updateFlatChatList();
          LogService.log.i("✅ ADDED TO CHAT: ${message.message}");
        } else {
          LogService.log.i("⚠️ SKIPPED DUPLICATE: ${message.message}");
        }
      },
      onError: (error) {
        LogService.log.e('❌ LISTENER ERROR: $error');
      },
      onDone: () {
        LogService.log.w('⚠️ LISTENER DONE - Stream closed');
      },
    );
  }

  void sendMessage(String text) {
    if (text.trim().isEmpty) return;
    if (isSendingMessage.value) return; // Prevent double sending

    // Validate message for XSS and malicious content
    if (containsMaliciousContent(text)) {
      _showFailedToSendMessage();
      return;
    }

    // Create candidate message
    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      sender: "candidate",
      message: text,
      timestamp: DateTime.now(),
    );

    // Add to local chat messages immediately for better UX
    chatMessages.add(userMessage);
    messageInput.clear();
    updateFlatChatList();

    // Send message via multiple channels
    _sendMessageToServer(text, userMessage);
  }

  Future<void> _sendMessageToServer(
      String text, ChatMessage userMessage) async {
    isSendingMessage.value = true;

    try {
      // 1. Send via HTTP API (primary method)
      if (currentIdKoordinator != null && currentIdLamaran != null) {
        await _sendViaHttpApi(text);
        LogService.log.i('✅ Message sent via HTTP API');

        // Auto read messages after sending (user is actively chatting)
        try {
          final notificationController = Get.find<ChatNotificationController>();
          await notificationController.autoReadMessages(currentIdLamaran!);
        } catch (e) {
          LogService.log.w('Could not auto read after sending message: $e');
        }
      } else {
        LogService.log.w('⚠️ Missing chat parameters for HTTP API');
      }

      // 2. Send via WebSocket (secondary/real-time method)
      if (_webSocketService.isConnected) {
        _webSocketService.sendMessage(userMessage);
        LogService.log.i('✅ Message sent via WebSocket');
      } else {
        _initializeWebSocket();
        LogService.log.w('⚠️ WebSocket not connected');
      }
    } catch (e) {
      LogService.log.e('❌ Error sending message: $e');
    } finally {
      isSendingMessage.value = false;
    }
  }

  Future<void> _sendViaHttpApi(String message) async {
    if (currentIdKoordinator == null || currentIdLamaran == null) {
      throw 'Missing required parameters: idKoordinator or idLamaran';
    }

    final response = await _chatService.sendChat(
      idKoordinator: currentIdKoordinator!,
      idLamaran: currentIdLamaran!,
      message: message,
    );

    LogService.log.i('HTTP API response: ${response.statusCode}');

    if (response.statusCode != 200) {
      throw 'Server returned error: ${response.statusCode}';
    }
  }

  void updateFlatChatList() async {
    // Combine all messages: system messages + regular chat messages
    List<ChatMessage> allMessages = [];
    allMessages.addAll(systemMessages);
    allMessages.addAll(chatMessages);

    // Sort by timestamp to maintain chronological order
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    Map<String, List<ChatMessage>> groupedMessages =
        ChatHelper.groupMessagesByDate(allMessages);
    messages.value = ChatHelper.createFlatListWithSeparators(groupedMessages);
  }

  // WebSocket status getters
  WebSocketStatus get webSocketStatus => _webSocketService.status;
  Rx<WebSocketStatus> get webSocketStatusObservable =>
      _webSocketService.statusObservable;
  String get webSocketErrorMessage => 'No error message available';
  bool get isWebSocketConnected => _webSocketService.isConnected;

  // Manual reconnect method
  void reconnectWebSocket() {
    _webSocketService.reconnect();
  }

  void forceReconnect() {
    LogService.log.i('🔄 Force reconnecting WebSocket...');
    _webSocketService.reconnect();

    // Reinitialize listener after reconnection
    Future.delayed(const Duration(seconds: 2), () {
      _initializeWebSocket();
    });
  }

  Future<void> scanAndConnect() async {
    LogService.log.i('🔄 Reconnecting WebSocket...');
    _webSocketService.reconnect();

    // Reinitialize listener
    Future.delayed(const Duration(seconds: 2), () {
      _initializeWebSocket();
    });
  }

  /// Set chat parameters for sending messages
  void setChatParameters({
    required String idKoordinator,
    required String idLamaran,
  }) {
    currentIdKoordinator = idKoordinator;
    currentIdLamaran = idLamaran;
    LogService.log.i(
        'Chat parameters set: koordinator=$idKoordinator, lamaran=$idLamaran');
  }

  /// Load chat history from server
  Future<void> loadRiwayatChat({
    required String idKoordinator,
    required String idLamaran,
  }) async {
    try {
      isLoadingHistory.value = true;

      // Set chat parameters for future message sending
      setChatParameters(
        idKoordinator: idKoordinator,
        idLamaran: idLamaran,
      );

      // Notify notification controller that chat screen is now active
      try {
        final notificationController = Get.find<ChatNotificationController>();
        notificationController.updateChatScreenState(
            idLamaran, idKoordinator, true);

        // Check and update total unread chats from server
        await notificationController
            .checkAndUpdateTotalUnreadChatsForLamaran(idLamaran);
      } catch (e) {
        LogService.log.w('Could not find ChatNotificationController: $e');
      }

      LogService.log.i(
          'Loading riwayat chat for koordinator: $idKoordinator, lamaran: $idLamaran');

      final riwayatList = await _chatService.getRiwayatChatParsed(
        idKoordinator: idKoordinator,
        idLamaran: idLamaran,
      );

      riwayatChatHistory.value = riwayatList;
      LogService.log.i('Loaded ${riwayatList.length} chat history items');

      // Convert to ChatMessage and add to chat
      _addRiwayatToChat(riwayatList);
    } catch (e) {
      LogService.log.e('Error loading riwayat chat: $e');
      // You might want to show an error message to user
    } finally {
      isLoadingHistory.value = false;
    }
  }

  /// Convert RiwayatChatModel list to ChatMessage and add to chat
  void _addRiwayatToChat(List<RiwayatChatModel> riwayatList) {
    final chatMessagesFromHistory =
        riwayatList.map((riwayat) => riwayat.toChatMessage()).toList();

    // Clear existing messages and add history
    chatMessages.clear();
    chatMessages.addAll(chatMessagesFromHistory);

    // Update the flat list for UI
    updateFlatChatList();

    LogService.log.i(
        'Added ${chatMessagesFromHistory.length} messages from history to chat');
  }

  /// Refresh chat history
  Future<void> refreshChatHistory({
    required String idKoordinator,
    required String idLamaran,
  }) async {
    await loadRiwayatChat(
      idKoordinator: idKoordinator,
      idLamaran: idLamaran,
    );
  }

  /// Validate message for XSS and malicious content
  bool containsMaliciousContent(String text) {
    // List of common XSS patterns and malicious scripts
    final List<String> maliciousPatterns = [
      '<script',
      '</script>',
      'javascript:',
      'onload=',
      'onerror=',
      'onclick=',
      'onmouseover=',
      'onfocus=',
      'onblur=',
      'onchange=',
      'onsubmit=',
      'alert(',
      'confirm(',
      'prompt(',
      'document.cookie',
      'document.write',
      'eval(',
      'setTimeout(',
      'setInterval(',
      'window.location',
      'location.href',
      '<iframe',
      '<object',
      '<embed',
      '<link',
      '<meta',
      '<style',
      'vbscript:',
      'data:text/html',
      'expression(',
      'url(',
      '@import',
      'behavior:',
      '-moz-binding',
      'mocha:',
      'livescript:',
    ];

    // Convert to lowercase for case-insensitive matching
    final String lowerText = text.toLowerCase();

    // Check for malicious patterns
    for (String pattern in maliciousPatterns) {
      if (lowerText.contains(pattern.toLowerCase())) {
        LogService.log
            .w('⚠️ Malicious content detected: $pattern in message: $text');
        return true;
      }
    }

    // Check for HTML tags (basic XSS prevention)
    final RegExp htmlTagRegex = RegExp(r'<[^>]*>', caseSensitive: false);
    if (htmlTagRegex.hasMatch(text)) {
      LogService.log.w('⚠️ HTML tags detected in message: $text');
      return true;
    }

    // Check for suspicious URL patterns (executable files)
    final RegExp suspiciousUrlRegex = RegExp(
      r'(https?:\/\/[^\s]+\.(exe|bat|cmd|scr|pif|com|jar|zip|rar))(\s|$)',
      caseSensitive: false,
    );
    if (suspiciousUrlRegex.hasMatch(text)) {
      LogService.log.w('⚠️ Suspicious URL detected in message: $text');
      return true;
    }

    return false;
  }

  /// Show error message when message fails to send due to security validation
  void _showFailedToSendMessage() {
    final snackBarService = SnackBarService();
    snackBarService.showError("controller.gagal_mengirim_pesan".tr);
    LogService.log.w('⚠️ Message blocked due to security validation');
  }
}
