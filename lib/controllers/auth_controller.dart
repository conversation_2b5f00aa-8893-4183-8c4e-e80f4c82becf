import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

/// Controller to handle authentication and WebSocket integration
class AuthController extends GetxController {
  late FlutterSecureStorage _secureStorage;

  // Observable authentication state
  final RxBool isLoggedIn = false.obs;
  final RxString currentUserId = ''.obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
  }

  void _initializeServices() {
    _secureStorage = Get.find<FlutterSecureStorage>();
    LogService.log.i('🔧 AuthController initialized');
  }

  /// Handle user login
  Future<bool> login(String userId, String password, {String? token}) async {
    try {
      isLoading.value = true;
      LogService.log.i('🔐 Processing login for user: $userId');

      // Here you would normally call your authentication API
      // For now, we'll simulate a successful login

      // Store authentication token if provided
      if (token != null) {
        await _secureStorage.write(key: 'token', value: token);
      }

      // Update local state
      isLoggedIn.value = true;
      currentUserId.value = userId;

      LogService.log.i('✅ Login successful for user: $userId');
      return true;
    } catch (e) {
      LogService.log.e('❌ Login failed for user $userId: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Handle user logout
  Future<bool> logout() async {
    try {
      isLoading.value = true;
      final userId = currentUserId.value;
      LogService.log.i('🔓 Processing logout for user: $userId');

      // Update local state
      isLoggedIn.value = false;
      currentUserId.value = '';

      LogService.log.i('✅ Logout successful for user: $userId');
      return true;
    } catch (e) {
      LogService.log.e('❌ Logout failed: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Switch to different user account
  Future<bool> switchUser(String newUserId, String password,
      {String? token}) async {
    try {
      isLoading.value = true;
      final previousUserId = currentUserId.value;
      LogService.log
          .i('🔄 Processing user switch: $previousUserId → $newUserId');

      // Here you would normally authenticate the new user
      // For now, we'll simulate a successful authentication

      // Store new authentication token if provided
      if (token != null) {
        await _secureStorage.write(key: 'token', value: token);
      }

      // Update local state
      isLoggedIn.value = true;
      currentUserId.value = newUserId;

      LogService.log
          .i('✅ User switch successful: $previousUserId → $newUserId');
      return true;
    } catch (e) {
      LogService.log.e('❌ User switch failed: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated =>
      isLoggedIn.value && currentUserId.value.isNotEmpty;

  /// Get current user info
  Map<String, dynamic> getCurrentUserInfo() {
    return {
      'userId': currentUserId.value,
      'isLoggedIn': isLoggedIn.value,
      'isLoading': isLoading.value,
      'isAuthenticated': isAuthenticated,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  @override
  void onClose() {
    // Clean up when controller is disposed
    LogService.log.i('🔧 AuthController disposing...');
    super.onClose();
  }
}
