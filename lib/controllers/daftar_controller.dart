import 'dart:async';

import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/services/auth_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class DaftarController extends GetxController {
  final AuthService authService = AuthService();
  final snackbar = Get.find<SnackBarService>();
  var isLoadingDaftar = false.obs;
  var isLoadingOTP = false.obs;
  var userData = <String, String?>{}.obs; // Simpan data login sebagai Map
  var codeOtp = ''.obs;
  final prefs = Get.find<FlutterSecureStorage>();
  String? fcmToken;

  final RxInt otpCountdown = 0.obs;
  void startOTPTimer() {
    otpCountdown.value = 60;
    isLoadingOTP.value = true;

    Timer.periodic(Duration(seconds: 1), (timer) {
      if (otpCountdown.value <= 1) {
        timer.cancel();
        isLoadingOTP.value = false;
        otpCountdown.value = 0;
      } else {
        otpCountdown.value--;
      }
    });
  }

  Future<void> register(
      String nama, String email, String password, String noTlp) async {
    try {
      isLoadingDaftar(true);

      fcmToken = await prefs.read(key: "fcm_token");
      var response = await authService.register(
          nama, email, password, noTlp, fcmToken ?? "");

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.register_success".tr;
              snackbar.showSuccess(message);
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "OTP Salah.") {
                snackbar.showError("controller.wrong_otp".tr);
              } else if (responseData["message"] ==
                  "E-mail dan No. Handphone sudah terdaftar.") {
                snackbar.showError("controller.email_noTelp_is_exist".tr);
              } else if (responseData["message"] == "E-mail sudah terdaftar.") {
                snackbar.showError("controller.email_is_exist".tr);
              } else if (responseData["message"] ==
                  "No. Handphone sudah terdaftar.") {
                snackbar.showError("controller.noTelp_is_exist".tr);
              } else {
                snackbar.showError("controller.register_failed".tr);
              }
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.register_success".tr;
              snackbar.showSuccess(message);
              Get.offAllNamed(Routes.login);
            } else {
              if (responseData["message"] == "OTP Salah.") {
                snackbar.showError("controller.wrong_otp".tr);
              } else if (responseData["message"] ==
                  "E-mail dan No. Handphone sudah terdaftar.") {
                snackbar.showError("controller.email_noTelp_is_exist".tr);
              } else if (responseData["message"] == "E-mail sudah terdaftar.") {
                snackbar.showError("controller.email_is_exist".tr);
              } else if (responseData["message"] ==
                  "No. Handphone sudah terdaftar.") {
                snackbar.showError("controller.noTelp_is_exist".tr);
              } else {
                snackbar.showError("controller.register_failed".tr);
              }
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} DaftarController");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError("controller.server_error".tr);
      }
    } catch (e) {
      snackbar.showError("$e");
    } finally {
      isLoadingDaftar(false);
    }
  }

  Future<void> reqOtp(String email, String noHp, String? forParam) async {
    try {
      startOTPTimer();

      var response = await authService.reqOtp(email, noHp, forParam);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("status") &&
              responseData["status"] == "success") {
            String message = "controller.otp_success".tr;
            snackbar.showSuccess(message);
          } else {
            if (responseData["message"] == "Email sudah terdaftar.") {
              otpCountdown.value = 0;
              snackbar.showError("controller.email_is_exist".tr);
            } else {
              otpCountdown.value = 0;
              snackbar.showError("controller.otp_failed".tr);
            }
          }
        } else {
          otpCountdown.value = 0;
          snackbar
              .showError("${"controller.invalid_format".tr} DaftarController");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
    } finally {
      isLoadingOTP(false);
    }
  }
}
