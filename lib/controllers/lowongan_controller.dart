import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/services/lowongan_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
// import 'package:shared_preferences/shared_preferences.dart';
import 'package:translator/translator.dart';

class LowonganController extends GetxController {
  final LowonganService lowonganService = LowonganService();
  final snackbar = Get.find<SnackBarService>();
  final prefs = FlutterSecureStorage();
  String pin = "";
  final RxBool isLoading = false.obs;
  final RxInt totalData = 0.obs;
  final RxInt totalPage = 0.obs;

  final translator = GoogleTranslator();

  RxList<LowonganModel> lowonganList = <LowonganModel>[].obs;
  final RxBool refreshTersimpan = false.obs;

  void triggerRefreshTersimpan() {
    refreshTersimpan
        .toggle(); // akan mengubah nilainya dari true ke false, lalu false ke true
  }

  final refreshBeranda = false.obs;

  triggerRefreshBeranda() {
    refreshBeranda.toggle(); // ini akan memicu listener-nya
  }

  final refreshPerusahaan = false.obs;

  void triggerRefreshPerusahaan() {
    refreshPerusahaan.toggle(); // ini akan memicu listener-nya
  }

  Future<List<LowonganModel>> getListJob(
      String search,
      String lokasi,
      List<String> jenisPekerjaan,
      List<String> spesialisasi,
      List<String> pendidikan,
      String waktuPosting,
      int page,
      int pageSize) async {
    try {
      pin = await prefs.read(key: "pin") ?? "";
      isLoading(true);
      var response = await lowonganService.getListJob(
          search,
          lokasi,
          jenisPekerjaan,
          spesialisasi,
          pendidikan,
          waktuPosting,
          page,
          pageSize,
          pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LowonganModel> result =
                  dataList.map((item) => LowonganModel.fromJson(item)).toList();

              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              return result;
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              lowonganList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lowonganList.clear();
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} LowonganController");
          lowonganList.clear();

          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lowonganList.clear();
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        lowonganList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lowonganList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<List<LowonganModel>> getListJobCompany(
      String idKoordinator, int page, int pageSize) async {
    try {
      pin = await prefs.read(key: "pin") ?? "";
      isLoading(true);
      var response = await lowonganService.getListJobCompany(
          idKoordinator, page, pageSize, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LowonganModel> result =
                  dataList.map((item) => LowonganModel.fromJson(item)).toList();

              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              return result;
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              lowonganList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lowonganList.clear();
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} LowonganController");
          lowonganList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lowonganList.clear();
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        lowonganList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lowonganList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<List<LowonganModel>> getDetailJob(
      String idRequest, int page, int pageSize) async {
    try {
      pin = await prefs.read(key: "pin") ?? "";
      isLoading(true);
      var response =
          await lowonganService.getDetailJob(idRequest, page, pageSize, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LowonganModel> result =
                  dataList.map((item) => LowonganModel.fromJson(item)).toList();

              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              return result;
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              lowonganList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lowonganList.clear();
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} LowonganController");
          lowonganList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lowonganList.clear();
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        lowonganList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lowonganList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<List<LowonganModel>> getLowonganTersimpan(
      String search, int page, int pageSize) async {
    try {
      pin = await prefs.read(key: "pin") ?? "";
      isLoading(true);
      var response = await lowonganService.getLowonganTersimpan(
          search, page, pageSize, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LowonganModel> result =
                  dataList.map((item) => LowonganModel.fromJson(item)).toList();

              totalData.value =
                  int.tryParse(responseData["total_data"].toString()) ?? 0;
              totalPage.value =
                  int.tryParse(responseData["total_page"].toString()) ?? 0;

              return result;
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              lowonganList.clear();
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lowonganList.clear();
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} LowonganController");
          lowonganList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lowonganList.clear();
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        lowonganList.clear();
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lowonganList.clear();
      return [];
    } finally {
      isLoading(false);
    }
  }

  Future<bool> saveLowongan(String q, bool check) async {
    try {
      pin = await prefs.read(key: "pin") ?? "";
      var response = await lowonganService.saveLowongan(q, check, pin);

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        if (responseData["success"] == true) {
          return true;
        } else {
          snackbar.showError("controller.save_failed".tr);
          return false;
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lowonganList.clear();
        return false;
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        lowonganList.clear();
        return false;
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      lowonganList.clear();
      return false;
    } finally {}
  }
}
