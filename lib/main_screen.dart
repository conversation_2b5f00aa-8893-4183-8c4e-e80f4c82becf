import 'package:digital_cv_mobile/controllers/bottom_nav_controller.dart';
import 'package:digital_cv_mobile/controllers/chat_notification_controller.dart';
import 'package:digital_cv_mobile/controllers/fcm_controller.dart';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/notification_service.dart';
import 'package:digital_cv_mobile/views/beranda_screen.dart';
import 'package:digital_cv_mobile/views/lowongan_tersimpan_screen.dart';
import 'package:digital_cv_mobile/views/profile_screen.dart';
import 'package:digital_cv_mobile/views/riwayat_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final NavController navController = Get.find();
  NotificationServices notificationServices = NotificationServices();
  final FCMController fcmController = Get.put(FCMController());
  final ChatNotificationController chatNotificationController =
      Get.put(ChatNotificationController());

  final List<Widget> pages = const [
    BerandaScreen(),
    LowonganTersimpanScreen(),
    RiwayatScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();

    fcmController.checkStatus();
    notificationServices.requestNotificationPermission();
    notificationServices.forgroundMessage();
    notificationServices.firebaseInit(context);
    notificationServices.setupInteractMessage(context);
    notificationServices.isTokenRefresh();

    notificationServices.getDeviceToken().then((value) {
      if (kDebugMode) {
        fcmController.updateToken(fcmToken: value);
      }
    });
    notificationServices.subscribeTopic();
  }

  /// Refresh chat notifications manually
  Future<void> _refreshChatNotifications() async {
    try {
      Get.put(LamaranController()).triggerRefreshRiwayatLamar();
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing chat notifications: $e');
      }
    }
  }

  /// Build Riwayat icon with chat notification badge
  Widget _buildRiwayatIconWithBadge() {
    return Obx(() {
      final totalUnreadCount =
          chatNotificationController.totalUnreadChats.value;

      return GestureDetector(
        onTap: () {
          // Long press to manually refresh notifications (for debugging/testing)
          // if (kDebugMode) {
          _refreshChatNotifications();
          // }
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Icon(Icons.history),
            if (totalUnreadCount > 0)
              Positioned(
                right: -6,
                top: -6,
                child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  constraints: BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    totalUnreadCount > 99 ? '99+' : totalUnreadCount.toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => IndexedStack(
          index: navController.selectedIndex.value,
          children: pages,
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              spreadRadius: 5,
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Obx(
          () => BottomNavigationBar(
            currentIndex: navController.selectedIndex.value,
            onTap: (index) => navController.changeIndex(index),
            type: BottomNavigationBarType.fixed,
            selectedItemColor: ColorAsset.primaryColor,
            unselectedItemColor: Colors.grey,
            backgroundColor: Colors.white,
            items: [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'drawer.beranda'.tr,
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.bookmark_added),
                label: 'drawer.tersimpan'.tr,
              ),
              BottomNavigationBarItem(
                icon: _buildRiwayatIconWithBadge(),
                label: 'drawer.riwayat'.tr,
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'drawer.profil'.tr,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
