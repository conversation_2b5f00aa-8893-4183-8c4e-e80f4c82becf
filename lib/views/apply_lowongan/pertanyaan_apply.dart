import 'package:digital_cv_mobile/controllers/apply_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PertanyaanApply extends StatefulWidget {
  final LowonganModel lowonganModel;
  const PertanyaanApply({super.key, required this.lowonganModel});

  @override
  State<PertanyaanApply> createState() => _PertanyaanApplyState();
}

class _PertanyaanApplyState extends State<PertanyaanApply> {
  final ApplyController applyController = Get.put(ApplyController());
  List<TextEditingController> controllers = [];
  late List<String> kkList;

  @override
  void initState() {
    super.initState();

    final kkString = widget.lowonganModel.kKhusus;
    final kkList = kkString.split('|').map((e) => e.trim()).toList();
    // Inisialisasi KK List + Controllers (ka<PERSON> belum)
    applyController.initKkList(kkList);

    // Set value jika jawaban sebelumnya ada
    if (applyController.jawabanKhusus.isNotEmpty) {
      for (int i = 0; i < applyController.kkList.length; i++) {
        applyController.controllers[i].text =
            i < applyController.jawabanKhusus.length
                ? applyController.jawabanKhusus[i]
                : '';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                "apply.title1".tr,
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
            Center(
              child: Text(
                widget.lowonganModel.posisi,
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Center(
              child: Text(
                "${"apply.di".tr} ${widget.lowonganModel.perusahaan}",
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
            Divider(
              thickness: 1,
            ),
            Center(
              child: Text(
                "${"apply.pendaftaran_di".tr} ${widget.lowonganModel.perusahaan} ${"apply.digitalcv".tr}",
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
            Divider(
              thickness: 1,
            ),
            applyController.kkList.isNotEmpty
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "apply.pertanyaan_kandidat".tr,
                        style: TextStyle(fontSize: 14),
                        textAlign: TextAlign.left,
                      ),
                      Form(
                        key: applyController.formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: applyController.kkList
                              .asMap()
                              .entries
                              .map((entry) {
                            final index = entry.key;
                            final text = entry.value;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 10),
                                Text(
                                  "${index + 1}. $text",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                const SizedBox(height: 10),
                                _buildTextField(
                                  "apply.hint_jawaban".tr,
                                  "apply.validator_jawaban".tr,
                                  applyController.controllers[index],
                                  TextInputType.text,
                                  onChanged: (value) {
                                    if (index <
                                        applyController.jawabanKhusus.length) {
                                      applyController.jawabanKhusus[index] =
                                          value;
                                    } else {
                                      // Jika index belum ada, tambahkan data
                                      applyController.jawabanKhusus.add(value);
                                    }
                                  },
                                ),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  )
                : SizedBox(),
            SizedBox(
              height: 10,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(
                  () => Checkbox(
                    value: applyController.rxConfirm.value,
                    onChanged: (value) {
                      applyController.rxConfirm.value = value!;
                    },
                    visualDensity: VisualDensity.compact,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 7,
                      ),
                      Text(
                        "apply.txt0".tr,
                        style: TextStyle(fontSize: 14),
                        textAlign: TextAlign.left,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "1.",
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          Expanded(
                            flex: 15,
                            child: Text(
                              "apply.txt1".tr,
                              style: TextStyle(fontSize: 14),
                              softWrap: true,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "2.",
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          Expanded(
                            flex: 15,
                            child: Text(
                              "${"apply.txt2".tr} ${widget.lowonganModel.perusahaan} ${"apply.txt3".tr} ${widget.lowonganModel.perusahaan} ${"apply.txt4".tr}",
                              style: TextStyle(fontSize: 14),
                              softWrap: true,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(
              height: 15,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    String hintText,
    String errorMessage, // <- ganti dari 'validator' biar lebih jelas
    TextEditingController controller,
    TextInputType inputType, {
    Function(String)? onChanged, // <- tambahan
  }) {
    return TextFormField(
      controller: controller,
      style: const TextStyle(fontSize: 14),
      keyboardType: inputType,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: const TextStyle(fontSize: 14),
        contentPadding:
            const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide:
              const BorderSide(color: ColorAsset.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        errorStyle: const TextStyle(
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return errorMessage;
        }
        return null;
      },
      onChanged: onChanged, // <- tambahan
    );
  }
}
