import 'package:digital_cv_mobile/components/digital-cv/card_pelatihan_kursus.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PelatihanApply extends StatefulWidget {
  const PelatihanApply({super.key});

  @override
  State<PelatihanApply> createState() => _PelatihanApplyState();
}

class _PelatihanApplyState extends State<PelatihanApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
    infoCVController.loadRiwayatKursus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardPelatihanKursus(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController)
          ],
        ),
      ),
    );
  }
}
