import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pekerjaan.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InfoPekerjaanApply extends StatefulWidget {
  const InfoPekerjaanApply({super.key});

  @override
  State<InfoPekerjaanApply> createState() => _InfoPekerjaanApplyState();
}

class _InfoPekerjaanApplyState extends State<InfoPekerjaanApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardInformasiPekerjaan(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController)
          ],
        ),
      ),
    );
  }
}
