import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_bekerja.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PengalamanKerjaApply extends StatefulWidget {
  const PengalamanKerjaApply({super.key});

  @override
  State<PengalamanKerjaApply> createState() => _PengalamanKerjaApplyState();
}

class _PengalamanKerjaApplyState extends State<PengalamanKerjaApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
    infoCVController.loadRiwayatPekerjaan();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardPengalamanBekerja(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController)
          ],
        ),
      ),
    );
  }
}
