import 'package:digital_cv_mobile/components/digital-cv/card_minat_konsep_pribadi.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MinatKonsepApply extends StatefulWidget {
  const MinatKonsepApply({super.key});

  @override
  State<MinatKonsepApply> createState() => _MinatKonsepApplyState();
}

class _MinatKonsepApplyState extends State<MinatKonsepApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  final MinatKonsepController minatKonsepController =
      Get.find<MinatKonsepController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
    infoCVController.loadPenguasaanBahasa();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardMinatdanKonsepPribadi(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController,
                minatKonsepController: minatKonsepController)
          ],
        ),
      ),
    );
  }
}
