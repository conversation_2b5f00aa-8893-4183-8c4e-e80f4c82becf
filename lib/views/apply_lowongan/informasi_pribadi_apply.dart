import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pribadi.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InfoPribadiApply extends StatefulWidget {
  const InfoPribadiApply({super.key});

  @override
  State<InfoPribadiApply> createState() => _InfoPribadiApplyState();
}

class _InfoPribadiApplyState extends State<InfoPribadiApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  final ProfileController profileController = Get.find<ProfileController>();

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardInformasiPribadi(
                infoCVController: infoCVController,
                timelineData: Utilities().timelineData,
                profileController: profileController)
          ],
        ),
      ),
    );
  }
}
