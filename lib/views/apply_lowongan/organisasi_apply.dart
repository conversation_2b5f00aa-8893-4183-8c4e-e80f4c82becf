import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_organisasi.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrganissiApply extends StatefulWidget {
  const OrganissiApply({super.key});

  @override
  State<OrganissiApply> createState() => _OrganissiApplyState();
}

class _OrganissiApplyState extends State<OrganissiApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
    infoCVController.loadRiwayatOrganisasi();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardPengalamanOrganisasi(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController)
          ],
        ),
      ),
    );
  }
}
