import 'package:digital_cv_mobile/components/digital-cv/card_riwayat_pendidikan.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PendidikanApply extends StatefulWidget {
  const PendidikanApply({super.key});

  @override
  State<PendidikanApply> createState() => _PendidikanApplyState();
}

class _PendidikanApplyState extends State<PendidikanApply> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    load();
  }

  void load() async {
    await Future.delayed(const Duration(milliseconds: 300));
    infoCVController.loadRH();
    infoCVController.loadRiwayatPendidikan();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardRiwayatPendidikan(
                timelineData: Utilities().timelineData,
                infoCVController: infoCVController),
            // Text(
            //   "dcv.pendidikan.judul".tr,
            //   style: TextStyle(
            //     fontSize: 16,
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),
            // SizedBox(
            //   height: 5,
            // ),
            // Material(
            //   color: Colors.white,
            //   shape: RoundedRectangleBorder(
            //     side: BorderSide(color: Colors.grey),
            //     borderRadius: BorderRadius.circular(16),
            //   ),
            //   child: Padding(
            //     padding: const EdgeInsets.all(12.0),
            //     child: Column(
            //       children: [
            //         Obx(
            //           () => (infoCVController.pendidikanList.isEmpty
            //               ? SizedBox()
            //               : Column(
            //                   children: [
            //                     Row(
            //                       crossAxisAlignment: CrossAxisAlignment.end,
            //                       mainAxisAlignment: MainAxisAlignment.end,
            //                       children: [
            //                         Text(
            //                           "tombol.tambah_data".tr,
            //                           style: TextStyle(
            //                             fontSize: 14,
            //                             fontWeight: FontWeight.bold,
            //                           ),
            //                         ),
            //                         SizedBox(
            //                           width: 10,
            //                         ),
            //                         GestureDetector(
            //                           onTap: () async {
            //                             var result = await Get.toNamed(
            //                               Routes.pendidikan,
            //                               arguments: {
            //                                 "pendidikan": null,
            //                                 "diploma": infoCVController
            //                                         .rhList.isNotEmpty
            //                                     ? infoCVController
            //                                         .rhList[0].diploma
            //                                     : "",
            //                                 "pendidikan_terakhir":
            //                                     infoCVController
            //                                             .rhList.isNotEmpty
            //                                         ? infoCVController.rhList[0]
            //                                             .pendidikanTerakhir
            //                                         : "",
            //                               },
            //                             );

            //                             if (result == true) {
            //                               infoCVController.loadRH();
            //                               infoCVController
            //                                   .loadRiwayatPendidikan();
            //                             }
            //                           },
            //                           child: Icon(
            //                             Icons.add_circle,
            //                             size: 18,
            //                             color: ColorAsset.primaryColor,
            //                           ),
            //                         ),
            //                       ],
            //                     ),
            //                     SizedBox(
            //                       height: 10,
            //                     ),
            //                     Divider(
            //                       thickness: 1,
            //                       height: 1,
            //                     ),
            //                     SizedBox(
            //                       height: 10,
            //                     ),
            //                   ],
            //                 )),
            //         ),
            //         Obx(() => !infoCVController.isLoadingPendidikan.value
            //             ? (!infoCVController.pendidikanList.isEmpty
            //                 ? Column(
            //                     children: List.generate(
            //                       infoCVController.pendidikanList.length,
            //                       (index) {
            //                         final item =
            //                             infoCVController.pendidikanList[index];
            //                         return Column(
            //                           children: [
            //                             Row(
            //                               crossAxisAlignment:
            //                                   CrossAxisAlignment.start,
            //                               children: [
            //                                 Expanded(
            //                                   child: Column(
            //                                     crossAxisAlignment:
            //                                         CrossAxisAlignment.start,
            //                                     children: [
            //                                       Text(
            //                                         item.namaSekolah,
            //                                         style: TextStyle(
            //                                           fontSize: 14,
            //                                           fontWeight:
            //                                               FontWeight.bold,
            //                                         ),
            //                                       ),
            //                                       Text(
            //                                         "${item.jurusan} (${item.tahunMulai} - ${item.tahunSelesai})",
            //                                         style: TextStyle(
            //                                           fontSize: 12,
            //                                           fontWeight:
            //                                               FontWeight.normal,
            //                                         ),
            //                                       ),
            //                                     ],
            //                                   ),
            //                                 ),
            //                                 GestureDetector(
            //                                   onTap: () async {
            //                                     var result = await Get.toNamed(
            //                                         Routes.pendidikan,
            //                                         arguments: {
            //                                           "pendidikan": item,
            //                                           "diploma":
            //                                               infoCVController
            //                                                   .rhList[0]
            //                                                   .diploma,
            //                                           "pendidikan_terakhir":
            //                                               infoCVController
            //                                                   .rhList[0]
            //                                                   .pendidikanTerakhir,
            //                                         });

            //                                     if (result == true) {
            //                                       infoCVController.loadRH();
            //                                       infoCVController
            //                                           .loadRiwayatPendidikan();
            //                                     }
            //                                   },
            //                                   child: Icon(
            //                                     Icons.edit,
            //                                     size: 18,
            //                                     color: ColorAsset.primaryColor,
            //                                   ),
            //                                 ),
            //                               ],
            //                             ),
            //                             // Divider antar item, jangan ditambahkan di item terakhir
            //                             if (index !=
            //                                 infoCVController
            //                                         .pendidikanList.length -
            //                                     1)
            //                               const Divider(height: 16),
            //                           ],
            //                         );
            //                       },
            //                     ),
            //                   )
            //                 : Row(
            //                     crossAxisAlignment: CrossAxisAlignment.start,
            //                     children: [
            //                       Expanded(
            //                         child: Column(
            //                           crossAxisAlignment:
            //                               CrossAxisAlignment.start,
            //                           children: [
            //                             Text(
            //                               "data_kosong".tr,
            //                               style: TextStyle(
            //                                 fontSize: 14,
            //                                 fontWeight: FontWeight.bold,
            //                               ),
            //                             ),
            //                           ],
            //                         ),
            //                       ),
            //                       GestureDetector(
            //                         onTap: () async {
            //                           var result = await Get.toNamed(
            //                             Routes.pendidikan,
            //                             arguments: {
            //                               "pendidikan": null,
            //                               "diploma":
            //                                   infoCVController.rhList.isNotEmpty
            //                                       ? infoCVController
            //                                           .rhList[0].diploma
            //                                       : "",
            //                               "pendidikan_terakhir":
            //                                   infoCVController.rhList.isNotEmpty
            //                                       ? infoCVController.rhList[0]
            //                                           .pendidikanTerakhir
            //                                       : "",
            //                             },
            //                           );

            //                           if (result == true) {
            //                             infoCVController.loadRH();
            //                             infoCVController
            //                                 .loadRiwayatPendidikan();
            //                           }
            //                         },
            //                         child: Icon(
            //                           Icons.add_circle,
            //                           size: 18,
            //                           color: ColorAsset.primaryColor,
            //                         ),
            //                       ),
            //                     ],
            //                   ))
            //             : ShimmerPendidikan()),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
