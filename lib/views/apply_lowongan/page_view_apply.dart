import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/controllers/apply_controller.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/views/apply_lowongan/pertanyaan_apply.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ApplyDialog extends StatefulWidget {
  final LowonganModel lowonganModel;
  const ApplyDialog({super.key, required this.lowonganModel});

  @override
  State<ApplyDialog> createState() => _ApplyDialogState();
}

class _ApplyDialogState extends State<ApplyDialog> {
  final ApplyController applyController = Get.put(ApplyController());
  final GetInfoCVController infoCVController = Get.put(GetInfoCVController());
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 8;

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _prevPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentPage = index;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.95,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: SafeArea(
              bottom: false,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Indikator drag
                    Container(
                      width: 50,
                      height: 5,
                      margin: const EdgeInsets.only(bottom: 10),
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () async {
                            final currentContext = context;
                            var confirm = await showConfirmationDialog(
                                currentContext,
                                "apply.confirmation_title".tr,
                                "apply.confirmation_cancel".tr,
                                "konfirmasi.iya2".tr,
                                "konfirmasi.tidak".tr);
                            if (confirm == true) {
                              Navigator.pop(currentContext);
                            }
                          },
                          icon: const Icon(Icons.close),
                        ),
                        Text(
                          "apply.screening_cv".tr,
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),

                    // Padding(
                    //   padding: const EdgeInsets.only(
                    //       bottom: 16, left: 16, right: 16),
                    //   child: TweenAnimationBuilder<double>(
                    //     tween: Tween<double>(
                    //       begin: (_currentPage) / _totalPages,
                    //       end: (_currentPage + 1) / _totalPages,
                    //     ),
                    //     duration: Duration(milliseconds: 500), // Waktu animasi
                    //     curve: Curves.easeInOut, // Kurva animasi
                    //     builder: (context, value, child) {
                    //       return LinearProgressIndicator(
                    //         value: value,
                    //         borderRadius: BorderRadius.circular(50),
                    //         backgroundColor: Colors.grey[300],
                    //         valueColor: AlwaysStoppedAnimation<Color>(
                    //             ColorAsset.secodaryColor),
                    //       );
                    //     },
                    //   ),
                    // ),

                    const Divider(
                      thickness: 1,
                      height: 1,
                    ),

                    SizedBox(
                      height: 16,
                    ),

                    // PageView dengan Controller
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: _onPageChanged,
                        physics:
                            NeverScrollableScrollPhysics(), // Mencegah swipe
                        children: [
                          PertanyaanApply(
                            lowonganModel: widget.lowonganModel,
                          ),
                          // InfoPribadiApply(),
                          // PendidikanApply(),
                          // PelatihanApply(),
                          // PengalamanKerjaApply(),
                          // InfoPekerjaanApply(),
                          // OrganissiApply(),
                          // MinatKonsepApply(),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    // Tombol Navigasi
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Obx(() {
                            final isEnabled = applyController.rxConfirm.value;

                            return applyController.isLoading.value
                                ? Center(child: CircularProgressIndicator())
                                : ElevatedButton(
                                    onPressed: isEnabled
                                        ? () async {
                                            var confirm =
                                                await showConfirmationDialog(
                                                    context,
                                                    "apply.confirmation_title"
                                                        .tr,
                                                    "apply.confirmation_yes".tr,
                                                    "konfirmasi.iya2".tr,
                                                    "konfirmasi.tidak".tr);
                                            if (confirm == true) {
                                              applyController.applyLowongan(
                                                  widget.lowonganModel.idReq,
                                                  widget.lowonganModel
                                                      .lokasiKerja2,
                                                  applyController
                                                      .jawabanKhusus);
                                            }
                                          }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 6),
                                      minimumSize: const Size.fromHeight(50),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                      textStyle: const TextStyle(fontSize: 14),
                                    ),
                                    child: Text(
                                      "tombol.apply".tr,
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  );
                          }),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Fungsi untuk menampilkan dialog
Future<bool?> showDialogApply(
    BuildContext context, LowonganModel lowonganModel) {
  return Navigator.push(
    context,
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => ApplyDialog(
        lowonganModel: lowonganModel,
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      opaque: false,
      barrierColor: Colors.black54,
      barrierDismissible: true,
    ),
  );
}
