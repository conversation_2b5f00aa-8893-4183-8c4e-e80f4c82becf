import 'dart:async';

import 'package:digital_cv_mobile/components/konfirmasi_login.dart';
import 'package:digital_cv_mobile/controllers/chat_message_controller.dart';
import 'package:digital_cv_mobile/controllers/chat_notification_controller.dart';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lamaran_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/views/chat/chat_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RiwayatScreen extends StatefulWidget {
  const RiwayatScreen({super.key});

  @override
  State<RiwayatScreen> createState() => _RiwayatScreenState();
}

class _RiwayatScreenState extends State<RiwayatScreen> {
  final LamaranController lamaranController = Get.find<LamaranController>();
  final ChatNotificationController chatNotificationController =
      Get.put(ChatNotificationController());
  static const _pageSize = 5;
  final PagingController<int, LamaranModel> _pagingController =
      PagingController(firstPageKey: 1);
  final TextEditingController searchController = TextEditingController();
  late Worker _refreshWorker;
  final sharedPrefs = Get.find<SharedPreferences>();

  @override
  void initState() {
    super.initState();

    // Use Worker to prevent multiple listeners
    _refreshWorker = ever(lamaranController.refreshRiwayatLamar, (_) {
      if (mounted) {
        _pagingController.refresh();
      }
    });

    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
  }

  @override
  void dispose() {
    _refreshWorker.dispose();
    _pagingController.dispose();
    searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // ✅ Cegah pemanggilan API jika sudah tahu totalPage dan pageKey > totalPage
      final int totalPage = lamaranController.totalPage.value;
      if (totalPage != 0 && pageKey > totalPage) {
        if (kDebugMode) {
          print('❌ Page $pageKey melebihi totalPage ($totalPage), SKIP fetch.');
        }
        _pagingController.appendLastPage([]);
        return;
      }

      final newItems = await lamaranController.getRiwayatLamaran(
        pageKey,
        _pageSize,
        searchController.text,
      );

      if (!mounted) return; // Check if widget is still mounted

      // Hitung ulang totalPage kalau belum terisi
      final isLastPage = pageKey >= lamaranController.totalPage.value ||
          newItems.length < _pageSize;

      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }

      // Initialize notification counts from fetched items
      if (pageKey == 1) {
        // Only initialize on first page to avoid overwriting real-time updates
        chatNotificationController.initializeFromLamaranList(newItems);
      }

      if (kDebugMode) {
        print('✅ FETCH PAGE $pageKey');
        print('API response count: ${newItems.length}');
        print('Total list after append: ${_pagingController.itemList?.length}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('❌ Error saat fetch page $pageKey: $error');
      }
      if (mounted) {
        _pagingController.error = error;
      }
    }
  }

  Future<void> _onRefresh() async {
    lamaranController.totalPage.value = 0; // Reset totalPage
    lamaranController.totalData.value = 0; // Reset totalData
    _pagingController.refresh();
  }

  Timer? _debounce;
  void _onSearchChanged(String query) {
    // Batalkan timer sebelumnya jika masih aktif
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      // Allow empty search to show all data
      if (query.isNotEmpty && query.length < 3) {
        return; // Don't search if query is not empty but less than 3 characters
      }

      // Reset pagination state before new search
      lamaranController.totalPage.value = 0;
      lamaranController.totalData.value = 0;

      // Clear existing items to prevent duplication
      _pagingController.itemList?.clear();
      _pagingController.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            SliverAppBar(
              forceElevated: true,
              expandedHeight: 50, // Batasi tinggi maksimal
              toolbarHeight: 50,
              floating: true,
              pinned: false,
              snap: true,
              backgroundColor: Colors.white, // Warna tetap putih
              elevation: 0,
              shadowColor: Colors.black.withOpacity(0.0),
              title: const Text(
                  ''), // Tambahkan agar transparansi tidak bermasalah
              automaticallyImplyLeading: false,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        spreadRadius: 2,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    physics:
                        const NeverScrollableScrollPhysics(), // Supaya tidak ikut scroll utama
                    child: Padding(
                      padding:
                          const EdgeInsets.only(top: 10, left: 20, right: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "riwayat.judul".tr,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
          body: sharedPrefs.getBool("auth") == true ||
                  sharedPrefs.getBool("isGoogle") == true
              ? RefreshIndicator(
                  child: CustomScrollView(
                    slivers: [
                      Obx(() => SliverPersistentHeader(
                            pinned: true,
                            floating: false,
                            delegate: _SliverHeaderDelegate(
                              jmlLamaran: lamaranController.totalData.value,
                              searchController,
                              _onSearchChanged,
                            ),
                          )),
                      PagedSliverList(
                        pagingController: _pagingController,
                        builderDelegate: PagedChildBuilderDelegate<
                                LamaranModel>(
                            itemBuilder: (context, item, index) => Padding(
                                  padding: const EdgeInsets.all(8),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border(
                                          bottom: BorderSide(
                                        color: Colors.grey.withOpacity(0.2),
                                        width: 1,
                                      )),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                ClipRRect(
                                                  borderRadius:
                                                      (isValidImageUrl(
                                                                  item.image) &&
                                                              item.image
                                                                  .isNotEmpty)
                                                          ? BorderRadius
                                                              .circular(0)
                                                          : BorderRadius
                                                              .circular(10),
                                                  child: (isValidImageUrl(
                                                              item.image) &&
                                                          item.image.isNotEmpty)
                                                      ? Image.network(
                                                          item.image,
                                                          width: 70,
                                                          height: 70,
                                                          fit: BoxFit.cover,
                                                          errorBuilder:
                                                              (context, error,
                                                                  stackTrace) {
                                                            return Image.asset(
                                                              ImageAssets.logo2,
                                                              fit: BoxFit.cover,
                                                              width: 60,
                                                              height: 60,
                                                            );
                                                          },
                                                        )
                                                      : Image.asset(
                                                          ImageAssets.logo2,
                                                          fit: BoxFit.cover,
                                                          width: 60,
                                                          height: 60,
                                                        ),
                                                ),
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        item.posisi,
                                                        style: TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                      Text(
                                                        item.perusahaan,
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          color: Colors.grey,
                                                        ),
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        textAlign:
                                                            TextAlign.start,
                                                        softWrap: true,
                                                      ),
                                                      SizedBox(
                                                        height: 5,
                                                      ),
                                                      ...[
                                                        if (item.lastStatus ==
                                                                "Lamaran Dikirim" ||
                                                            item.lastStatus ==
                                                                "Terima Rekrutmen")
                                                          Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    4),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors
                                                                  .green
                                                                  .withOpacity(
                                                                      0.1),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4),
                                                            ),
                                                            child: Text(
                                                              TranslationService
                                                                  .translateBetweenLangs(
                                                                item.lastStatus,
                                                                "status_lamaran",
                                                                "id",
                                                                Get.locale
                                                                        ?.languageCode
                                                                        .toLowerCase() ??
                                                                    "id",
                                                              ),
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                      .green),
                                                            ),
                                                          )
                                                        else if (item
                                                                    .lastStatus ==
                                                                "On Proccess Psikotes" ||
                                                            item.lastStatus ==
                                                                "On Proccess Digitalcv" ||
                                                            item.lastStatus ==
                                                                "Psikotes Gestalt" ||
                                                            item.lastStatus ==
                                                                "Selesai Psikotes")
                                                          Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    4),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: ColorAsset
                                                                  .primaryColor
                                                                  .withOpacity(
                                                                      0.1),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4),
                                                            ),
                                                            child: Text(
                                                              TranslationService
                                                                  .translateBetweenLangs(
                                                                item.lastStatus,
                                                                "status_lamaran",
                                                                "id",
                                                                Get.locale
                                                                        ?.languageCode
                                                                        .toLowerCase() ??
                                                                    "id",
                                                              ),
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: ColorAsset
                                                                      .primaryColor),
                                                            ),
                                                          )
                                                        else
                                                          Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    4),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors.red
                                                                  .withOpacity(
                                                                      0.1),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4),
                                                            ),
                                                            child: Text(
                                                              TranslationService
                                                                  .translateBetweenLangs(
                                                                item.lastStatus,
                                                                "status_lamaran",
                                                                "id",
                                                                Get.locale
                                                                        ?.languageCode
                                                                        .toLowerCase() ??
                                                                    "id",
                                                              ),
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                      .red),
                                                            ),
                                                          )
                                                      ]
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Stack(
                                                  children: [
                                                    if (item.isPaketChat ==
                                                        "true")
                                                      OutlinedButton(
                                                        style: OutlinedButton
                                                            .styleFrom(
                                                          side:
                                                              const BorderSide(
                                                                  color: Colors
                                                                      .blue),
                                                          foregroundColor:
                                                              Colors.blue,
                                                          shape:
                                                              RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                          ),
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      13,
                                                                  vertical: 10),
                                                        ),
                                                        onPressed: () async {
                                                          // Mark chat as read when opening
                                                          chatNotificationController
                                                              .markChatAsRead(
                                                            item.idLamar,
                                                            item.idKoordinator,
                                                          );

                                                          // Auto read messages on server when clicking Chat HRD button
                                                          await chatNotificationController
                                                              .autoReadMessages(
                                                                  item.idLamar);

                                                          Get.put(ChatMessageController())
                                                              .loadRiwayatChat(
                                                            idKoordinator: item
                                                                .idKoordinator,
                                                            idLamaran:
                                                                item.idLamar,
                                                          );

                                                          Get.to(() =>
                                                              ChatScreen());
                                                        },
                                                        child: Text("Chat HRD"),
                                                      ),
                                                    Obx(() {
                                                      final notifCount =
                                                          chatNotificationController
                                                              .getNotificationCount(
                                                        item.idLamar,
                                                        item.idKoordinator,
                                                      );

                                                      // Fallback to item.notifChatRef if real-time count is 0
                                                      final displayCount =
                                                          notifCount > 0
                                                              ? notifCount
                                                              : item
                                                                  .notifChatRef;

                                                      if (displayCount > 0) {
                                                        return Positioned(
                                                          top: -5,
                                                          right: 0,
                                                          child: Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    5),
                                                            decoration:
                                                                BoxDecoration(
                                                                    color: Colors
                                                                        .red,
                                                                    shape: BoxShape
                                                                        .circle),
                                                            child: Text(
                                                              displayCount
                                                                  .toString(),
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 12,
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }
                                                      return SizedBox.shrink();
                                                    })
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 5,
                                                ),
                                                ElevatedButton(
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        const Color(0xFFFFC107),
                                                    foregroundColor:
                                                        Colors.black87,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    elevation: 1,
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 16,
                                                        vertical: 10),
                                                  ),
                                                  onPressed: () {
                                                    Get.toNamed(Routes.timeline,
                                                        arguments: item);
                                                  },
                                                  child:
                                                      Text('timeline.judul'.tr),
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                Text(
                                                  TranslationService
                                                      .translateTimeAgo(
                                                          item.waktuLalu),
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                            noItemsFoundIndicatorBuilder: (context) => Center(
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/images/empty_data_notif.png',
                                          width: 200,
                                          height: 200,
                                        ),
                                        SizedBox(height: 20),
                                        Text(
                                          "riwayat.data_kosong".tr,
                                          style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(height: 10),
                                        Text(
                                          "riwayat.data_kosong_desc".tr,
                                          style: TextStyle(
                                              fontSize: 14, color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                      ),
                    ],
                  ),
                  onRefresh: _onRefresh,
                )
              : Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: KonfirmasiLogin(),
                ),
        ),
      ),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final int jmlLamaran;
  final TextEditingController searchController;
  final Function(String) onSearchChanged;

  _SliverHeaderDelegate(
    this.searchController,
    this.onSearchChanged, {
    required this.jmlLamaran,
  });

  @override
  double get minExtent => 100; // Tinggi minimum saat tersembunyi
  @override
  double get maxExtent => 100; // Tinggi maksimum saat muncul

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
      color: Colors.white, // Pastikan warna sama dengan background
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            child: Text(
              "$jmlLamaran ${"riwayat.subjudul1".tr} 90 ${"riwayat.subjudul2".tr}",
              style: TextStyle(fontSize: 12),
            ),
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(6),
              ),
              child: TextField(
                controller: searchController,
                onChanged: onSearchChanged,
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                decoration: InputDecoration(
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  hintText: "beranda.hint_searching".tr,
                  border: InputBorder.none,
                  prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                  contentPadding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool shouldRebuild(covariant _SliverHeaderDelegate oldDelegate) {
    return oldDelegate.jmlLamaran != jmlLamaran;
  }
}

            // Padding(
            //   padding: const EdgeInsets.all(16.0),
            //   child: Align(
            //     alignment: Alignment.bottomCenter,
            //     child: ElevatedButton.icon(
            //       onPressed: () {},
            //       icon: Icon(Icons.filter_alt),
            //       label: const Text("Filter"),
            //     ),
            //   ),
            // ),