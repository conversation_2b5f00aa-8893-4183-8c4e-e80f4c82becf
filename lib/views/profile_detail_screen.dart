import 'dart:io';
import 'dart:typed_data';

import 'package:crop_your_image/crop_your_image.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ProfileDetailScreen extends StatefulWidget {
  const ProfileDetailScreen({super.key});

  @override
  State<ProfileDetailScreen> createState() => _ProfileDetailScreenState();
}

class _ProfileDetailScreenState extends State<ProfileDetailScreen> {
  final ProfileController profileController = Get.find<ProfileController>();

  final ImagePicker _picker = ImagePicker();

  final prefs = Get.find<FlutterSecureStorage>();
  String? pin;
  String? nama;
  String? email;
  String? image;
  String? noTelp;
  String? tempatLahir;
  String? tglLahir;
  String? jk;
  String? alamat;

  void loadPref() async {
    pin = await prefs.read(key: "pin") ?? '';
    nama = await prefs.read(key: "nama") ?? '';
    tempatLahir = await prefs.read(key: "tempat_lahir") ?? '';
    tglLahir = await prefs.read(key: "tgl_lahir") ?? '';
    jk = await prefs.read(key: "jenis_kelamin") ?? '';
    alamat = await prefs.read(key: "alamat") ?? '';
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    loadPref();
  }

  Future<void> pickImage() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();

      // Mendapatkan dimensi gambar
      final image = await decodeImageFromList(bytes);
      final imageWidth = image.width.toDouble();
      final imageHeight = image.height.toDouble();

      final cropController = CropController();

      showDialog(
        context: context,
        builder: (context) {
          Uint8List? croppedData;
          bool isCropping = false;

          return StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                insetPadding:
                    EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: SingleChildScrollView(
                  // Membungkus kolom dengan scrollable
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height *
                          0.8, // Membatasi tinggi dialog
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: imageHeight >
                                  MediaQuery.of(context).size.height * 0.6
                              ? MediaQuery.of(context).size.height * 0.6
                              : imageHeight, // Mengatur tinggi sesuai ukuran gambar
                          width: imageWidth,
                          child: Stack(
                            children: [
                              Crop(
                                controller: cropController,
                                image: bytes,
                                aspectRatio: 1,
                                onCropped: (result) {
                                  switch (result) {
                                    case CropSuccess(:final croppedImage):
                                      croppedData = croppedImage;
                                      break;
                                    case CropFailure(:final cause):
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: Text('error.error'.tr),
                                          content: Text(
                                              '${"error.crop_image".tr} $cause'),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context),
                                              child: Text('tombol.ok'.tr),
                                            ),
                                          ],
                                        ),
                                      );
                                      break;
                                  }
                                  setState(() => isCropping = false);

                                  if (croppedData != null) {
                                    final croppedFile = File(
                                        '${Directory.systemTemp.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png');
                                    croppedFile.writeAsBytesSync(croppedData!);

                                    // Tutup dialog crop, lanjut ke upload
                                    Navigator.of(context).pop();
                                    _showUploadDialog(croppedFile);
                                  }
                                },
                              ),
                              if (isCropping)
                                Center(child: CircularProgressIndicator()),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() => isCropping = true);
                            cropController.crop();
                          },
                          icon: Icon(Icons.crop, color: Colors.black),
                          label: Text("tombol.crop".tr,
                              style: TextStyle(color: Colors.black)),
                        ),
                        const SizedBox(height: 10),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text("tombol.batal".tr,
                              style: TextStyle(color: Colors.black)),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      );
    }
  }

  void _showUploadDialog(File file) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          title: Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Text(
              "image.upload".tr,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 100,
                  child: ClipOval(
                    child: Image.file(
                      file,
                      height: 200,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        pickImage();
                      },
                      icon: Icon(Icons.photo, color: Colors.black),
                      label: Text("tombol.cari_foto".tr,
                          style: TextStyle(color: Colors.black)),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => profileController.isLoading.value
                          ? CircularProgressIndicator()
                          : ElevatedButton.icon(
                              onPressed: () {
                                profileController.uploadPhoto(file);
                              },
                              icon:
                                  Icon(Icons.cloud_upload, color: Colors.black),
                              label: Text("image.upload".tr,
                                  style: TextStyle(color: Colors.black)),
                            ),
                    ),
                  ],
                )
              ],
            ),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(top: 5, bottom: 20),
              child: Center(
                child: SizedBox(
                  height: 25,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text("tombol.batal".tr,
                        style: TextStyle(color: Colors.black)),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "profil.profildcv".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Material(
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(8), // Atur radius border
                    side: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1), // Warna dan ketebalan border
                  ),
                  color: Colors.white,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Stack(
                          children: [
                            GestureDetector(
                              onTap: () {
                                pickImage();
                              },
                              child: Container(
                                width: 100,
                                height: 100,
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle, boxShadow: []),
                                child: CircleAvatar(
                                  radius: 60, // Warna background jika error
                                  child: ClipOval(
                                    child: Obx(
                                      () => (isValidImageUrl(profileController
                                                  .rxImage.value) &&
                                              profileController
                                                  .rxImage.value.isNotEmpty)
                                          ? Image.network(
                                              profileController.rxImage.value,
                                              fit: BoxFit.cover,
                                              width: 100,
                                              height: 100,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              ImageAssets.logo,
                                              fit: BoxFit.cover,
                                              width: 100,
                                              height: 100,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: GestureDetector(
                                onTap: () {
                                  pickImage();
                                },
                                child: Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color:
                                        const Color.fromARGB(255, 24, 82, 154),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Text(
                                nama ?? '',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            jk != "Laki-Laki"
                                ? Icon(Icons.female)
                                : Icon(Icons.male),
                          ],
                        ),
                        Divider(
                          color: Colors.grey.shade300,
                          height: 20,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_month_outlined,
                              size: 20,
                              color: Colors.grey.shade500,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              "$tempatLahir, $tglLahir",
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 20,
                              color: Colors.grey.shade500,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Obx(
                              () => Text(
                                profileController.noTelp.value,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Material(
                              color: Colors
                                  .transparent, // Hindari warna default Material
                              child: InkWell(
                                borderRadius: BorderRadius.circular(100),
                                onTap: () {
                                  Get.toNamed(Routes.changePhoneNumber);
                                },
                                child: Icon(
                                  Icons.edit,
                                  size: 20,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.email,
                              size: 20,
                              color: Colors.grey.shade500,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Obx(
                              () => Text(
                                profileController.rxEmail.value,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Material(
                              color: Colors
                                  .transparent, // Hindari warna default Material
                              child: InkWell(
                                borderRadius: BorderRadius.circular(100),
                                onTap: () {
                                  Get.toNamed(Routes.changeEmail);
                                },
                                child: Icon(
                                  Icons.edit,
                                  size: 20,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 20,
                              color: Colors.grey.shade500,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.7,
                              child: Text(
                                alamat ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.grey.shade600,
                                ),
                                maxLines: 5,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.justify,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
