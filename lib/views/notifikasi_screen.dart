import 'package:digital_cv_mobile/controllers/notifikasi_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/models/notifikasi_model.dart';
import 'package:digital_cv_mobile/views/riwayat_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:url_launcher/url_launcher.dart';

class NotifikasiScreen extends StatefulWidget {
  const NotifikasiScreen({super.key});

  @override
  State<NotifikasiScreen> createState() => _NotifikasiScreenState();
}

class _NotifikasiScreenState extends State<NotifikasiScreen> {
  final NotifikasiController notifikasiController =
      Get.find<NotifikasiController>();
  final PagingController<int, NotifikasiModel> _pagingController =
      PagingController(firstPageKey: 1);
  static const _pageSize = 5;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ever(notifikasiController.refreshNotifikasi, (_) {
        _pagingController.refresh(); // refresh list di beranda
      });
      _pagingController.addPageRequestListener((pageKey) {
        _fetchPage(pageKey);
      });

      _pagingController.refresh();
      _pagingController.notifyPageRequestListeners(1); // ⬅️ PENTING
    });
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // ✅ Cegah pemanggilan API jika sudah tahu totalPage dan pageKey > totalPage
      final int totalPage = notifikasiController.totalPage.value;
      if (totalPage != 0 && pageKey > totalPage) {
        if (kDebugMode) {
          print('❌ Page $pageKey melebihi totalPage ($totalPage), SKIP fetch.');
        }
        _pagingController.appendLastPage([]);
        return;
      }

      final newItems = await notifikasiController.getNotif(
        pageKey,
        _pageSize,
      );

      // Hitung ulang totalPage kalau belum terisi
      final isLastPage = pageKey >= notifikasiController.totalPage.value ||
          newItems.length < _pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }

      if (kDebugMode) {
        print('✅ FETCH PAGE $pageKey');
        print('API response count: ${newItems.length}');
        print('Total list after append: ${_pagingController.itemList?.length}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('❌ Error saat fetch page $pageKey: $error');
      }
      _pagingController.error = error;
    }
  }

  Future<void> _onRefresh() async {
    notifikasiController.totalPage.value = 0; // Optional: reset totalPage
    _pagingController.refresh();
  }

  void launchWeb(String url) async {
    Uri googleUrl = Uri.parse(url);

    if (await canLaunchUrl(googleUrl)) {
      await launchUrl(
        googleUrl,
        mode: LaunchMode.externalApplication,
      );
    } else {
      // showAnimatedSnackbarError(context, "Tidak dapat menu.");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
          child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            forceElevated: true,
            expandedHeight: 60, // Batasi tinggi maksimal
            toolbarHeight: 60,
            floating: true,
            pinned: false,
            snap: true,
            backgroundColor: Colors.white, // Warna tetap putih
            elevation: 0,
            shadowColor: Colors.black.withOpacity(0.0),
            automaticallyImplyLeading: false,
            title: const Text(''),
            leading: IconButton(
              color: Colors.black,
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Get.back(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                padding: EdgeInsets.only(left: 30),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Align(
                    alignment: Alignment.centerLeft, // Tetap rata kiri
                    child: Text(
                      "notifikasi.notifikasi".tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        body: RefreshIndicator(
            onRefresh: _onRefresh,
            child: CustomScrollView(
              slivers: [
                // Obx(
                //   () => SliverPersistentHeader(
                //     key: ValueKey(notifikasiController
                //         .jmlNotifikasiUnread), // ⬅️ ini bikin header rebuild
                //     pinned: true,
                //     floating: false,
                //     delegate: _SliverHeaderDelegate(
                //         jmlNotifikasi:
                //             notifikasiController.jmlNotifikasiUnread.value),
                //   ),
                // ),
                PagedSliverList(
                  pagingController: _pagingController,
                  builderDelegate: PagedChildBuilderDelegate<NotifikasiModel>(
                    itemBuilder: (context, item, index) => Padding(
                      padding:
                          const EdgeInsets.only(bottom: 8, left: 8, right: 8),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(0),
                          border: const Border(
                            bottom: BorderSide(
                              color: Colors.grey,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: InkWell(
                          onTap: () async {
                            await notifikasiController.updateNotif(
                              penerima: item.penerima,
                              pengirim: item.pengirim,
                              tgl: item.tglKirim,
                            );
                            Get.to(RiwayatScreen());
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: Colors.grey.shade300,
                                      radius: 20, // Warna background jika error
                                      child: ClipOval(
                                        child: (isValidImageUrl(item.logoUrl) &&
                                                item.logoUrl.isNotEmpty)
                                            ? Image.network(
                                                item.logoUrl,
                                                fit: BoxFit.cover,
                                                width: 100,
                                                height: 100,
                                                errorBuilder: (context, error,
                                                    stackTrace) {
                                                  return Image.asset(
                                                    ImageAssets.logo2,
                                                    fit: BoxFit.cover,
                                                    width: 30,
                                                    height: 30,
                                                  );
                                                },
                                              )
                                            : Image.asset(
                                                ImageAssets.logo2,
                                                fit: BoxFit.cover,
                                                width: 30,
                                                height: 30,
                                              ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: 5,
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            item.label,
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            item.waktu,
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.normal,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    item.status != "Dibaca"
                                        ? Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              color: ColorAsset.primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              "notifikasi.txt_baru".tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black,
                                              ),
                                            ),
                                          )
                                        : SizedBox(),
                                  ],
                                ),
                                SizedBox(height: 8),
                                Text(
                                  item.isi,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    noItemsFoundIndicatorBuilder: (context) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/empty_data_notif.png',
                            width: 200,
                            height: 200,
                          ),
                          SizedBox(height: 20),
                          Text(
                            "notifikasi.notifikasi_empty".tr,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 10),
                          Text(
                            "notifikasi.notifikasi_desc".tr,
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
      )),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final int jmlNotifikasi;

  _SliverHeaderDelegate({
    required this.jmlNotifikasi,
  });

  @override
  double get minExtent => 40; // Tinggi minimum saat tersembunyi
  @override
  double get maxExtent => 40; // Tinggi maksimum saat muncul

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    if (jmlNotifikasi > 0) {
      return Container(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
        color: Colors.white, // Pastikan warna sama dengan background
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                textAlign: TextAlign.center,
                jmlNotifikasi == 0
                    ? "notifikasi.notifikasi_empty".tr
                    : "${"notifikasi.ada".tr} $jmlNotifikasi ${"notifikasi.ada_desc".tr}",
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      );
    } else {
      return SizedBox
          .shrink(); // Kembalikan widget kosong jika tidak ada notifikasi
    }
  }

  @override
  bool shouldRebuild(covariant _SliverHeaderDelegate oldDelegate) {
    return oldDelegate.jmlNotifikasi != jmlNotifikasi;
  }
}
