import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/views/ubah_password_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class AkunScreen extends StatefulWidget {
  const AkunScreen({super.key});

  @override
  State<AkunScreen> createState() => _AkunScreenState();
}

class _AkunScreenState extends State<AkunScreen> {
  final ProfileController profileController = Get.find<ProfileController>();
  final prefsShared = Get.find<SharedPreferences>();
  final TextEditingController _controller =
      TextEditingController(text: "Rahasia123");
  final prefs = Get.find<FlutterSecureStorage>();
  String? email;
  String? noTelp;

  void loadPref() async {
    email = await prefs.read(key: "email") ?? '';
    noTelp = await prefs.read(key: "no_telp") ?? '';

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    loadPref();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "akun.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "akun.email".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Obx(
                                () => Text(
                                  profileController.rxEmail.value.isEmpty
                                      ? '-'
                                      : profileController.rxEmail.value,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),
                              TextButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    Get.toNamed(Routes.changeEmail);
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_ubah_email".tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                child: Text(
                                  "tombol.ubah".tr,
                                  style: TextStyle(
                                    color: ColorAsset.primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            ],
                          ),
                          Text(
                            "akun.password".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: TextField(
                                  readOnly: true,
                                  controller: _controller,
                                  obscureText: true, // Menyembunyikan teks
                                  decoration: InputDecoration(
                                    border: InputBorder
                                        .none, // Menghilangkan border
                                    hintText: 'daftar.txt_password'
                                        .tr, // Opsional: Menampilkan hint jika ingin
                                  ),
                                  style: TextStyle(
                                      fontSize:
                                          16), // Opsional: Sesuaikan tampilan teks
                                ),
                              ),
                              TextButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    Get.to(UbahPasswordScreen(
                                      email: profileController.rxEmail.value,
                                      noTelp: profileController.noTelp.value,
                                    ));
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_ubah_password".tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                child: Text(
                                  "tombol.ubah".tr,
                                  style: TextStyle(
                                    color: ColorAsset.primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            ],
                          ),
                          Text(
                            "akun.noTelp".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Obx(
                                () => Text(
                                  profileController.noTelp.value.isEmpty
                                      ? "-"
                                      : profileController.noTelp.value,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),
                              TextButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    Get.toNamed(Routes.changePhoneNumber);
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_ubah_no_telp".tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                child: Text(
                                  "tombol.ubah".tr,
                                  style: TextStyle(
                                    color: ColorAsset.primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            ],
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "akun.visibilitas".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  Text(
                                    "akun.visibilitas_title".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Obx(
                                    () => Container(
                                      padding: EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                          color: Colors.grey.shade300,
                                        ),
                                      ),
                                      child: Text(
                                        profileController.visibilitas.value ==
                                                "1"
                                            ? "akun.visibilitas_aktif".tr
                                            : "akun.visibilitas_deaktif".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: profileController
                                                      .visibilitas.value ==
                                                  "1"
                                              ? Colors.blue
                                              : Colors.blue,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              TextButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    visibilitasProfile(context);
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_profile_visibilitas"
                                            .tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                child: Text(
                                  "tombol.ubah".tr,
                                  style: TextStyle(
                                    color: ColorAsset.primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "akun.status_kerja".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Obx(
                                    () => Container(
                                      padding: EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                          color: Colors.grey.shade300,
                                        ),
                                      ),
                                      child: Text(
                                        profileController.statusKerja.value ==
                                                "Sedang aktif mencari kerja"
                                            ? "akun.status_kerja_aktif".tr
                                            : profileController
                                                        .statusKerja.value ==
                                                    "Terbuka untuk peluang kerja"
                                                ? "akun.status_kerja_terbuka".tr
                                                : "akun.status_kerja_tidak_aktif"
                                                    .tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: profileController
                                                      .statusKerja.value ==
                                                  "1"
                                              ? Colors.blue
                                              : Colors.blue,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              TextButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    statusKetersediaanBekerja(context);
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_profile_visibilitas"
                                            .tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                child: Text(
                                  "tombol.ubah".tr,
                                  style: TextStyle(
                                    color: ColorAsset.primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 24),
                          Divider(color: Colors.grey.shade300),
                          SizedBox(height: 16),
                          if (prefsShared.getBool("auth") == true)
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: () async {
                                  final prefs = Get.find<SharedPreferences>();
                                  final isLoggedIn =
                                      prefs.getBool("auth") ?? false;
                                  final isGoogle =
                                      prefs.getBool("isGoogle") ?? false;
                                  if (isLoggedIn || isGoogle) {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "tombol.peringatan".tr,
                                        "tombol.peringatan_hapus_akun".tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      await launchUrl(Uri.parse(
                                          "https://digitalcv.id/account-deletion"));
                                    }
                                  } else {
                                    var confirm = await showConfirmationDialog(
                                        context,
                                        "dialog_out.explore_judul".tr,
                                        "dialog_out.explore_hapus_akun".tr,
                                        "konfirmasi.iya2".tr,
                                        "konfirmasi.tidak".tr);
                                    if (confirm == true) {
                                      Get.offAllNamed('/login');
                                    }
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                ),
                                child: Text(
                                  "tombol.hapus_akun".tr,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void visibilitasProfile(BuildContext context) {
    showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        builder: (context) {
          return Container(
            padding: EdgeInsets.all(16),
            child: Obx(
              () => Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "akun.visibilitas_title".tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          Get.back();
                        },
                        icon: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    "akun.visibilitas_subtitle".tr,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: profileController.visibilitas.value == '1'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          title: Stack(
                            children: [
                              Column(
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        "akun.visibilitas_aktif".tr,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(4)),
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          "akun.visibilitas_recommend".tr,
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.normal,
                                              color: Colors.blue),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    "akun.visibilitas_aktif_desc".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          onTap: () {
                            if (profileController.isLoading.value) return;
                            profileController.ubahVisibility("1");
                            // Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: profileController.visibilitas.value == '0'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "akun.visibilitas_deaktif".tr,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                "akun.visibilitas_deaktif_desc".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          onTap: () {
                            if (profileController.isLoading.value) return;
                            profileController.ubahVisibility("0");
                            // Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
          );
        });
  }

  void statusKetersediaanBekerja(BuildContext context) {
    showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        builder: (context) {
          return Container(
            padding: EdgeInsets.all(16),
            child: Obx(
              () => Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "akun.status_kerja".tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          Get.back();
                        },
                        icon: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    "akun.status_kerja_subtitle".tr,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: profileController.statusKerja.value ==
                                'Sedang aktif mencari kerja'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          title: Stack(
                            children: [
                              Column(
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        "akun.status_kerja_aktif".tr,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(4)),
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          "akun.visibilitas_recommend".tr,
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.normal,
                                              color: Colors.blue),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    "akun.status_kerja_aktif_desc".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          onTap: () {
                            if (profileController.isLoading.value) return;
                            profileController
                                .ubahStatusKerja("Sedang aktif mencari kerja");
                            // Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: profileController.statusKerja.value ==
                                'Terbuka untuk peluang kerja'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "akun.status_kerja_terbuka".tr,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                "akun.status_kerja_terbuka_desc".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          onTap: () {
                            if (profileController.isLoading.value) return;
                            profileController
                                .ubahStatusKerja("Terbuka untuk peluang kerja");
                            // Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: profileController.statusKerja.value ==
                                'Tidak mencari kerja'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "akun.status_kerja_tidak_aktif".tr,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                "akun.status_kerja_tidak_aktif_desc".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          onTap: () {
                            if (profileController.isLoading.value) return;
                            profileController
                                .ubahStatusKerja("Tidak mencari kerja");
                            // Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
          );
        });
  }
}
