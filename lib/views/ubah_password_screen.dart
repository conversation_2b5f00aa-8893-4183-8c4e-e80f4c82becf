import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/daftar_controller.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class UbahPasswordScreen extends StatefulWidget {
  final String? email;
  final String? noTelp;
  const UbahPasswordScreen({super.key, this.email, this.noTelp});

  @override
  State<UbahPasswordScreen> createState() => _UbahPasswordScreenState();
}

class _UbahPasswordScreenState extends State<UbahPasswordScreen> {
  final LoginController authController = Get.find<LoginController>();
  final DaftarController daftarController =
      Get.put(DaftarController(), permanent: true, tag: "daftar");
  final prefs = Get.find<FlutterSecureStorage>();
  final TextEditingController _passLamaController = TextEditingController();
  final TextEditingController _passBaruController = TextEditingController();
  final TextEditingController _passBaruConfirmController =
      TextEditingController();
  final TextEditingController _otpController = TextEditingController();

  final key = GlobalKey<FormState>();
  bool _isObscure = true; // State untuk menyembunyikan/memperlihatkan password
  bool _isObscure2 = true;
  bool _isObscure3 = true;
  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "ubah_password.hint0_blank".tr,
      "isError": false,
    },
    {
      "message": "ubah_password.hint1_blank".tr,
      "isError": false,
    },
    {
      "message": "ubah_password.hint2_blank".tr,
      "isError": false,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_OTP".tr,
    },
  ];

  // Fungsi validasi password yang lebih kuat
  String? validatePassword(String password) {
    if (password.isEmpty) {
      return "controller.validasi.password_kosong".tr;
    }

    if (password.length < 8) {
      return "controller.validasi.min_password".tr;
    }

    // Cek apakah mengandung huruf besar & huruf kecil
    if (!RegExp(r'[A-Z]').hasMatch(password) ||
        !RegExp(r'[a-z]').hasMatch(password)) {
      return "controller.validasi.has_uppercase".tr;
    }

    // Cek apakah mengandung angka
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return "controller.validasi.has_number".tr;
    }

    // Cek apakah mengandung karakter khusus
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return "${"controller.validasi.has_special_char".tr} (!@#\$%^&*(),.?\":{}|<>)";
    }

    return null; // Password valid
  }

  // Fungsi untuk mengecek kriteria password individual
  Map<String, bool> getPasswordCriteria(String password) {
    return {
      'minLength': password.length >= 8,
      'hasUppercase': RegExp(r'[A-Z]').hasMatch(password) &&
          RegExp(r'[a-z]').hasMatch(password),
      'hasNumber': RegExp(r'[0-9]').hasMatch(password),
      'hasSpecialChar': RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
    };
  }

  // Widget untuk menampilkan kriteria password
  Widget buildPasswordCriteria() {
    final criteria = getPasswordCriteria(_passBaruController.text);

    return Container(
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "controller.kriteria_password".tr,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          SizedBox(height: 8),
          _buildCriteriaItem(
              "controller.min_length".tr, criteria['minLength']!),
          _buildCriteriaItem(
              "controller.has_uppercase".tr, criteria['hasUppercase']!),
          _buildCriteriaItem(
              "controller.has_number".tr, criteria['hasNumber']!),
          _buildCriteriaItem("${"controller.has_special_char".tr} (!@#\$%^&*)",
              criteria['hasSpecialChar']!),
        ],
      ),
    );
  }

  Widget _buildCriteriaItem(String text, bool isValid) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 16,
            color: isValid ? Colors.green : Colors.grey,
          ),
          SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: isValid ? Colors.green : Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "ubah_password.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            width: double.infinity, // Pastikan kontainer memenuhi layar
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: key,
                    child: Column(
                      children: [
                        _buildTextFieldPassLama(
                          "ubah_password.hint0".tr,
                          errorsMessage[0]['isError']
                              ? errorsMessage[0]['message']
                              : '',
                          _passLamaController,
                          TextInputType.visiblePassword,
                        ),
                        const SizedBox(height: 10),
                        _buildTextFieldPassBaru(
                          "ubah_password.hint1".tr,
                          errorsMessage[1]['isError']
                              ? errorsMessage[1]['message']
                              : '',
                          _passBaruController,
                          TextInputType.visiblePassword,
                        ),
                        if (_passBaruController.text.isNotEmpty)
                          buildPasswordCriteria(),
                        const SizedBox(height: 10),
                        _buildTextFieldKonPassBaru(
                          "ubah_password.hint2".tr,
                          errorsMessage[2]['isError']
                              ? errorsMessage[2]['message']
                              : '',
                          _passBaruConfirmController,
                          TextInputType.visiblePassword,
                        ),
                        const SizedBox(height: 10),
                        Obx(
                          () => Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 1, // Memberikan ruang lebih ke tombol
                                child: SizedBox(
                                  height: 50,
                                  child: ElevatedButton(
                                    onPressed: (daftarController
                                                .isLoadingOTP.value ||
                                            daftarController
                                                    .otpCountdown.value >
                                                0)
                                        ? null // disable tombol saat loading
                                        : () async {
                                            LogService.log.i(
                                                "Requesting OTP for email: ${widget.email}, phone: ${widget.noTelp}");
                                            // if (widget.email != "" ||
                                            //     widget.noTelp != "") {
                                            //   showAnimatedSnackbarError(
                                            //       context,
                                            //       "daftar.txt_error_email_hp"
                                            //           .tr);
                                            // } else {
                                            daftarController.reqOtp(
                                                widget.email ?? '',
                                                widget.noTelp ?? '',
                                                'ubah_password');
                                            // }
                                          },
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 20,
                                        vertical: 6,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.horizontal(
                                          left: Radius.circular(6),
                                        ),
                                        side: BorderSide(
                                          color: Color(0xFFFF9800),
                                        ),
                                      ),
                                      textStyle: const TextStyle(fontSize: 14),
                                      backgroundColor: Color(0xFFFF9800),
                                    ),
                                    child: daftarController.otpCountdown.value >
                                            0
                                        ? Text(
                                            "${daftarController.otpCountdown.value}s",
                                            style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold),
                                          )
                                        : const Text(
                                            "OTP",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold),
                                          ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex:
                                    3, // Memberikan lebih banyak ruang ke TextFormField
                                child: FormTextFiled(
                                  controller: _otpController,
                                  keyboardType: TextInputType.text,
                                  hintText: "daftar.txt_OTP".tr,
                                  type: "otp",
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (errorsMessage[3]['isError'] == true)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    errorsMessage[3]['message'],
                                    style: TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: () {
                      // Navigasi ke halaman lupa password
                      Get.toNamed(Routes.lupaPassword);
                    },
                    child: Text(
                      "login.txt_forgot_pass".tr,
                      style: TextStyle(
                        color: ColorAsset.secodaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          bottom: MediaQuery.of(context).viewInsets.bottom > 0
              ? MediaQuery.of(context).viewInsets.bottom + 8.0
              : 16.0,
          top: 8.0,
        ),
        child: Obx(
          () => authController.isLoading.value
              ? const SizedBox(
                  width: 30,
                  height: 50,
                  child: Center(child: CircularProgressIndicator()),
                )
              : SizedBox(
                  width: Get.width,
                  height: 45,
                  child: ElevatedButton(
                    onPressed: () async {
                      for (var error in errorsMessage) {
                        setState(() {
                          error['isError'] = false;
                        });
                      }
                      // Validasi password dengan kriteria yang lebih kuat
                      String? passwordError =
                          validatePassword(_passBaruController.text);
                      if (passwordError != null) {
                        showAnimatedSnackbarError(context, passwordError);
                        return;
                      }
                      if (_passLamaController.text.isEmpty) {
                        setState(() {
                          errorsMessage[0]['isError'] = true;
                        });
                        return;
                      }
                      if (_passBaruController.text.isEmpty) {
                        setState(() {
                          errorsMessage[1]['isError'] = true;
                        });
                        return;
                      }
                      if (_passBaruConfirmController.text.isEmpty) {
                        setState(() {
                          errorsMessage[2]['isError'] = true;
                        });
                        return;
                      }
                      if (_otpController.text.isEmpty) {
                        setState(() {
                          errorsMessage[3]['isError'] = true;
                          errorsMessage[3]['message'] =
                              "daftar.txt_error_OTP".tr;
                        });
                        return;
                      }
                      if (_passBaruController.text !=
                          _passBaruConfirmController.text) {
                        showAnimatedSnackbarError(
                            context, "ubah_password.validator_equal".tr);
                        return;
                      }

                      await authController.changePassword(
                        _passLamaController.text,
                        _passBaruController.text,
                        _passBaruConfirmController.text,
                        _otpController.text,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      textStyle: const TextStyle(fontSize: 14),
                    ),
                    child: Text(
                      "tombol.simpan".tr,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildTextFieldPassLama(
    String hintText,
    String validator,
    TextEditingController controller,
    TextInputType inputType,
  ) {
    return FormTextFiled(
      controller: controller,
      keyboardType: inputType,
      hintText: hintText,
      obscureText: _isObscure,
      suffixIcon: IconButton(
        icon: Icon(
          _isObscure ? Icons.visibility_off : Icons.visibility,
        ),
        onPressed: () {
          setState(() {
            _isObscure = !_isObscure; // Toggle antara true/false
          });
        },
      ),
      errorText: validator == "" ? null : validator,
    );
  }

  Widget _buildTextFieldPassBaru(
    String hintText,
    String validator,
    TextEditingController controller,
    TextInputType inputType,
  ) {
    return FormTextFiled(
      controller: controller,
      keyboardType: inputType,
      hintText: hintText,
      obscureText: _isObscure2,
      suffixIcon: IconButton(
        icon: Icon(
          _isObscure2 ? Icons.visibility_off : Icons.visibility,
        ),
        onPressed: () {
          setState(() {
            _isObscure2 = !_isObscure2; // Toggle antara true/false
          });
        },
      ),
      errorText: validator == "" ? null : validator,
      onChanged: (value) {
        // Validasi real-time password
        String? passwordError = validatePassword(value);
        setState(() {
          errorsMessage[1]['isError'] = passwordError != null;
          if (passwordError != null) {
            errorsMessage[1]['message'] = passwordError;
          }
        });
      },
    );
  }

  Widget _buildTextFieldKonPassBaru(
    String hintText,
    String validator,
    TextEditingController controller,
    TextInputType inputType,
  ) {
    return FormTextFiled(
      controller: controller,
      keyboardType: inputType,
      hintText: hintText,
      obscureText: _isObscure3,
      suffixIcon: IconButton(
        icon: Icon(
          _isObscure3 ? Icons.visibility_off : Icons.visibility,
        ),
        onPressed: () {
          setState(() {
            _isObscure3 = !_isObscure3; // Toggle antara true/false
          });
        },
      ),
      errorText: validator == "" ? null : validator,
      onChanged: (value) {
        setState(() {
          if (value != _passBaruConfirmController.text) {
            errorsMessage[2]['isError'] = true;
            errorsMessage[2]['message'] = "Password tidak sama";
          } else {
            errorsMessage[2]['isError'] = false;
          }
        });
      },
    );
  }
}
