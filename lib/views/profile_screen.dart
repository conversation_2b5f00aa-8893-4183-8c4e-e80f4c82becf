import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/components/konfirmasi_login.dart';
import 'package:digital_cv_mobile/controllers/notifikasi_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ProfileController profileController = Get.find<ProfileController>();
  final NotifikasiController notifikasiController =
      Get.put(NotifikasiController());
  final prefs = Get.find<FlutterSecureStorage>();
  final sharedPrefs = Get.find<SharedPreferences>();
  String? pin;
  String? nama;
  String? email;
  String? image;
  String? noTelp;

  void loadPref() async {
    pin = await prefs.read(key: "pin") ?? '';
    nama = await prefs.read(key: "nama") ?? '';
    image = await prefs.read(key: "image") ?? '';

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    loadPref();
    notifikasiController.countNewNotif();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle, boxShadow: []),
                        child: CircleAvatar(
                          radius: 60, // Warna background jika error
                          child: ClipOval(
                            child: Obx(
                              () => (isValidImageUrl(
                                          profileController.rxImage.value) &&
                                      profileController
                                          .rxImage.value.isNotEmpty)
                                  ? Image.network(
                                      profileController.rxImage.value,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return const Icon(
                                          Icons.person,
                                          size: 20,
                                        );
                                      },
                                    )
                                  : Image.asset(
                                      ImageAssets.logo,
                                      fit: BoxFit.cover,
                                      width: 60,
                                      height: 60,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return const Icon(
                                          Icons.person,
                                          size: 20,
                                        );
                                      },
                                    ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        flex: 8,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              nama ?? "error.name_empty".tr,
                              style: TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            Obx(
                              () => Text(
                                profileController.noTelp.value,
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          SizedBox(
                            width: 10,
                          ),
                          Stack(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.grey,
                                    width: 1,
                                  ),
                                ),
                                // padding:
                                //     const EdgeInsets.only(left: 12),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(100),
                                  onTap: () async {
                                    final prefs = Get.find<SharedPreferences>();
                                    final isLoggedIn =
                                        prefs.getBool("auth") ?? false;
                                    final isGoogle =
                                        prefs.getBool("isGoogle") ?? false;
                                    if (isLoggedIn || isGoogle) {
                                      Get.toNamed(Routes.notifikasi);
                                    } else {
                                      var confirm =
                                          await showConfirmationDialog(
                                              context,
                                              "dialog_out.explore_judul".tr,
                                              "dialog_out.explore_notifikasi"
                                                  .tr,
                                              "konfirmasi.iya2".tr,
                                              "konfirmasi.tidak".tr);
                                      if (confirm == true) {
                                        Get.offAllNamed('/login');
                                      }
                                    }
                                  },
                                  child: const Icon(
                                    Icons.notifications_none,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Obx(() =>
                                  notifikasiController.jmlNewNotif.value > 0
                                      ? Positioned(
                                          right: 7,
                                          top: 5,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              Icon(
                                                Icons.circle,
                                                size: 12,
                                                color: Colors.red,
                                              ),
                                              Align(
                                                alignment: Alignment.center,
                                                child: Text(
                                                  "${notifikasiController.jmlNewNotif.value}",
                                                  style: TextStyle(
                                                      fontSize: 10,
                                                      color: Colors.white),
                                                ),
                                              )
                                            ],
                                          ))
                                      : SizedBox())
                            ],
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          GestureDetector(
                            onTap: () {
                              Get.toNamed(Routes.setting);
                            },
                            child: Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.grey,
                                    width: 1,
                                  ),
                                ),
                                child: const Icon(Icons.settings)),
                          ),
                        ],
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 40,
                  ),
                  if (sharedPrefs.getBool("auth") == true ||
                      sharedPrefs.getBool("isGoogle") == true)
                    Column(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "profil.judul1".tr,
                            style: TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.grey[200]!,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Material(
                                color: Colors
                                    .transparent, // Hindari warna default Material
                                child: InkWell(
                                  onTap: () {
                                    Get.toNamed(Routes.profile);
                                  },
                                  child: Container(
                                    height: 40,
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    child: Row(
                                      children: [
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Icon(
                                            Icons.article_outlined,
                                            color: Colors.grey[500],
                                          ),
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "profil.profildcv".tr,
                                            style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey[500]),
                                          ),
                                        ),
                                        Expanded(
                                          child: Align(
                                            alignment: Alignment.centerRight,
                                            child: Icon(
                                              Icons.keyboard_arrow_right,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Divider(
                                  thickness: 1,
                                  height: 1,
                                  color: Colors.grey[200],
                                ),
                              ),
                              Material(
                                color: Colors
                                    .transparent, // Hindari warna default Material
                                child: InkWell(
                                  onTap: () {
                                    Get.toNamed(Routes.cvAts);
                                  },
                                  child: Container(
                                    height: 40,
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    child: Row(
                                      children: [
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Icon(
                                            Icons.upload_file_outlined,
                                            color: Colors.grey[500],
                                          ),
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "profil.cv".tr,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[500],
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Align(
                                            alignment: Alignment.centerRight,
                                            child: Icon(
                                              Icons.keyboard_arrow_right,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Divider(
                                  thickness: 1,
                                  height: 1,
                                  color: Colors.grey[200],
                                ),
                              ),
                              Material(
                                color: Colors
                                    .transparent, // Hindari warna default Material
                                child: InkWell(
                                  onTap: () {
                                    Get.toNamed(Routes.riwayatHidup);
                                  },
                                  child: Container(
                                    height: 40,
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    child: Row(
                                      children: [
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Icon(
                                            Icons.person_outline_outlined,
                                            color: Colors.grey[500],
                                          ),
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "profil.rh".tr,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[500],
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Align(
                                            alignment: Alignment.centerRight,
                                            child: Icon(
                                              Icons.keyboard_arrow_right,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                              // Material(
                              //   color: Colors
                              //       .transparent, // Hindari warna default Material
                              //   child: InkWell(
                              //     onTap: () {},
                              //     child: Container(
                              //       height: 40,
                              //       width: double.infinity,
                              //       padding:
                              //           const EdgeInsets.symmetric(horizontal: 16),
                              //       child: Row(
                              //         children: [
                              //           Align(
                              //             alignment: Alignment.centerLeft,
                              //             child: Icon(
                              //               Icons.work_outline_rounded,
                              //               color: Colors.grey[500],
                              //             ),
                              //           ),
                              //           SizedBox(
                              //             width: 10,
                              //           ),
                              //           Align(
                              //             alignment: Alignment.centerLeft,
                              //             child: Text(
                              //               "profil.preferensi".tr,
                              //               style: TextStyle(
                              //                 fontSize: 14,
                              //                 color: Colors.grey[500],
                              //               ),
                              //             ),
                              //           ),
                              //           Expanded(
                              //             child: Align(
                              //               alignment: Alignment.centerRight,
                              //               child: Icon(
                              //                 Icons.keyboard_arrow_right,
                              //               ),
                              //             ),
                              //           ),
                              //         ],
                              //       ),
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                      ],
                    )
                  else
                    SizedBox(height: Get.height / 1.5, child: KonfirmasiLogin())
                  // Align(
                  //   alignment: Alignment.centerLeft,
                  //   child: Text(
                  //     "profil.judul2".tr,
                  //     style:
                  //         TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  //   ),
                  // ),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // Material(
                  //   color: Colors.white,
                  //   borderRadius: BorderRadius.circular(12),
                  //   elevation: 0,
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Material(
                  //         color: Colors
                  //             .transparent, // Hindari warna default Material
                  //         child: InkWell(
                  //           onTap: () {},
                  //           child: Container(
                  //             height: 50,
                  //             width: double.infinity,
                  //             padding:
                  //                 const EdgeInsets.symmetric(horizontal: 16),
                  //             child: Row(
                  //               children: [
                  //                 Align(
                  //                   alignment: Alignment.centerLeft,
                  //                   child: Icon(
                  //                     Icons.workspace_premium_outlined,
                  //                     color: Colors.amber,
                  //                   ),
                  //                 ),
                  //                 SizedBox(
                  //                   width: 10,
                  //                 ),
                  //                 Align(
                  //                   alignment: Alignment.centerLeft,
                  //                   child: Text(
                  //                     "profil.upgrade".tr,
                  //                     style: TextStyle(
                  //                       fontSize: 12,
                  //                       color: Colors.grey[500],
                  //                     ),
                  //                   ),
                  //                 ),
                  //                 Expanded(
                  //                   child: Align(
                  //                     alignment: Alignment.centerRight,
                  //                     child: Icon(
                  //                       Icons.keyboard_arrow_right,
                  //                     ),
                  //                   ),
                  //                 ),
                  //               ],
                  //             ),
                  //           ),
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        ));
  }
}
