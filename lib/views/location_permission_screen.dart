import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/route_manager.dart';

class LocationPermissionScreen extends StatelessWidget {
  const LocationPermissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.only(
            left: 16.0, right: 16.0, top: 50, bottom: 16.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.location_on,
                size: 100,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 20),
              Text(
                "Izin Akses Lokasi",
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                "Mengapa kami memerlukan akses lokasi <PERSON>?",
                style: const TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                "Digital CV meminta izin untuk mengakses lokasi Anda guna meningkatkan pengalaman pencarian kerja Anda.",
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () async {
                    bool serviceEnabled;
                    LocationPermission permission;

                    // Cek apakah location service aktif
                    serviceEnabled =
                        await Geolocator.isLocationServiceEnabled();
                    // if (!serviceEnabled) {
                    //   throw Exception('Location services are disabled.');
                    // }

                    // Cek permission
                    permission = await Geolocator.checkPermission();
                    if (permission == LocationPermission.denied) {
                      permission = await Geolocator.requestPermission();
                      if (permission == LocationPermission.denied) {
                        throw Exception('Location permissions are denied');
                      }
                    }

                    if (permission == LocationPermission.deniedForever) {
                      throw Exception(
                          'Location permissions are permanently denied');
                    }

                    // Ambil posisi sekarang
                    Position position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.high,
                    );

                    if (position != null) {
                      Get.back(result: position);
                    }
                  },
                  child: const Text("Berikan Izin",
                      style: TextStyle(fontSize: 16, color: Colors.black)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
