import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lamaran_model.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/models/timeline_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TimelineScreen extends StatefulWidget {
  const TimelineScreen({super.key});

  @override
  State<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends State<TimelineScreen> {
  final LowonganController lowonganController = Get.find<LowonganController>();
  final LamaranController lamaranController = Get.find<LamaranController>();
  late LamaranModel lamaran;
  LowonganModel? lowongan;
  List<TimelineModel>? timeline;
  // List<Map<String, String>> timelineDataFinal = [];

  @override
  void initState() {
    super.initState();
    final args = Get.arguments;
    if (args is LamaranModel) {
      lamaran = args;
    } else {
      // Kalau null atau salah tipe, langsung keluarin alert dan back
      final params = Get.parameters;
      if (params.isNotEmpty) {
        Future.microtask(() {
          // showAnimatedSnackbarError(context, 'Data lowongan tidak ditemukan');
          // Get.back();
          loadLamaran(params['id_lamar'] ?? "", params['id_req'] ?? "");
        });
      } else {
        Future.microtask(() {
          showAnimatedSnackbarError(context, 'error.detail_lowongan_empty'.tr);
          Get.back();
        });
      }
    }
    loadDetail(lamaran.idReq);
    loadTimeline(lamaran.idLamar);
  }

  void loadLamaran(String idLamar, String idReq) async {
    var result = await lamaranController.getRiwayatLamaran(1, 1, "",
        idLamar: idLamar, idReq: idReq);

    if (result.isNotEmpty) {
      lamaran = result[0];
    }
  }

  void loadDetail(String idReq) async {
    final result = await lowonganController.getDetailJob(idReq, 1, 1);

    if (result.isNotEmpty) {
      setState(() {
        lowongan = result.first;
      });
    }
  }

  void loadTimeline(String idLamar) async {
    final result = await lamaranController.getTimeline(idLamar);

    if (result.isNotEmpty) {
      setState(() {
        timeline = result;
        // timelineDataFinal = buildFinalTimeline(timelineData, timeline!.first);
      });
    }
  }

  final List<Map<String, dynamic>> timelineData = [
    {"deskripsi": "timeline.tahap1".tr},
    {"deskripsi": "timeline.tahap2".tr},
    {"deskripsi": "timeline.tahap3".tr},
    {"deskripsi": "timeline.tahap6".tr},
    {"deskripsi": "timeline.tahap7".tr},
    {"deskripsi": "timeline.tahap8".tr},
  ];

  // List<Map<String, String>> buildFinalTimeline(
  //   List<Map<String, String>> baseTimeline,
  //   TimelineModel? timeline,
  // ) {
  //   if (timeline == null) return baseTimeline;

  //   // Ambil tanggal dari model
  //   final tanggalList = [
  //     timeline.tglTahap1,
  //     timeline.tglTahap2,
  //     timeline.tglTahap3,
  //     timeline.tglTahap4,
  //   ];

  //   return List.generate(baseTimeline.length, (index) {
  //     final item = baseTimeline[index];
  //     return {
  //       "tanggal": tanggalList[index] != '' ? tanggalList[index] : "",
  //       "tahap": item['tahap'] ?? '',
  //       "deskripsi": item['deskripsi'] ?? '',
  //     };
  //   });
  // }

  bool isValidImageUrl(String url) {
    if (url.isEmpty) return false;

    // Check if it's a valid URL format
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: Colors.white,
          title: Text(
            "timeline.judul".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          iconTheme: IconThemeData(
            color: Colors.black,
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                    ), // Bisa dikurangi jika perlu
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius:
                                          (isValidImageUrl(lamaran.image) &&
                                                  lamaran.image.isNotEmpty)
                                              ? BorderRadius.circular(100)
                                              : BorderRadius.circular(10),
                                      child: (isValidImageUrl(lamaran.image) &&
                                              lamaran.image.isNotEmpty)
                                          ? Image.network(
                                              lamaran.image,
                                              width: 70,
                                              height: 70,
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Image.asset(
                                                  ImageAssets.logo2,
                                                  fit: BoxFit.cover,
                                                  width: 70,
                                                  height: 70,
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              ImageAssets.logo2,
                                              fit: BoxFit.cover,
                                              width: 70,
                                              height: 70,
                                            ),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            lamaran.perusahaan,
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            maxLines: 1,
                                            softWrap: true,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Text(
                                            lamaran.posisi,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    TranslationService.translateTimeAgo(
                                        lamaran.waktuLalu),
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                  Text(
                                    lamaran.tgl,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                  const SizedBox(height: 5),
                                  SizedBox(
                                    height: 30,
                                    child: AnimatedOpacity(
                                      opacity: lowongan != null ? 1.0 : 0.0,
                                      duration: Duration(milliseconds: 500),
                                      child: lowongan != null
                                          ? ElevatedButton(
                                              onPressed: () {
                                                Get.toNamed(
                                                    Routes.lowonganDetail,
                                                    arguments: lowongan);
                                              },
                                              style: ElevatedButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 6),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                              child: Text(
                                                "tombol.lihat_detail".tr,
                                                style: TextStyle(
                                                    fontSize: 13,
                                                    color: Colors.black),
                                              ),
                                            )
                                          : SizedBox.shrink(),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.end,
                          //   children: [
                          // Row(
                          //   crossAxisAlignment: CrossAxisAlignment.start,
                          //   children: [
                          //     Icon(
                          //       Icons.date_range,
                          //       size: 15,
                          //     ),
                          //     SizedBox(
                          //       width: 5,
                          //     ),
                          //     // Column(
                          //     //   crossAxisAlignment:
                          //     //       CrossAxisAlignment.start,
                          //     //   children: [
                          //     //     Text(
                          //     //       "timeline.tanggal_daftar".tr,
                          //     //       style: TextStyle(
                          //     //         fontSize: 10,
                          //     //       ),
                          //     //     ),
                          //     //     Text(
                          //     //       "01 Januari 2025 - 30 Januari 2025",
                          //     //       style: TextStyle(
                          //     //         fontSize: 10,
                          //     //       ),
                          //     //       softWrap: true,
                          //     //     ),
                          //     //   ],
                          //     // ),
                          //   ],
                          // ),
                          //   ],
                          // ),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                  child: Center(
                    child: SizedBox(
                      child: timeline == null
                          ? Center(
                              child: CircularProgressIndicator(),
                            )
                          : ListView.builder(
                              shrinkWrap:
                                  true, // ✅ Tambahkan ini agar ListView mengikuti ukuran anak-anaknya

                              physics:
                                  NeverScrollableScrollPhysics(), // ✅ Nonaktifkan scroll internal jika berada di dalam scroll lain
                              itemCount: timeline?.length,
                              itemBuilder: (context, index) {
                                bool isLast = index == timeline!.length - 1;
                                return Material(
                                  color: Colors.transparent,
                                  child: SizedBox(
                                    height: !isLast ? 90 : 90,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Tanggal
                                        SizedBox(
                                          width: 90,
                                          child: Padding(
                                            padding:
                                                const EdgeInsets.only(top: 8.0),
                                            child: Text(
                                              timeline?[index].tglTahap != ""
                                                  ? TranslationService
                                                      .translateDateString(
                                                          timeline?[index]
                                                                  .tglTahap ??
                                                              "")
                                                  : "error.jadwal_empty".tr,
                                              style: TextStyle(
                                                fontSize: 13,
                                                color:
                                                    timeline?[index].tglTahap !=
                                                            ""
                                                        ? Colors.black
                                                        : Colors.grey,
                                                fontWeight:
                                                    timeline?[index].tglTahap !=
                                                            ""
                                                        ? FontWeight.w500
                                                        : FontWeight.normal,
                                              ),
                                              softWrap: true,
                                            ),
                                          ),
                                        ),
                                        // Timeline Line & Dot
                                        SizedBox(
                                          width: 40,
                                          child: Column(
                                            children: [
                                              // Dot
                                              Container(
                                                margin: const EdgeInsets.only(
                                                    top: 4),
                                                child: CircleAvatar(
                                                  radius: 14,
                                                  backgroundColor: timeline?[
                                                                      index]
                                                                  .tglTahap !=
                                                              "" &&
                                                          timeline?[index]
                                                                  .tahap !=
                                                              'Kandidat Tidak Diterima'
                                                      ? ColorAsset.primaryColor
                                                      : timeline?[index]
                                                                  .tahap ==
                                                              'Kandidat Tidak Diterima'
                                                          ? Colors.red
                                                          : Colors
                                                              .grey.shade300,
                                                  child: Text(
                                                    "${index + 1}",
                                                    style: TextStyle(
                                                      color: timeline?[index]
                                                                      .tglTahap !=
                                                                  "" &&
                                                              timeline?[index]
                                                                      .tahap !=
                                                                  'Kandidat Tidak Diterima'
                                                          ? Colors.black
                                                          : timeline?[index]
                                                                      .tahap ==
                                                                  'Kandidat Tidak Diterima'
                                                              ? Colors.white
                                                              : Colors.grey,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 14,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              // Line
                                              if (!isLast)
                                                Expanded(
                                                  child: Container(
                                                    width: 4,
                                                    color: Colors.grey.shade300,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        // Tahap & Deskripsi
                                        Expanded(
                                          child: SizedBox(
                                            height:
                                                100, // Set height agar semua container sama
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                  left: 0, top: 2, bottom: 10),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8,
                                                      horizontal: 12),
                                              decoration: BoxDecoration(
                                                color:
                                                    timeline?[index].tglTahap !=
                                                            ""
                                                        ? Colors.white
                                                        : Colors.grey.shade100,
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                border: Border.all(
                                                  color: timeline?[index]
                                                                  .tglTahap !=
                                                              "" &&
                                                          timeline?[index]
                                                                  .tahap !=
                                                              'Kandidat Tidak Diterima'
                                                      ? ColorAsset.primaryColor
                                                          .withOpacity(0.2)
                                                      : timeline?[index]
                                                                  .tahap ==
                                                              'Kandidat Tidak Diterima'
                                                          ? Colors.red
                                                              .withOpacity(0.2)
                                                          : Colors
                                                              .grey.shade200,
                                                  width: 1,
                                                ),
                                                boxShadow: timeline?[index]
                                                            .tglTahap !=
                                                        ""
                                                    ? [
                                                        BoxShadow(
                                                          color: timeline?[
                                                                          index]
                                                                      .tahap ==
                                                                  'Kandidat Tidak Diterima'
                                                              ? Colors.red
                                                                  .withOpacity(
                                                                      0.08)
                                                              : ColorAsset
                                                                  .primaryColor
                                                                  .withOpacity(
                                                                      0.08),
                                                          blurRadius: 6,
                                                          offset: Offset(0, 2),
                                                        )
                                                      ]
                                                    : [],
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    "Tahap ${index + 1}",
                                                    style: TextStyle(
                                                      fontSize: 15,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: timeline?[index]
                                                                  .tglTahap !=
                                                              ""
                                                          ? Colors.black
                                                          : Colors.grey,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 2),
                                                  Text(
                                                    timeline?[index].tahap ==
                                                            'Kandidat Tidak Diterima'
                                                        ? timelineData[4]
                                                            ['deskripsi']
                                                        : timelineData[index]
                                                                ['deskripsi'] ??
                                                            "",
                                                    style: TextStyle(
                                                      fontSize: 13,
                                                      color: timeline?[index]
                                                                  .tglTahap !=
                                                              ""
                                                          ? Colors.black
                                                          : Colors.grey,
                                                      fontWeight: timeline?[
                                                                      index]
                                                                  .tglTahap !=
                                                              ""
                                                          ? FontWeight.w500
                                                          : FontWeight.normal,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
