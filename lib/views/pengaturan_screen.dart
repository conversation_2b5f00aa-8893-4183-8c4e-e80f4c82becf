import 'dart:io';
import 'package:digital_cv_mobile/components/dialog_choose_bahasa.dart';
import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class PengaturanScreen extends StatefulWidget {
  const PengaturanScreen({super.key});

  @override
  State<PengaturanScreen> createState() => _PengaturanScreenState();
}

class _PengaturanScreenState extends State<PengaturanScreen> {
  final LoginController _loginController = Get.find<LoginController>();
  final prefs = Get.find<SharedPreferences>();
  String? appVersion;
  bool isBahasa = false;

  void getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appVersion = packageInfo.version;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getAppVersion();
  }

  //atur permission internet, query all package, dan intent filter di manifest
  void launchWeb() async {
    Uri googleUrl =
        Uri.parse("https://api.whatsapp.com/send/?phone=628117797779&text");

    if (await canLaunchUrl(googleUrl)) {
      await launchUrl(
        googleUrl,
        mode: LaunchMode.externalApplication,
      );
    } else {
      showAnimatedSnackbarError(context, "error.location_empty".tr);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "pengaturan.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            height: MediaQuery.of(context).size.height * 0.9,
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Material(
                  color: Colors.transparent, // Hindari warna default Material
                  child: InkWell(
                    onTap: () {
                      Get.toNamed(Routes.akun);
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Icon(
                            Icons.person_outline_rounded,
                            color: ColorAsset.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 6,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "pengaturan.akun".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: Icon(
                                Icons.keyboard_arrow_right,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                ),
                Material(
                  color: Colors.transparent, // Hindari warna default Material
                  child: InkWell(
                    onTap: () async {
                      // Show loading indicator
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        },
                      );

                      try {
                        final Uri storeUri;
                        if (Platform.isAndroid) {
                          // Try Google Play Store URL first
                          storeUri = Uri.parse(
                            'https://play.google.com/store/apps/details?id=com.gestaltsystech.digitalCvCandidate',
                          );
                        } else if (Platform.isIOS) {
                          // Use itms-apps:// URL scheme for iOS
                          storeUri = Uri.parse(
                            'itms-apps://itunes.apple.com/app/id6748906080',
                          );
                        } else {
                          Navigator.pop(context); // Remove loading
                          return;
                        }

                        // Add small delay to prevent UI freeze
                        await Future.delayed(Duration(milliseconds: 300));

                        final canLaunch = await canLaunchUrl(storeUri);
                        if (canLaunch) {
                          await launchUrl(
                            storeUri,
                            mode: LaunchMode.externalApplication,
                          );
                          Navigator.pop(context); // Remove loading
                        } else {
                          Navigator.pop(context); // Remove loading

                          // Fallback to market:// URL for Android
                          if (Platform.isAndroid) {
                            final fallbackUri = Uri.parse(
                              'market://details?id=com.gestaltsystech.digitalCvCandidate',
                            );
                            if (await canLaunchUrl(fallbackUri)) {
                              await launchUrl(
                                fallbackUri,
                                mode: LaunchMode.externalApplication,
                              );
                              return;
                            }
                          }

                          throw Exception('Could not launch store');
                        }
                      } catch (e) {
                        Navigator.pop(context); // Remove loading
                        showAnimatedSnackbarError(
                          context,
                          "Maaf, tidak dapat membuka toko aplikasi. Silakan coba lagi nanti.",
                        );
                      }
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Icon(
                            Icons.star_border_rounded,
                            color: ColorAsset.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 6,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "pengaturan.rating_masukan".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                ),
                Material(
                  color: Colors.transparent, // Hindari warna default Material
                  child: InkWell(
                    onTap: () {
                      launchWeb();
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Icon(
                            Icons.help_outline_rounded,
                            color: ColorAsset.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 5,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "pengaturan.bantuan".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                ),
                Material(
                  color: Colors.transparent, // Hindari warna default Material
                  child: InkWell(
                    onTap: () {
                      Get.toNamed(Routes.tnc);
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Icon(
                            Icons.privacy_tip_outlined,
                            color: ColorAsset.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 5,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "pengaturan.ketentuan_privasi".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                ),
                Material(
                  color: Colors.transparent, // Hindari warna default Material
                  child: InkWell(
                    onTap: () {
                      showDialogBahasa(context);
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Icon(
                            Icons.language_rounded,
                            color: ColorAsset.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 5,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "pengaturan.bahasa".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                ),
                if (prefs.getBool("auth") == true)
                  SizedBox(
                    height: 30,
                  ),
                if (prefs.getBool("auth") == true)
                  GestureDetector(
                    onTap: () async {
                      final prefs = Get.find<SharedPreferences>();
                      final isLoggedIn = prefs.getBool("auth") ?? false;
                      final isGoogle = prefs.getBool("isGoogle") ?? false;
                      if (isLoggedIn || isGoogle) {
                        var confirm = await showConfirmationDialog(
                            context,
                            "dialog_out.judul".tr,
                            "dialog_out.subjudul".tr,
                            "konfirmasi.iya2".tr,
                            "konfirmasi.tidak".tr);
                        if (confirm == true) {
                          _loginController.logout();
                        }
                      } else {
                        Get.offAllNamed('/login');
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.power_settings_new_sharp,
                              color: Colors.red,
                            ),
                            SizedBox(width: 10),
                            Text(
                              "tombol.keluar".tr,
                              style: TextStyle(fontSize: 12, color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 54),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "${"pengaturan.versi".tr} $appVersion",
                      style: TextStyle(fontSize: 10),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
