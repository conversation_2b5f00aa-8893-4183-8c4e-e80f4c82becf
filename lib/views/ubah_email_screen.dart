import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UbahEmailScreen extends StatefulWidget {
  const UbahEmailScreen({super.key});

  @override
  State<UbahEmailScreen> createState() => _UbahEmailScreenState();
}

class _UbahEmailScreenState extends State<UbahEmailScreen> {
  final ProfileController _profileController = Get.find<ProfileController>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final key = GlobalKey<FormState>();
  String? emailError;
  String? otpError;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: Colors.white,
          title: Text(
            "ubah_email.judul".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          iconTheme: IconThemeData(
            color: Colors.black,
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Container(
              height: MediaQuery.of(context).size.height * 0.7,
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Form(
                      key: key,
                      child: Column(
                        children: [
                          FormTextFiled(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            hintText: "daftar.txt_email_baru".tr,
                            errorText: emailError,
                          ),
                          const SizedBox(height: 10),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Obx(
                                () => Expanded(
                                  flex: 1, // Memberikan ruang lebih ke tombol
                                  child: SizedBox(
                                    height: 50,
                                    child: ElevatedButton(
                                      onPressed: () {
                                        if (_profileController
                                            .isLoadingOTP.value) {
                                          return;
                                        }
                                        if (_emailController.text.isEmpty ||
                                            _emailController.text == "") {
                                          showAnimatedSnackbarError(context,
                                              "daftar.txt_error_email".tr);
                                        } else {
                                          _profileController
                                              .reqOtp(_emailController.text);
                                        }
                                      },
                                      style: ElevatedButton.styleFrom(
                                        elevation: 0,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 6,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.horizontal(
                                            left: Radius.circular(6),
                                          ),
                                          side: BorderSide(
                                            color: Color(0xFFFF9800),
                                          ),
                                        ),
                                        textStyle:
                                            const TextStyle(fontSize: 14),
                                        backgroundColor: Color(0xFFFF9800),
                                      ),
                                      child:
                                          _profileController.isLoadingOTP.value
                                              ? Center(
                                                  child: SizedBox(
                                                    height: 30,
                                                    width: 30,
                                                    child:
                                                        CircularProgressIndicator(
                                                      color: Colors.white,
                                                      strokeWidth: 2.0,
                                                    ),
                                                  ),
                                                )
                                              : const Text(
                                                  "OTP",
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                    ),
                                  ),
                                ),
                              ), //  ), jarak antara tombol dan input
                              Expanded(
                                flex:
                                    3, // Memberikan lebih banyak ruang ke TextFormField
                                child: FormTextFiled(
                                  controller: _otpController,
                                  keyboardType: TextInputType.text,
                                  hintText: "daftar.txt_OTP_baru".tr,
                                  type: "otp",
                                ),
                              ),
                            ],
                          ),
                          if (otpError != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.error,
                                      color: Colors.red,
                                      size: 16,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      otpError!,
                                      style: TextStyle(
                                          color: Colors.red, fontSize: 12),
                                    ),
                                  ],
                                ),
                              ),
                            )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: Padding(
          padding: EdgeInsets.only(
            left: 16.0,
            right: 16.0,
            bottom: MediaQuery.of(context).viewInsets.bottom > 0
                ? MediaQuery.of(context).viewInsets.bottom + 8.0
                : 16.0,
            top: 8.0,
          ),
          child: Obx(
            () => _profileController.isLoading.value
                ? const SizedBox(
                    width: 30,
                    height: 50,
                    child: Center(child: CircularProgressIndicator()),
                  )
                : ElevatedButton(
                    onPressed: () async {
                      final emailRegExp =
                          RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                      if (_emailController.text.isEmpty &&
                          _otpController.text.isEmpty) {
                        setState(() {
                          emailError = "daftar.txt_error_email".tr;
                          otpError = "daftar.txt_error_OTP".tr;
                        });
                      } else if (!emailRegExp.hasMatch(_emailController.text)) {
                        setState(() {
                          emailError = 'daftar.format_email_invalid'.tr;
                        });
                      } else {
                        setState(() {
                          emailError = null;
                          otpError = null;
                        });
                        String email = _emailController.text;
                        String otp = _otpController.text;

                        await _profileController.ubahEmail(email, otp);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      textStyle: const TextStyle(fontSize: 14),
                    ),
                    child: Text(
                      "tombol.simpan".tr,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
          ),
        ));
  }
}
