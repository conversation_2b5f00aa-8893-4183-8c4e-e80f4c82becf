import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TnCScreen extends StatefulWidget {
  const TnCScreen({super.key});

  @override
  State<TnCScreen> createState() => _TnCScreenState();
}

class _TnCScreenState extends State<TnCScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showButton = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    bool isScrolled = _scrollController.offset > 100;
    if (_showButton != isScrolled) {
      setState(() {
        _showButton = isScrolled;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      floatingActionButton: AnimatedOpacity(
        duration: Duration(milliseconds: 300),
        opacity: _showButton ? 1.0 : 0.0,
        child: _showButton
            ? FloatingActionButton(
                onPressed: _scrollToTop,
                child: Icon(Icons.arrow_upward),
              )
            : SizedBox(), // Menghindari FloatingActionButton tetap mengambil ruang
      ),
      body: SafeArea(
          child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverAppBar(
            surfaceTintColor: Colors.transparent,
            forceElevated: true,
            expandedHeight: 125, // Batasi tinggi maksimal
            floating: true,
            pinned: false,
            snap: true,
            backgroundColor: Colors.white, // Warna tetap putih
            elevation: 0,
            shadowColor: Colors.black.withOpacity(0.0),
            iconTheme: IconThemeData(color: Colors.black),
            title:
                const Text(''), // Tambahkan agar transparansi tidak bermasalah
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  physics:
                      const NeverScrollableScrollPhysics(), // Supaya tidak ikut scroll utama
                  child: Padding(
                    padding:
                        const EdgeInsets.only(top: 10, left: 20, right: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Row (IconButton + ElevatedButton)
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 30,
                                  ),
                                  Text(
                                    "tnc.judul".tr,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: ColorAsset.yellowLogo,
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "tnc.txt_berlaku".tr,
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                      Text(
                                        ": ",
                                        style: TextStyle(fontSize: 12),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "tnc.txt_tgl".tr,
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "tnc.txt_unggah".tr,
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                      Text(
                                        ": ",
                                        style: TextStyle(fontSize: 12),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "tnc.txt_tgl".tr,
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "tnc.txt_no_dok".tr,
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                      Text(
                                        ": ",
                                        style: TextStyle(fontSize: 12),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          "I/1201/digitalcv/2021",
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Image.asset(
                              ImageAssets.logo,
                              width: 100,
                              height: 100,
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                children: [
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_pengenalan".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncPengenalan.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_info_umum".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listInfoUmum.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listInfoUmum[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_ket_layanan".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listKetLayanan.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Text.rich(
                                          textScaler: TextScaler.linear(
                                              MediaQuery.of(context)
                                                  .textScaler
                                                  .scale(1)),
                                          strutStyle: StrutStyle.fromTextStyle(
                                              TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black)),
                                          textAlign: TextAlign.justify,
                                          TextSpan(
                                            children: [
                                              WidgetSpan(
                                                  child: SizedBox(width: 30.0)),
                                              TextSpan(
                                                text: StrAssets
                                                    .listKetLayanan[index].tr,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_data_pribadi".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listInfoPribadi.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listInfoPribadi[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_kelola_data".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listKelolaData.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listKelolaData[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_persetujuan".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listPersetujuan.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listPersetujuan[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_info_teknik".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listInfoPihakTiga.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listInfoPihakTiga[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_lama_data".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncLamaData.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_keamanan_data".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: ListView.builder(
                              itemCount: StrAssets.listKeamananData.length,
                              shrinkWrap: true, // Agar menyesuaikan konten
                              physics:
                                  NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${index + 1}. ",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StrAssets.listKeamananData[index].tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.justify,
                                          softWrap: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_web_lain".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncLayananWebLain.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_perubahan".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncPerubahan.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_force_majeur".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment.centerLeft,
                          child: SizedBox(
                            width: double.infinity, // Agar tidak overflow
                            child: Column(
                              children: [
                                Text.rich(
                                  textScaler: TextScaler.linear(
                                      MediaQuery.of(context)
                                          .textScaler
                                          .scale(1)),
                                  strutStyle: StrutStyle.fromTextStyle(
                                      TextStyle(
                                          fontSize: 12, color: Colors.black)),
                                  textAlign: TextAlign.justify,
                                  TextSpan(
                                    children: [
                                      WidgetSpan(child: SizedBox(width: 30.0)),
                                      TextSpan(
                                        text: StrAssets.tncForceMajeur1.tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                ListView.builder(
                                  itemCount: StrAssets.listForceMajeur.length,
                                  shrinkWrap: true, // Agar menyesuaikan konten
                                  physics:
                                      NeverScrollableScrollPhysics(), // Menghindari konflik scrolling
                                  itemBuilder: (context, index) {
                                    return Padding(
                                      padding:
                                          const EdgeInsets.only(bottom: 8.0),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "${index + 1}. ",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.black,
                                            ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              StrAssets
                                                  .listForceMajeur[index].tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black,
                                              ),
                                              textAlign: TextAlign.justify,
                                              softWrap: true,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Text.rich(
                                  textScaler: TextScaler.linear(
                                      MediaQuery.of(context)
                                          .textScaler
                                          .scale(1)),
                                  strutStyle: StrutStyle.fromTextStyle(
                                      TextStyle(
                                          fontSize: 12, color: Colors.black)),
                                  textAlign: TextAlign.justify,
                                  TextSpan(
                                    children: [
                                      WidgetSpan(child: SizedBox(width: 30.0)),
                                      TextSpan(
                                        text: StrAssets.tncForceMajeur6.tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_hukum".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncHukuman.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  Theme(
                    data: Theme.of(context).copyWith(
                      dividerColor: Colors
                          .transparent, // Hilangkan garis bawaan ExpansionTile
                      splashColor: Colors.transparent, // Hilangkan efek klik
                    ),
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero, // Hilangkan padding default
                      childrenPadding:
                          EdgeInsets.zero, // Hilangkan padding dalam
                      initiallyExpanded: true,
                      title: Text(
                        "tnc.txt_kontak".tr,
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          padding: const EdgeInsets.only(bottom: 10),
                          alignment: Alignment
                              .centerLeft, // Pastikan wrap mulai dari kiri
                          child: Wrap(
                            alignment: WrapAlignment
                                .start, // Memastikan wrap mulai dari kiri
                            crossAxisAlignment: WrapCrossAlignment
                                .start, // Menjaga elemen tetap di atas
                            spacing: 8, // Jarak horizontal antar button
                            runSpacing: 8, // Jarak antar baris button
                            children: [
                              Text.rich(
                                textScaler: TextScaler.linear(
                                    MediaQuery.of(context).textScaler.scale(1)),
                                strutStyle: StrutStyle.fromTextStyle(TextStyle(
                                    fontSize: 12, color: Colors.black)),
                                textAlign: TextAlign.justify,
                                TextSpan(
                                  children: [
                                    WidgetSpan(child: SizedBox(width: 30.0)),
                                    TextSpan(
                                      text: StrAssets.tncKontak.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    thickness: 1,
                    height: 1,
                  ),
                  SizedBox(
                    height: 100,
                  ),
                ],
              ),
            ),
          )
        ],
      )),
    );
  }
}
