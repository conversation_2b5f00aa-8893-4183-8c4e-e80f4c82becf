import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UbahNoTelpScreen extends StatefulWidget {
  const UbahNoTelpScreen({super.key});

  @override
  State<UbahNoTelpScreen> createState() => _UbahNoTelpScreenState();
}

class _UbahNoTelpScreenState extends State<UbahNoTelpScreen> {
  final ProfileController _profileController = Get.find<ProfileController>();
  final TextEditingController _noTelpController = TextEditingController();
  final key = GlobalKey<FormState>();
  String errorText = "";
  String countryCode = "+62";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "ubah_no_telp.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            height: MediaQuery.of(context).size.height * 0.7,
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Form(
                  key: key,
                  child: Column(
                    children: [
                      FormTextFiled(
                        controller: _noTelpController,
                        keyboardType: TextInputType.phone,
                        onCountryCodeChanged: (value) {
                          setState(() {
                            countryCode = value;
                          });
                        },
                        hintText: "ubah_no_telp.hint1".tr,
                        type: "phone",
                        selectedCountryCode: countryCode,
                        errorText: errorText.isEmpty ? null : errorText,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          bottom: MediaQuery.of(context).viewInsets.bottom > 0
              ? MediaQuery.of(context).viewInsets.bottom + 8.0
              : 16.0,
          top: 8.0,
        ),
        child: Obx(
          () => _profileController.isLoading.value
              ? const SizedBox(
                  width: 30,
                  height: 50,
                  child: Center(child: CircularProgressIndicator()),
                )
              : SizedBox(
                  width: Get.width,
                  height: 45,
                  child: ElevatedButton(
                    onPressed: () async {
                      if (_noTelpController.text.isEmpty) {
                        setState(() {
                          errorText = "daftar.txt_error_no_handphone".tr;
                        });
                      } else {
                        String input = _noTelpController.text;
                        if (input.startsWith('0')) {
                          input = input.substring(1);
                        }
                        var noTelp = (countryCode + input).replaceAll('+', "");

                        await _profileController.ubahNoTelp(
                          noTelp,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      textStyle: const TextStyle(fontSize: 14),
                    ),
                    child: Text(
                      "tombol.simpan".tr,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}
