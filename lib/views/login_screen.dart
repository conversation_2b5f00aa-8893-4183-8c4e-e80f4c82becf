import 'dart:io';

// import 'package:digital_cv_mobile/bindings/home_binding.dart';
// import 'package:digital_cv_mobile/bindings/profile_binding.dart';
import 'package:digital_cv_mobile/controllers/bottom_nav_controller.dart';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/main_screen.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final LoginController authController = Get.find<LoginController>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final key = GlobalKey<FormState>();
  bool _isObscure = true; // State untuk menyembunyikan/memperlihatkan password
  bool _isAgree = false; // State untuk checkbox setuju syarat dan ketentuan
  ValueNotifier<UserCredential?> userCredential = ValueNotifier(null);
  final FirebaseAuth auth = FirebaseAuth.instance;
  final List<Map<String, dynamic>> erorrs = [];
  final GlobalKey one = GlobalKey();
  final GlobalKey two = GlobalKey();
  final GlobalKey three = GlobalKey();

  @override
  void initState() {
    super.initState();
    authController.getSavedCredentials().then((credentials) {
      if (credentials != null) {
        _emailController.text = credentials['email'] ?? '';
        _passwordController.text = credentials['password'] ?? '';
        setState(() {
          if (_isAgree) {
            // Simpan email dan password ke local storage
            authController.saveCredentials(
              _emailController.text,
              _passwordController.text,
            );
          } else {
            // Hapus email dan password dari local storage jika tidak setuju
            authController.clearCredentials();
          }
        });
      }
    });
    checkShowcase();
  }

  void checkShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    final hasShownTutorial = prefs.getBool('hasShownTutorial') ?? false;

    if (!hasShownTutorial) {
      // Delay untuk memastikan widget sudah dirender
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShowCaseWidget.of(context).startShowCase([one, two]);
      });

      // Tandai bahwa tutorial sudah ditampilkan
      await prefs.setBool('hasShownTutorial', true);
    }
  }

  Future<void> _loginGoogle() async {
    try {
      LogService.log.i("🔄 Memulai login dengan Google...");
      GoogleSignInAccount? googleUser = await authController.signInWithGoogle();

      if (googleUser != null) {
        // Gunakan Get.offNamed agar login screen tidak bisa kembali
        authController.loginGoogle(
          googleUser.email,
          googleUser.displayName ?? '',
        );
      } else {
        LogService.log.i("⚠️ Login gagal atau dibatalkan.");
      }
    } catch (e) {
      LogService.log.e("❌ Terjadi error saat login: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          child: Center(
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Align(
                    alignment: Alignment.topCenter,
                    child: Image.asset(
                      ImageAssets.logo,
                      width: MediaQuery.of(context).size.width * 0.4,
                      color: ColorAsset.primaryColor,
                    ),
                  ),
                  SizedBox(height: 15),
                  Text(
                    "login.txt_title".tr,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 15),
                  Form(
                    key: key,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Email Field
                        Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: Icon(Icons.email,
                                    color: Colors.grey.shade500),
                              ),
                              Expanded(
                                child: TextFormField(
                                  controller: _emailController,
                                  style: const TextStyle(
                                      fontSize: 16, color: Colors.black87),
                                  keyboardType: TextInputType.emailAddress,
                                  decoration: InputDecoration(
                                    hintText: "login.txt_email".tr,
                                    hintStyle: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey.shade500),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 15),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        ...erorrs.map((error) {
                          if (error['email'] == null) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    error['email'] ?? "",
                                    style: const TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ));
                        }),
                        const SizedBox(height: 20),
                        // Password Field
                        Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: Icon(Icons.lock,
                                    color: Colors.grey.shade500),
                              ),
                              Expanded(
                                child: TextFormField(
                                  controller: _passwordController,
                                  style: const TextStyle(
                                      fontSize: 16, color: Colors.black87),
                                  obscureText: _isObscure,
                                  keyboardType: TextInputType.visiblePassword,
                                  decoration: InputDecoration(
                                    hintText: "login.txt_pass".tr,
                                    hintStyle: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey.shade500),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 15),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isObscure
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: Colors.grey.shade600,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isObscure = !_isObscure;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        ...erorrs.map((error) {
                          if (error['password'] == null) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    error['password'] ?? "",
                                    style: const TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ));
                        }),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Checkbox(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          side:
                              const BorderSide(color: Colors.blue, width: 1.5),
                          value: _isAgree,
                          onChanged: (bool? value) {
                            setState(() {
                              _isAgree = value!;
                              if (_isAgree) {
                                // Simpan email dan password ke local storage
                                authController.saveCredentials(
                                  _emailController.text,
                                  _passwordController.text,
                                );
                              } else {
                                // Hapus email dan password dari local storage jika tidak setuju
                                authController.clearCredentials();
                              }
                            });
                          },
                        ),
                        Text(
                          "login.txt_remember_me".tr,
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 15),
                  // Login attempts and lockout status
                  Obx(() {
                    if (authController.isLocked.value) {
                      final minutes =
                          (authController.lockoutTimeRemaining.value / 60)
                              .floor();
                      final seconds =
                          authController.lockoutTimeRemaining.value % 60;
                      return Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 15),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.lock_clock, color: Colors.red, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                "${"controller.login_gagal".tr} ${minutes > 0 ? '${minutes}m ' : ''}${seconds}s",
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                  Obx(
                    () => authController.isLoading.value
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(width: 10),
                              Text(
                                authController.recaptchaService.isLoading.value
                                    ? "Memverifikasi keamanan..."
                                    : "Memeriksa Login...".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          )
                        : SizedBox(
                            height: 50,
                            child: ElevatedButton(
                              onPressed: authController.isLocked.value
                                  ? null
                                  : () {
                                      if (_emailController.text.isEmpty &&
                                          _passwordController.text.isEmpty) {
                                        setState(() {
                                          erorrs.clear();
                                          erorrs.add({
                                            'email': "login.txt_error_email".tr,
                                            'password':
                                                "login.txt_error_password".tr,
                                          });
                                        });
                                      } else if (_emailController
                                          .text.isEmpty) {
                                        setState(() {
                                          erorrs.clear();
                                          erorrs.add({
                                            'email': "login.txt_error_email".tr,
                                            'password': null,
                                          });
                                        });
                                      } else if (_passwordController
                                          .text.isEmpty) {
                                        setState(() {
                                          erorrs.clear();
                                          erorrs.add({
                                            'email': null,
                                            'password':
                                                "login.txt_error_password".tr,
                                          });
                                        });
                                      } else {
                                        authController.login(
                                          _emailController.text,
                                          _passwordController.text,
                                        );
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  backgroundColor: authController.isLocked.value
                                      ? Colors.grey.shade400
                                      : null,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 100,
                                    vertical: 6,
                                  ),
                                  minimumSize: const Size.fromHeight(50),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                  textStyle: TextStyle(fontSize: 14)),
                              child: Text(
                                authController.isLocked.value
                                    ? "Akun Terkunci"
                                    : "login.txt_btn_login".tr,
                                style: TextStyle(
                                  color: authController.isLocked.value
                                      ? Colors.white
                                      : Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                  ),
                  const SizedBox(height: 15),
                  SizedBox(
                    // width: Get.width,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () {
                        LogService.log.i("Explore Jobs button pressed");
                        Get.offAll(MainScreen(), binding: BindingsBuilder(() {
                          Get.lazyPut(() => NavController());
                          Get.lazyPut(() => LoginController());
                          Get.lazyPut(() => LowonganController());
                          Get.lazyPut(() => LamaranController());
                          Get.lazyPut(() => LocationController(), fenix: true);
                          Get.lazyPut(() => ProfileController());
                        }));
                      },
                      style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 100,
                            vertical: 6,
                          ),
                          minimumSize: const Size.fromHeight(50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                          side: BorderSide(
                            color: ColorAsset.primaryColor,
                            width: 1,
                          ),
                          textStyle: TextStyle(fontSize: 14)),
                      child: Text("login.txt_explore_jobs".tr,
                          style: TextStyle(color: Colors.black, fontSize: 18)),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Align(
                    alignment: Alignment.center,
                    child: TextButton(
                      onPressed: () {
                        // Navigasi ke halaman lupa password
                        Get.toNamed(Routes.lupaPassword);
                      },
                      child: Text(
                        "login.txt_forgot_pass".tr,
                        style: TextStyle(
                          color: ColorAsset.secodaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Expanded(
                        child: Divider(
                          color: Colors.grey, // Warna divider
                          thickness: 1, // Ketebalan divider
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "login.txt_divider".tr,
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                      ),
                      Expanded(
                        child: Divider(
                          color: Colors.grey,
                          thickness: 1,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Obx(
                        () => SizedBox(
                          width: Get.width / 5.5,
                          height: 55,
                          child: authController.isLoading.value
                              ? const Center(
                                  child: CircularProgressIndicator(),
                                )
                              : ElevatedButton(
                                  onPressed: () {
                                    _loginGoogle();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5, vertical: 12),
                                    minimumSize: const Size.fromHeight(45),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6.0),
                                      side: BorderSide(
                                          color: const Color.fromARGB(
                                              255, 197, 197, 197),
                                          width: 1),
                                    ),
                                    textStyle: const TextStyle(fontSize: 14),
                                    backgroundColor: Colors.white,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: <Widget>[
                                      Image.asset(
                                        ImageAssets.google,
                                        height: 30,
                                      ),
                                    ],
                                  ),
                                ),
                        ),
                      ),
                      if (Platform.isIOS) SizedBox(width: 20),
                      // Apple Sign In Button
                      if (Platform.isIOS)
                        Obx(
                          () => SizedBox(
                            width: Get.width / 5.5,
                            height: 55,
                            child: authController.isLoading.value
                                ? const Center(
                                    child: CircularProgressIndicator(),
                                  )
                                : FutureBuilder<bool>(
                                    future: SignInWithApple.isAvailable(),
                                    builder: (context, snapshot) {
                                      if (snapshot.data == true) {
                                        return ElevatedButton(
                                          onPressed: () {
                                            authController.signInWithApple();
                                          },
                                          style: ElevatedButton.styleFrom(
                                            elevation: 0,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 5, vertical: 12),
                                            minimumSize:
                                                const Size.fromHeight(45),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6.0),
                                              side: BorderSide(
                                                  color: const Color.fromARGB(
                                                      255, 197, 197, 197),
                                                  width: 1),
                                            ),
                                            textStyle:
                                                const TextStyle(fontSize: 14),
                                            backgroundColor: Colors.white,
                                          ),
                                          child: Icon(
                                            Icons.apple,
                                            color: Colors.black,
                                            size: 30,
                                          ),
                                        );
                                      }
                                      return SizedBox.shrink();
                                    },
                                  ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "login.txt_belum_punya_akun".tr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // Navigasi ke halaman daftar
                          Get.toNamed(Routes.register);
                        },
                        child: Text(
                          "login.txt_btn_daftar".tr,
                          style: TextStyle(
                            fontSize: 14,
                            color: ColorAsset.secodaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  )
                  // Row(
                  //   crossAxisAlignment: CrossAxisAlignment.center,
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     Text(
                  //       "login.txt_belum_punya_akun".tr,
                  //       style: TextStyle(
                  //         fontSize: 14,
                  //         color: Colors.grey,
                  //       ),
                  //     ),
                  //     Showcase(
                  //       key: one,
                  //       targetShapeBorder: CircleBorder(),
                  //       targetBorderRadius:
                  //           BorderRadius.all(Radius.circular(100)),
                  //       targetPadding: EdgeInsets.all(0),
                  //       tooltipPosition: TooltipPosition.top,
                  //       overlayOpacity: 0.2,
                  //       blurValue: 0,
                  //       description:
                  //           "Sebelum Login Pastikan Anda sudah mendaftar terlebih dahulu",
                  //       child: TextButton(
                  //         onPressed: () {
                  //           // Navigasi ke halaman daftar
                  //           Get.toNamed(Routes.register);
                  //         },
                  //         child: Text(
                  //           "login.txt_btn_daftar".tr,
                  //           style: TextStyle(
                  //             fontSize: 14,
                  //             color: ColorAsset.secodaryColor,
                  //             fontWeight: FontWeight.bold,
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
