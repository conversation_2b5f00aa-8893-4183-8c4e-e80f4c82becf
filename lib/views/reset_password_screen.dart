import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordScreen extends StatefulWidget {
  final String token;
  const ResetPasswordScreen({super.key, required this.token});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final LoginController authController = Get.find<LoginController>();
  final TextEditingController _passBaruController = TextEditingController();
  final TextEditingController _passBaruConfirmController =
      TextEditingController();
  final key = GlobalKey<FormState>();
  bool _isObscure = true; // State untuk menyembunyikan/memperlihatkan password
  bool _isObscure2 = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "ubah_password.reset".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.9,
            width: double.infinity, // Pastikan kontainer memenuhi layar
            child: Column(
              mainAxisAlignment:
                  MainAxisAlignment.center, // Pusatkan ke tengah layar
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: Material(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    elevation: 3,
                    child: Column(
                      children: [
                        // 🔹 Bagian atas tetap di atas
                        Container(
                          width: double.infinity,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              "ubah_password.reset".tr,
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),

                        // 🔹 Spacer untuk dorong Form ke tengah
                        const Spacer(),

                        // 🔹 Form berada di tengah
                        Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Form(
                            key: key,
                            child: Column(
                              children: [
                                const SizedBox(height: 10),
                                _buildTextFieldPassBaru(
                                  "ubah_password.hint1".tr,
                                  "ubah_password.hint1_blank".tr,
                                  _passBaruController,
                                  TextInputType.visiblePassword,
                                ),
                                const SizedBox(height: 10),
                                _buildTextFieldKonPassBaru(
                                  "ubah_password.hint2".tr,
                                  "ubah_password.hint2_blank".tr,
                                  _passBaruConfirmController,
                                  TextInputType.visiblePassword,
                                ),
                              ],
                            ),
                          ),
                        ),
                        // 🔹 Spacer lagi untuk dorong ke tengah
                        const Spacer(),

                        // 🔹 Tombol tetap di bawah
                        Obx(
                          () => authController.isLoading.value
                              ? const CircularProgressIndicator()
                              : SizedBox(
                                  height: 40,
                                  width: 150,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      if (key.currentState!.validate()) {
                                        authController.resetPassword(
                                            _passBaruController.text,
                                            widget.token);
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                      ),
                                      textStyle: const TextStyle(fontSize: 14),
                                    ),
                                    child: Text(
                                      "tombol.simpan".tr,
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                        ),

                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextFieldPassBaru(
    String hintText,
    String validator,
    TextEditingController controller,
    TextInputType inputType,
  ) {
    return SizedBox(
        height: 40,
        child: TextFormField(
          controller: controller,
          style: const TextStyle(fontSize: 14),
          obscureText: _isObscure,
          keyboardType: inputType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(fontSize: 14),

            // Mengatur tinggi dengan contentPadding
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 20),

            // Normal Border
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Colors.grey),
            ),

            // Focused Border (saat diklik)
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide:
                  const BorderSide(color: ColorAsset.primaryColor, width: 2),
            ),

            // Error Border (saat ada error)
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),

            // Border saat error dan sedang focus
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),

            suffixIcon: IconButton(
              icon: Icon(
                _isObscure ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isObscure = !_isObscure; // Toggle antara true/false
                });
              },
            ),
            // Style teks error
            errorStyle:
                const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),

          // Validasi input
          validator: (value) {
            if (value == null || value.isEmpty) {
              return validator;
            }
            if (value.length <= 6) {
              return "daftar.length_password_invalid".tr;
            }
            return null;
          },
        ));
  }

  Widget _buildTextFieldKonPassBaru(
    String hintText,
    String validator,
    TextEditingController controller,
    TextInputType inputType,
  ) {
    return SizedBox(
        height: 40,
        child: TextFormField(
          controller: controller,
          style: const TextStyle(fontSize: 14),
          obscureText: _isObscure2,
          keyboardType: inputType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(fontSize: 14),

            // Mengatur tinggi dengan contentPadding
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 20),

            // Normal Border
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Colors.grey),
            ),

            // Focused Border (saat diklik)
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide:
                  const BorderSide(color: ColorAsset.primaryColor, width: 2),
            ),

            // Error Border (saat ada error)
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),

            // Border saat error dan sedang focus
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),

            suffixIcon: IconButton(
              icon: Icon(
                _isObscure2 ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isObscure2 = !_isObscure2; // Toggle antara true/false
                });
              },
            ),
            // Style teks error
            errorStyle:
                const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),

          // Validasi input
          validator: (value) {
            if (value == null || value.isEmpty) {
              return validator;
            }
            if (value.length <= 6) {
              return "daftar.length_password_invalid".tr;
            }
            if (value != _passBaruController.text) {
              return "ubah_password.validator_equal".tr;
            }
            return null;
          },
        ));
  }
}
