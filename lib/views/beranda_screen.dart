import 'dart:convert';

import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/components/filter_beranda_dialog.dart';
import 'package:digital_cv_mobile/components/lokasi_dialog.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/components/urutan_dialog.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/controllers/notifikasi_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/filter_beranda_model.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart' as urlLauncher;

class BerandaScreen extends StatefulWidget {
  const BerandaScreen({super.key});

  @override
  State<BerandaScreen> createState() => _BerandaScreenState();
}

class _BerandaScreenState extends State<BerandaScreen> {
  final LowonganController lowonganController = Get.find<LowonganController>();
  final NotifikasiController notifikasiController =
      Get.put(NotifikasiController());
  final snackbar = Get.find<SnackBarService>();
  final TextEditingController lokasiController = TextEditingController();
  final TextEditingController urutanController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  static const _pageSize = 4;
  final PagingController<int, LowonganModel> _pagingController =
      PagingController(firstPageKey: 1); // BUKAN 0
  FilterBerandaModel filterBerandaModel = FilterBerandaModel();
  int jmlFilter = 0;

  final ProfileController profileController = Get.find<ProfileController>();
  final prefs = Get.find<FlutterSecureStorage>();
  final sharedPrefs = Get.find<SharedPreferences>();
  String? pin;
  String? nama;
  String? email;
  String? image;
  String? noTelp;

  void loadPref() async {
    pin = await prefs.read(key: "pin") ?? '';
    nama = await prefs.read(key: "nama") ?? '';
    image = await prefs.read(key: "image") ?? '';

    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await loadFilter();
      await loadFilterLokasi();
      notifikasiController.countNewNotif();
      debugPrint('⏳ loadFilter selesai, pasang page listener dan refresh list');

      ever(lowonganController.refreshBeranda, (_) {
        _pagingController.refresh(); // refresh list di beranda
      });
      _pagingController.addPageRequestListener((pageKey) {
        _fetchPage(pageKey);
      });

      _pagingController.refresh();
      _pagingController.notifyPageRequestListeners(1); // ⬅️ PENTING
    });

    loadPref();
    notifikasiController.countNewNotif();
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // ✅ Cegah pemanggilan API jika sudah tahu totalPage dan pageKey > totalPage
      final int totalPage = lowonganController.totalPage.value;
      if (totalPage != 0 && pageKey > totalPage) {
        if (kDebugMode) {
          print('❌ Page $pageKey melebihi totalPage ($totalPage), SKIP fetch.');
        }
        _pagingController.appendLastPage([]);
        return;
      }

      if (kDebugMode) {
        print('Searching string: (${searchController.text})');
        print('Lokasi string: (${lokasiController.text})');
        print('Jenis Pekerjaan: (${filterBerandaModel.jenisPekerjaan})');
      }

      filterBerandaModel.spesialisasi = translateToIndonesian(
        filterBerandaModel.spesialisasi,
      );
      filterBerandaModel.jenisPekerjaan = translateToIndonesian(
        filterBerandaModel.jenisPekerjaan,
      );
      filterBerandaModel.pendidikan = translateToIndonesian(
        filterBerandaModel.pendidikan,
      );
      filterBerandaModel.waktuPosting =
          translateKey(filterBerandaModel.waktuPosting);

      final newItems = await lowonganController.getListJob(
        searchController.text,
        lokasiController.text,
        filterBerandaModel.jenisPekerjaan,
        filterBerandaModel.spesialisasi,
        filterBerandaModel.pendidikan,
        filterBerandaModel.waktuPosting != null
            ? filterBerandaModel.waktuPosting!.toString()
            : "",
        pageKey,
        _pageSize,
      );

      // Hitung ulang totalPage kalau belum terisi
      final isLastPage = pageKey >= lowonganController.totalPage.value ||
          newItems.length < _pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }

      if (kDebugMode) {
        print('✅ FETCH PAGE $pageKey');
        print('API response count: ${newItems.length}');
        print('Total list after append: ${_pagingController.itemList?.length}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('❌ Error saat fetch page $pageKey: $error');
      }
      _pagingController.error = error;
    }
  }

  void _bukaPilihLokasi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      LokasiDialog(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      lokasiController.text =
          result.nama; // Set text field dengan nama provinsi

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('filter_lokasi', result.nama);
      setState(() {});
      _pagingController.refresh();
    }
  }

  void _bukaUrutan() async {
    final result = await Get.bottomSheet(
      UrutanDialog(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      urutanController.text =
          result; // Set text field dengan urutan yang dipilih

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('filter_urutan', result);
      setState(() {});
      _pagingController.refresh();
    }
  }

  Future<void> loadFilter() async {
    final prefs = await SharedPreferences.getInstance();
    final filterString = prefs.getString('filter_beranda');

    if (filterString != null) {
      final jsonMap = jsonDecode(filterString);
      final loadedFilter = FilterBerandaModel.fromJson(jsonMap);

      setState(() {
        filterBerandaModel = loadedFilter;
        filterBerandaModel.waktuPosting ??= "waktu_posting.txt_kapan_saja";
        jmlFilter = filterBerandaModel.jenisPekerjaan.length +
            filterBerandaModel.spesialisasi.length +
            filterBerandaModel.pendidikan.length;
      });
    } else {
      filterBerandaModel = FilterBerandaModel(); // kosong
    }
  }

  Future<void> loadFilterLokasi() async {
    final prefs = await SharedPreferences.getInstance();
    final filterLokasi = prefs.getString('filter_lokasi');

    if (filterLokasi != null) {
      setState(() {
        lokasiController.text = filterLokasi;
      });
    }
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    lowonganController.totalPage.value = 0; // Optional: reset totalPage
    _pagingController.refresh();
    notifikasiController.countNewNotif();
  }

  String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return "drawer.selamat_pagi".tr;
    } else if (hour < 17) {
      return "drawer.selamat_siang".tr;
    } else {
      return "drawer.selamat_malam".tr;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            SliverAppBar(
              surfaceTintColor: Colors.transparent,
              forceElevated: true,
              expandedHeight: 200, // Batasi tinggi maksimal
              floating: true,
              pinned: false,
              snap: true,
              backgroundColor: Colors.white, // Warna tetap putih
              elevation: 0,
              shadowColor: Colors.black.withOpacity(0.0),
              automaticallyImplyLeading: false, // Menghilangkan back button
              title: const Text(
                  ''), // Tambahkan agar transparansi tidak bermasalah
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        spreadRadius: 2,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    physics:
                        const NeverScrollableScrollPhysics(), // Supaya tidak ikut scroll utama
                    child: Padding(
                      padding:
                          const EdgeInsets.only(top: 10, left: 20, right: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle, boxShadow: []),
                                child: CircleAvatar(
                                  radius: 100, // Warna background jika error
                                  child: ClipOval(
                                    child: Obx(
                                      () => (isValidImageUrl(profileController
                                                  .rxImage.value) &&
                                              profileController
                                                  .rxImage.value.isNotEmpty)
                                          ? Image.network(
                                              profileController.rxImage.value,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              ImageAssets.logo,
                                              fit: BoxFit.cover,
                                              width: 60,
                                              height: 60,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                flex: 8,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      getGreeting(),
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.normal),
                                    ),
                                    sharedPrefs.getBool("auth") == false ||
                                            sharedPrefs.getBool("isGoogle") ==
                                                false
                                        ? Text(
                                            nama ?? "error.name_empty".tr,
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold),
                                          )
                                        : SizedBox.shrink()
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  Stack(
                                    children: [
                                      Container(
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.all(5),
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.grey,
                                            width: 1,
                                          ),
                                        ),
                                        // padding:
                                        //     const EdgeInsets.only(left: 12),
                                        child: InkWell(
                                          borderRadius:
                                              BorderRadius.circular(100),
                                          onTap: () async {
                                            final prefs =
                                                Get.find<SharedPreferences>();
                                            final isLoggedIn =
                                                prefs.getBool("auth") ?? false;
                                            final isGoogle =
                                                prefs.getBool("isGoogle") ??
                                                    false;
                                            if (isLoggedIn || isGoogle) {
                                              Get.toNamed(Routes.notifikasi);
                                            } else {
                                              var confirm =
                                                  await showConfirmationDialog(
                                                      context,
                                                      "dialog_out.explore_judul"
                                                          .tr,
                                                      "dialog_out.explore_notifikasi"
                                                          .tr,
                                                      "konfirmasi.iya2".tr,
                                                      "konfirmasi.tidak".tr);
                                              if (confirm == true) {
                                                Get.offAllNamed('/login');
                                              }
                                            }
                                          },
                                          child: const Icon(
                                            Icons.notifications_none,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                      Obx(() => notifikasiController
                                                  .jmlNewNotif.value >
                                              0
                                          ? Positioned(
                                              right: 7,
                                              top: 5,
                                              child: Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.circle,
                                                    size: 12,
                                                    color: Colors.red,
                                                  ),
                                                  Align(
                                                    alignment: Alignment.center,
                                                    child: Text(
                                                      "${notifikasiController.jmlNewNotif.value}",
                                                      style: TextStyle(
                                                          fontSize: 10,
                                                          color: Colors.white),
                                                    ),
                                                  )
                                                ],
                                              ))
                                          : SizedBox())
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ),
                          const SizedBox(height: 11),
                          // TextField
                          cardSearch(context),

                          // Tambahkan jarak antara TextField & Row
                          const SizedBox(height: 15),

                          // Row (IconButton + ElevatedButton)

                          Row(
                            children: [
                              // Expanded(
                              //   child: ElevatedButton(
                              //     onPressed: () {
                              //       _bukaUrutan();
                              //     },
                              //     style: ElevatedButton.styleFrom(
                              //       elevation: 0,
                              //       backgroundColor: Colors.white,
                              //       shape: RoundedRectangleBorder(
                              //         borderRadius: BorderRadius.circular(8),
                              //         side:
                              //             const BorderSide(color: Colors.black),
                              //       ),
                              //     ),
                              //     child: Row(
                              //       mainAxisAlignment: MainAxisAlignment
                              //           .spaceBetween, // Supaya ukuran mengikuti kontennya
                              //       children: [
                              //         Row(
                              //           children: [
                              //             SizedBox(
                              //               width: 110,
                              //               child: Text(
                              //                 urutanController.text.isNotEmpty
                              //                     ? urutanController.text
                              //                     : "urutan.txt_relevan".tr,
                              //                 style: const TextStyle(
                              //                     color: Colors.black),
                              //                 overflow: TextOverflow.ellipsis,
                              //                 maxLines: 1,
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //         urutanController.text.isNotEmpty ||
                              //                 urutanController.text != ""
                              //             ? Material(
                              //                 child: InkWell(
                              //                   child: Icon(Icons.clear,
                              //                       color: Colors.red),
                              //                   borderRadius:
                              //                       BorderRadius.circular(100),
                              //                   onTap: () async {
                              //                     final prefs =
                              //                         await SharedPreferences
                              //                             .getInstance();
                              //                     await prefs
                              //                         .remove('filter_urutan');
                              //                     setState(() {
                              //                       urutanController.text = "";
                              //                     });
                              //                     _pagingController.refresh();
                              //                   },
                              //                 ),
                              //               )
                              //             : RotatedBox(
                              //                 quarterTurns: 3,
                              //                 child: const Icon(
                              //                     Icons
                              //                         .arrow_back_ios_new_rounded,
                              //                     color: Colors.black),
                              //               ), // Ikon di kanan
                              //         // Ikon di kanan
                              //       ],
                              //     ),
                              //   ),
                              // ),
                              // const SizedBox(width: 8), // Spasi antara tombol
                              // ElevatedButton tanpa jarak tambahan
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () {
                                    _bukaPilihLokasi();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    elevation: 0,
                                    backgroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side:
                                          const BorderSide(color: Colors.black),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment
                                        .spaceBetween, // Supaya ukuran mengikuti kontennya
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(Icons.location_on,
                                              color: Color.fromARGB(
                                                  255, 7, 54, 93)),
                                          const SizedBox(width: 8),
                                          SizedBox(
                                            width: 85,
                                            child: Text(
                                              lokasiController.text.isNotEmpty
                                                  ? lokasiController.text
                                                  : "tombol.lokasi".tr,
                                              style: const TextStyle(
                                                  color: Colors.black),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                      lokasiController.text.isNotEmpty ||
                                              lokasiController.text != ""
                                          ? Material(
                                              child: InkWell(
                                                child: Icon(Icons.clear,
                                                    color: Colors.red),
                                                borderRadius:
                                                    BorderRadius.circular(100),
                                                onTap: () async {
                                                  final prefs =
                                                      await SharedPreferences
                                                          .getInstance();
                                                  await prefs
                                                      .remove('filter_lokasi');
                                                  setState(() {
                                                    lokasiController.text = "";
                                                  });
                                                  _pagingController.refresh();
                                                },
                                              ),
                                            )
                                          : RotatedBox(
                                              quarterTurns: 3,
                                              child: const Icon(
                                                  Icons
                                                      .arrow_back_ios_new_rounded,
                                                  color: Colors.black),
                                            ), // Ikon di kanan
                                      // Ikon di kanan
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
          body: RefreshIndicator(
            onRefresh: _onRefresh,
            child: CustomScrollView(
              slivers: [
                const SliverToBoxAdapter(
                  child: SizedBox(
                    height: 16,
                  ),
                ),
                PagedSliverList<int, LowonganModel>(
                  pagingController: _pagingController,
                  builderDelegate: PagedChildBuilderDelegate<LowonganModel>(
                    itemBuilder: (context, item, index) => Padding(
                      padding:
                          const EdgeInsets.only(bottom: 8, left: 20, right: 20),
                      child: cardLowongan(item),
                    ),
                    noItemsFoundIndicatorBuilder: (context) => Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/empty_data_notif.png',
                              width: 200,
                              height: 200,
                            ),
                            SizedBox(height: 20),
                            Text(
                              "beranda.data_kosong".tr,
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 10),
                            Text(
                              "beranda.data_kosong_desc".tr,
                              style:
                                  TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget cardSearch(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.search,
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(vertical: 6),
                  hintText: "beranda.hint_searching".tr,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  suffixIcon: searchController.text.isNotEmpty ||
                          searchController.text != ""
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: () async {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.remove('filter_search');
                            searchController.clear();
                            _pagingController.refresh();
                          },
                        )
                      : null,
                ),
                onChanged: (value) {
                  setState(() {});
                },
                onSubmitted: (value) async {
                  final prefs = await SharedPreferences.getInstance();
                  if (value.isNotEmpty) {
                    await prefs.setString('filter_search', value);
                  } else {
                    await prefs.remove('filter_search');
                  }
                  _pagingController.refresh();
                },
              ),
            ),
          ),
          Stack(
            children: [
              GestureDetector(
                onTap: () async {
                  final result = await showModalBottomSheet<FilterBerandaModel>(
                    context: context,
                    isScrollControlled: true,
                    isDismissible: false,
                    enableDrag: true,
                    backgroundColor: Colors.transparent,
                    builder: (context) => const FilterBerandaDialog(),
                  );

                  if (result != null) {
                    if (filterBerandaModel != result) {
                      filterBerandaModel = result;
                      filterBerandaModel.spesialisasi = translateToIndonesian(
                        result.spesialisasi,
                      );
                      filterBerandaModel.jenisPekerjaan = translateToIndonesian(
                        result.jenisPekerjaan,
                      );
                      filterBerandaModel.waktuPosting =
                          translateKey(result.waktuPosting);
                      _pagingController.refresh();
                      setState(() {
                        jmlFilter = filterBerandaModel.jenisPekerjaan.length +
                            filterBerandaModel.spesialisasi.length +
                            filterBerandaModel.pendidikan.length;
                      });
                    }
                    // panggil API/filter ulang berdasarkan hasil
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Icon(
                    Icons.tune,
                    color: const Color.fromARGB(255, 7, 54, 93),
                  ),
                ),
              ),
              jmlFilter != 0
                  ? Positioned(
                      top: 10,
                      right: 10,
                      child: Container(
                        padding: EdgeInsets.zero,
                        height: 12,
                        width: 12,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          color: Colors.red,
                        ),
                        child: Center(
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              "$jmlFilter",
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    )
                  : SizedBox(),
            ],
          )
        ],
      ),
    );
  }

  Widget cardLowongan(dynamic item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () async {
          if (item.status == "ultra") {
            await urlLauncher.launch(item.link);
            return;
          }
          Get.toNamed(Routes.lowonganDetail, arguments: item);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo
                  ClipRRect(
                    borderRadius: (isValidImageUrl(item.logoURL) &&
                            item.logoURL.isNotEmpty)
                        ? BorderRadius.circular(0)
                        : BorderRadius.circular(10),
                    child: (isValidImageUrl(item.logoURL) &&
                            item.logoURL.isNotEmpty)
                        ? Image.network(
                            item.logoURL,
                            fit: BoxFit.contain,
                            width: 70,
                            height: 70,
                            errorBuilder: (context, error, stackTrace) {
                              return Image.asset(
                                ImageAssets.logo2,
                                fit: BoxFit.cover,
                                width: 60,
                                height: 60,
                              );
                            },
                          )
                        : Image.asset(
                            ImageAssets.logo2,
                            fit: BoxFit.cover,
                            width: 60,
                            height: 60,
                          ),
                  ),
                  const SizedBox(width: 10),
                  // Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Posisi
                        Text(
                          item.posisi,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Perusahaan
                        Text(
                          item.perusahaan,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[500]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Lamar
                        if (item.isLamar)
                          Row(
                            children: [
                              const Icon(
                                Icons.check_circle_outline,
                                color: Colors.green,
                                size: 14,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                "beranda.lamar".tr,
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  // Favorite
                  if (item.status != "ultra")
                    IconButton(
                      onPressed: () async {
                        final prefs = Get.find<SharedPreferences>();
                        final isLoggedIn = prefs.getBool("auth") ?? false;
                        final isGoogle = prefs.getBool("isGoogle") ?? false;
                        if (isLoggedIn || isGoogle) {
                          item.isFavorit = !item.isFavorit;
                          SchedulerBinding.instance.addPostFrameCallback((_) {
                            setState(() {});
                          });
                          final success = await lowonganController.saveLowongan(
                            item.tempKode,
                            item.isFavorit,
                          );
                          if (success) {
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            lowonganController.triggerRefreshTersimpan();
                          }
                        } else {
                          var confirm = await showConfirmationDialog(
                              context,
                              "dialog_out.explore_judul".tr,
                              "dialog_out.explore_simpan_lowongan".tr,
                              "konfirmasi.iya2".tr,
                              "konfirmasi.tidak".tr);
                          if (confirm == true) {
                            Get.offAllNamed('/login');
                          }
                        }
                      },
                      icon: Icon(
                        item.isFavorit
                            ? Icons.bookmark_added
                            : Icons.bookmark_added_outlined,
                        color: item.isFavorit ? ColorAsset.primaryColor : null,
                      ),
                    ),
                ],
              ),
              Divider(
                color: Colors.grey[300],
                height: 20,
              ),
              Row(
                children: [
                  // Spacer for logo
                  const SizedBox(width: 80),
                  // Lokasi dan tipe pekerjaan
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Lokasi kerja
                        Text(
                          item.lokasiKerja2,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[500],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              TranslationService.translateTimeAgo(
                                  item.waktuLalu),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: ColorAsset.secodaryColor,
                              ),
                            ),
                            Text(
                              " • ",
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                            Flexible(
                              child: Text(
                                TranslationService.translateDateString(
                                    item.tglPosting),
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: ColorAsset.secodaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        // Tipe pekerjaan
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            TranslationService.translateBetweenLangs(
                                item.tipePekerjaan,
                                "jenis_pekerjaan",
                                "id",
                                Get.locale?.languageCode.toLowerCase() ?? "id"),
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
