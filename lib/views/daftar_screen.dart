import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/daftar_controller.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DaftarScreen extends StatefulWidget {
  final String? email;
  final String? nama;
  const DaftarScreen({super.key, this.email, this.nama});

  @override
  State<DaftarScreen> createState() => _DaftarScreenState();
}

class _DaftarScreenState extends State<DaftarScreen> {
  final DaftarController daftarController =
      Get.put(DaftarController(), permanent: true, tag: "daftar");
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _namaController = TextEditingController();
  final TextEditingController _noHpController = TextEditingController();
  final TextEditingController _konfirmPassController = TextEditingController();
  final key = GlobalKey<FormState>();
  bool _isObscure = true;
  bool _isObscure2 = true; // State untuk menyembunyikan/memperlihatkan password
  bool _isAgree = true;
  String? emailError;
  String countryCode = '+62';

  // Fungsi validasi password yang lebih kuat
  String? validatePassword(String password) {
    if (password.isEmpty) {
      return "controller.validasi.password_kosong".tr;
    }

    if (password.length < 8) {
      return "controller.validasi.min_password".tr;
    }

    // Cek apakah mengandung huruf besar & huruf kecil
    if (!RegExp(r'[A-Z]').hasMatch(password) ||
        !RegExp(r'[a-z]').hasMatch(password)) {
      return "controller.validasi.has_uppercase".tr;
    }

    // Cek apakah mengandung angka
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return "controller.validasi.has_number".tr;
    }

    // Cek apakah mengandung karakter khusus
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return "${"controller.validasi.has_special_char".tr} (!@#\$%^&*(),.?\":{}|<>)";
    }

    return null; // Password valid
  }

  // Fungsi untuk mengecek kriteria password individual
  Map<String, bool> getPasswordCriteria(String password) {
    return {
      'minLength': password.length >= 8,
      'hasUppercase': RegExp(r'[A-Z]').hasMatch(password) &&
          RegExp(r'[a-z]').hasMatch(password),
      'hasNumber': RegExp(r'[0-9]').hasMatch(password),
      'hasSpecialChar': RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
    };
  }

  // Widget untuk menampilkan kriteria password
  Widget buildPasswordCriteria() {
    final criteria = getPasswordCriteria(_passwordController.text);

    return Container(
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "controller.kriteria_password".tr,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          SizedBox(height: 8),
          _buildCriteriaItem(
              "controller.min_length".tr, criteria['minLength']!),
          _buildCriteriaItem(
              "controller.has_uppercase".tr, criteria['hasUppercase']!),
          _buildCriteriaItem(
              "controller.has_number".tr, criteria['hasNumber']!),
          _buildCriteriaItem("${"controller.has_special_char".tr} (!@#\$%^&*)",
              criteria['hasSpecialChar']!),
        ],
      ),
    );
  }

  Widget _buildCriteriaItem(String text, bool isValid) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 16,
            color: isValid ? Colors.green : Colors.grey,
          ),
          SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: isValid ? Colors.green : Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  final List<Map<String, dynamic>> errorsMessage = [
    {
      "isError": false,
      "message": "daftar.txt_error_nama".tr,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_email".tr,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_no_handphone".tr,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_OTP".tr,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_password".tr,
    },
    {
      "isError": false,
      "message": "daftar.txt_error_confirm_pass".tr,
    },
  ];

  @override
  void initState() {
    _emailController.text = widget.email ?? ""; // Set email jika ada
    _namaController.text = widget.nama ?? ""; // Set nama jika ada
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        iconTheme: const IconThemeData(color: Colors.black),
        titleSpacing: 0, // 🔹 Agar title menempel dengan icon back
        title: Text(
          "daftar.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(
                right: 16), // 🔹 Beri jarak dari sisi kanan
            child: Image.asset(
              ImageAssets.logo,
              height: 50, // 🔹 Sesuaikan ukuran logo
              width: 50,
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 10),
                  Form(
                    key: key,
                    child: Column(
                      children: [
                        FormTextFiled(
                          controller: _namaController,
                          keyboardType: TextInputType.name,
                          hintText: "daftar.txt_nama".tr,
                          errorText: errorsMessage[0]['isError']
                              ? errorsMessage[0]['message']
                              : null,
                        ),
                        const SizedBox(height: 10),
                        FormTextFiled(
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          hintText: "daftar.txt_email".tr,
                          isReadOnly: widget.email != null && widget.email != ''
                              ? true
                              : false,
                          errorText: errorsMessage[1]['isError']
                              ? errorsMessage[1]['message']
                              : null,
                        ),
                        const SizedBox(height: 10),
                        FormTextFiled(
                          controller: _noHpController,
                          keyboardType: TextInputType.phone,
                          onCountryCodeChanged: (value) {
                            setState(() {
                              countryCode = value;
                            });
                          },
                          hintText: "ubah_no_telp.hint1".tr,
                          type: "phone",
                          selectedCountryCode: countryCode,
                          errorText: errorsMessage[2]['isError']
                              ? errorsMessage[2]['message']
                              : null,
                        ),
                        const SizedBox(height: 10),
                        // Obx(
                        //   () => Row(
                        //     crossAxisAlignment: CrossAxisAlignment.start,
                        //     children: [
                        //       Expanded(
                        //         flex: 1, // Memberikan ruang lebih ke tombol
                        //         child: SizedBox(
                        //           height: 50,
                        //           child: ElevatedButton(
                        //             onPressed: (daftarController
                        //                         .isLoadingOTP.value ||
                        //                     daftarController
                        //                             .otpCountdown.value >
                        //                         0)
                        //                 ? null // disable tombol saat loading
                        //                 : () {
                        //                     if (!Utilities().isValidEmail(
                        //                         _emailController.text)) {
                        //                       daftarController
                        //                           .otpCountdown.value = 0;
                        //                       return showAnimatedSnackbarError(
                        //                           context,
                        //                           "controller.login_failed".tr);
                        //                     }
                        //                     if (_emailController.text.isEmpty ||
                        //                         _noHpController.text.isEmpty) {
                        //                       daftarController
                        //                           .otpCountdown.value = 0;
                        //                       showAnimatedSnackbarError(
                        //                           context,
                        //                           "daftar.txt_error_email_hp"
                        //                               .tr);
                        //                     } else {
                        //                       daftarController.reqOtp(
                        //                           _emailController.text,
                        //                           _noHpController.text,
                        //                           'candidate');
                        //                     }
                        //                   },
                        //             style: ElevatedButton.styleFrom(
                        //               elevation: 0,
                        //               padding: const EdgeInsets.symmetric(
                        //                 horizontal: 20,
                        //                 vertical: 6,
                        //               ),
                        //               shape: RoundedRectangleBorder(
                        //                 borderRadius: BorderRadius.horizontal(
                        //                   left: Radius.circular(6),
                        //                 ),
                        //                 side: BorderSide(
                        //                   color: Color(0xFFFF9800),
                        //                 ),
                        //               ),
                        //               textStyle: const TextStyle(fontSize: 14),
                        //               backgroundColor: Color(0xFFFF9800),
                        //             ),
                        //             child: daftarController.otpCountdown.value >
                        //                     0
                        //                 ? Text(
                        //                     "${daftarController.otpCountdown.value}s",
                        //                     style: const TextStyle(
                        //                         color: Colors.white,
                        //                         fontSize: 16,
                        //                         fontWeight: FontWeight.bold),
                        //                   )
                        //                 : const Text(
                        //                     "OTP",
                        //                     style: TextStyle(
                        //                         color: Colors.white,
                        //                         fontSize: 18,
                        //                         fontWeight: FontWeight.bold),
                        //                   ),
                        //           ),
                        //         ),
                        //       ),
                        //       Expanded(
                        //         flex:
                        //             3, // Memberikan lebih banyak ruang ke TextFormField
                        //         child: FormTextFiled(
                        //           controller: _otpController,
                        //           keyboardType: TextInputType.text,
                        //           hintText: "daftar.txt_OTP".tr,
                        //           type: "otp",
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        // if (errorsMessage[3]['isError'] == true)
                        //   Padding(
                        //     padding: const EdgeInsets.only(top: 8.0),
                        //     child: Align(
                        //       alignment: Alignment.centerLeft,
                        //       child: Row(
                        //         children: [
                        //           Icon(
                        //             Icons.error,
                        //             color: Colors.red,
                        //             size: 16,
                        //           ),
                        //           SizedBox(width: 8),
                        //           Text(
                        //             errorsMessage[3]['message'],
                        //             style: TextStyle(
                        //                 color: Colors.red, fontSize: 12),
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ),
                        // const SizedBox(height: 10),
                        FormTextFiled(
                          controller: _passwordController,
                          keyboardType: TextInputType.visiblePassword,
                          hintText: "daftar.txt_password".tr,
                          obscureText: _isObscure,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _isObscure
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _isObscure =
                                    !_isObscure; // Toggle antara true/false
                              });
                            },
                          ),
                          errorText: errorsMessage[4]['isError']
                              ? errorsMessage[4]['message']
                              : null,
                          onChanged: (value) {
                            // Validasi real-time password
                            String? passwordError = validatePassword(value);
                            setState(() {
                              errorsMessage[4]['isError'] =
                                  passwordError != null;
                              if (passwordError != null) {
                                errorsMessage[4]['message'] = passwordError;
                              }
                            });
                          },
                        ),
                        // Tampilkan kriteria password jika user sedang mengetik
                        if (_passwordController.text.isNotEmpty)
                          buildPasswordCriteria(),
                        const SizedBox(height: 10),
                        FormTextFiled(
                          controller: _konfirmPassController,
                          keyboardType: TextInputType.visiblePassword,
                          hintText: "daftar.txt_confirm_pass".tr,
                          obscureText: _isObscure2,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _isObscure2
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _isObscure2 =
                                    !_isObscure2; // Toggle antara true/false
                              });
                            },
                          ),
                          errorText: errorsMessage[5]['isError']
                              ? errorsMessage[5]['message']
                              : null,
                          onChanged: (value) {
                            // Validasi konfirmasi password
                            setState(() {
                              if (value != _passwordController.text) {
                                errorsMessage[5]['isError'] = true;
                                errorsMessage[5]['message'] =
                                    "Password tidak sama";
                              } else {
                                errorsMessage[5]['isError'] = false;
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  Align(
                    alignment: Alignment.topRight,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Checkbox(
                          value: _isAgree,
                          onChanged: (bool? value) {
                            setState(() {
                              _isAgree = value!;
                            });
                          },
                        ),
                        Expanded(
                          child: Wrap(
                            alignment: WrapAlignment.start,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              Text(
                                "daftar.txt_konfirm_tnc1".tr,
                                style: TextStyle(fontSize: 12),
                              ),
                              SizedBox(width: 3),
                              GestureDetector(
                                onTap: () {
                                  Get.toNamed(Routes.tnc);
                                },
                                child: Text(
                                  "daftar.txt_konfirm_tnc2".tr,
                                  style: TextStyle(
                                      fontSize: 12, color: Colors.blue),
                                ),
                              ),
                              SizedBox(width: 3),
                              Text(
                                "daftar.txt_konfirm_tnc3".tr,
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                    child: Obx(
                      () => daftarController.isLoadingDaftar.value
                          ? const Center(
                              child:
                                  CircularProgressIndicator()) // 🔄 Loading saat daftar diproses
                          : SizedBox(
                              width: 250, // 🔹 Tetapkan lebar yang jelas
                              height: 50, // 🔹 Sesuaikan dengan button
                              child: ElevatedButton(
                                onPressed: () {
                                  for (var i = 0;
                                      i < errorsMessage.length;
                                      i++) {
                                    setState(() {
                                      errorsMessage[i]['isError'] = false;
                                    });
                                  }
                                  if (_namaController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[0]['isError'] = true;
                                    });
                                    showAnimatedSnackbarError(
                                        context, "daftar.txt_error_nama".tr);
                                    return;
                                  }
                                  if (_emailController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[1]['isError'] = true;
                                    });
                                    showAnimatedSnackbarError(
                                        context, "daftar.txt_error_email".tr);
                                    return;
                                  }
                                  if (_noHpController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[2]['isError'] = true;
                                    });
                                    showAnimatedSnackbarError(context,
                                        "daftar.txt_error_no_handphone".tr);
                                    return;
                                  }
                                  // Validasi password dengan kriteria yang lebih kuat
                                  String? passwordError = validatePassword(
                                      _passwordController.text);
                                  if (passwordError != null) {
                                    showAnimatedSnackbarError(
                                        context, passwordError);
                                    return;
                                  }

                                  // Validasi konfirmasi password
                                  if (_passwordController.text !=
                                      _konfirmPassController.text) {
                                    showAnimatedSnackbarError(context,
                                        "ubah_password.validator_equal".tr);
                                    return;
                                  }

                                  if (!_isAgree) {
                                    showAnimatedSnackbarError(context,
                                        "daftar.txt_error_konfirm_tnc".tr);
                                  } else {
                                    String input = _noHpController.text;
                                    if (input.startsWith('0')) {
                                      input = input.substring(1);
                                    }
                                    var noTelp = (countryCode + input)
                                        .replaceAll('+', "");
                                    daftarController.register(
                                      _namaController.text,
                                      _emailController.text,
                                      _passwordController.text,
                                      noTelp,
                                    );
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                  textStyle: const TextStyle(fontSize: 14),
                                ),
                                child: Text(
                                  "daftar.txt_btn_daftar".tr,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
