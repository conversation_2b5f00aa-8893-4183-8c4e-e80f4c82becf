import 'package:digital_cv_mobile/components/konfirmasi_login.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LowonganTersimpanScreen extends StatefulWidget {
  const LowonganTersimpanScreen({super.key});

  @override
  State<LowonganTersimpanScreen> createState() =>
      _LowonganTersimpanScreenState();
}

class _LowonganTersimpanScreenState extends State<LowonganTersimpanScreen> {
  final LowonganController lowonganController = Get.find<LowonganController>();
  static const _pageSize = 5;
  final PagingController<int, LowonganModel> _pagingController =
      PagingController(firstPageKey: 1);
  final sharedPrefs = Get.find<SharedPreferences>();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    ever(lowonganController.refreshTersimpan, (_) {
      _pagingController.refresh(); // refresh PagedSliverList
    });
    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // ✅ Cegah pemanggilan API jika sudah tahu totalPage dan pageKey > totalPage
      final int totalPage = lowonganController.totalPage.value;
      if (totalPage != 0 && pageKey > totalPage) {
        if (kDebugMode) {
          print('❌ Page $pageKey melebihi totalPage ($totalPage), SKIP fetch.');
        }
        _pagingController.appendLastPage([]);
        return;
      }

      final newItems = await lowonganController.getLowonganTersimpan(
        searchController.text,
        pageKey,
        _pageSize,
      );

      // Hitung ulang totalPage kalau belum terisi
      final isLastPage = pageKey >= lowonganController.totalPage.value ||
          newItems.length < _pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }

      if (kDebugMode) {
        print('✅ FETCH PAGE $pageKey');
        print('API response count: ${newItems.length}');
        print('Total list after append: ${_pagingController.itemList?.length}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('❌ Error saat fetch page $pageKey: $error');
      }
      _pagingController.error = error;
    }
  }

  Future<void> _onRefresh() async {
    lowonganController.totalPage.value = 0; // Optional: reset totalPage
    _pagingController.refresh();
  }

  void removeItemFromList(int index) {
    final list = _pagingController.itemList;
    if (list != null && index >= 0 && index < list.length) {
      final updatedList = List<LowonganModel>.from(list)..removeAt(index);
      _pagingController.itemList = updatedList;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
          child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            forceElevated: true,
            expandedHeight: 80, // Batasi tinggi maksimal
            toolbarHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            backgroundColor: Colors.white, // Warna tetap putih
            elevation: 0,
            shadowColor: Colors.black.withOpacity(0.0),
            automaticallyImplyLeading: false,
            title: const Text(''),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  physics:
                      const NeverScrollableScrollPhysics(), // Supaya tidak ikut scroll utama
                  child: Padding(
                    padding:
                        const EdgeInsets.only(top: 10, left: 20, right: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "tersimpan.judul".tr,
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 5),
                        cardSearch(context),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        body: sharedPrefs.getBool("auth") == true ||
                sharedPrefs.getBool("isGoogle") == true
            ? RefreshIndicator(
                onRefresh: _onRefresh,
                child: CustomScrollView(
                  slivers: [
                    const SliverToBoxAdapter(
                      child: SizedBox(
                        height: 16,
                      ),
                    ),
                    PagedSliverList(
                      pagingController: _pagingController,
                      builderDelegate: PagedChildBuilderDelegate<LowonganModel>(
                        itemBuilder: (context, item, index) => Padding(
                          padding: const EdgeInsets.only(
                              bottom: 8, left: 20, right: 20),
                          child: cardLowongan(item),
                        ),
                        noItemsFoundIndicatorBuilder: (context) => Center(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/images/empty_data_notif.png',
                                  width: 200,
                                  height: 200,
                                ),
                                SizedBox(height: 20),
                                Text(
                                  "tersimpan.data_kosong".tr,
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 10),
                                Text(
                                  "tersimpan.data_kosong_desc".tr,
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: KonfirmasiLogin(),
              ),
      )),
    );
  }

  Widget cardSearch(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.search,
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(vertical: 6),
                  hintText: "beranda.hint_searching".tr,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  suffixIcon: searchController.text.isNotEmpty ||
                          searchController.text != ""
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: () async {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.remove('filter_search');
                            searchController.clear();
                            _pagingController.refresh();
                          },
                        )
                      : null,
                ),
                onChanged: (value) {
                  setState(() {});
                },
                onSubmitted: (value) async {
                  final prefs = await SharedPreferences.getInstance();
                  if (value.isNotEmpty) {
                    await prefs.setString('filter_search', value);
                  } else {
                    await prefs.remove('filter_search');
                  }
                  _pagingController.refresh();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget cardLowongan(dynamic item) {
    return Opacity(
      opacity: DateTime.parse(item.expairedDate).isAfter(DateTime.now()) ||
              DateTime.parse(item.expairedDate)
                  .isAtSameMomentAs(DateTime.now()) ||
              item.status.toString().toLowerCase() == 'on proccess' &&
                  item.status.toString().toLowerCase() == 'ultra'
          ? 1
          : 0.5,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () {
            if (DateTime.parse(item.expairedDate).isAfter(DateTime.now()) ||
                DateTime.parse(item.expairedDate)
                    .isAtSameMomentAs(DateTime.now()) ||
                item.status.toString().toLowerCase() == 'on proccess' &&
                    item.status.toString().toLowerCase() == 'ultra') {
              Get.toNamed(Routes.lowonganDetail, arguments: item);
            } else {
              Get.snackbar("", "controller.info-lowongan".tr,
                  snackPosition: SnackPosition.BOTTOM,
                  borderRadius: 10,
                  margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 4),
                  animationDuration: const Duration(milliseconds: 500),
                  forwardAnimationCurve: Curves.easeOutBack,
                  reverseAnimationCurve: Curves.easeInBack,
                  colorText: Colors.white,
                  titleText: SizedBox.shrink(),
                  icon: Icon(
                    Icons.warning,
                    color: Colors.white,
                  ),
                  mainButton: TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: Text(
                      "tombol.dismiss".tr,
                      style: TextStyle(color: Colors.white),
                    ),
                  ));
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Logo
                    ClipRRect(
                      borderRadius: (isValidImageUrl(item.logoURL) &&
                              item.logoURL.isNotEmpty)
                          ? BorderRadius.circular(0)
                          : BorderRadius.circular(10),
                      child: (isValidImageUrl(item.logoURL) &&
                              item.logoURL.isNotEmpty)
                          ? Image.network(
                              item.logoURL,
                              fit: BoxFit.contain,
                              width: 70,
                              height: 70,
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  ImageAssets.logo2,
                                  fit: BoxFit.cover,
                                  width: 60,
                                  height: 60,
                                );
                              },
                            )
                          : Image.asset(
                              ImageAssets.logo2,
                              fit: BoxFit.cover,
                              width: 60,
                              height: 60,
                            ),
                    ),
                    const SizedBox(width: 10),
                    // Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Posisi
                          Text(
                            item.posisi,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          // Perusahaan
                          Text(
                            item.perusahaan,
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[500]),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          // Lamar
                          if (item.isLamar)
                            Row(
                              children: [
                                const Icon(
                                  Icons.check_circle_outline,
                                  color: Colors.green,
                                  size: 14,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  "beranda.lamar".tr,
                                  style: const TextStyle(
                                    color: Colors.green,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    // Favorite
                    IconButton(
                      onPressed: () async {
                        item.isFavorit = !item.isFavorit;
                        // SchedulerBinding.instance.addPostFrameCallback((_) {
                        //   setState(() {});
                        // });
                        final success = await lowonganController.saveLowongan(
                          item.tempKode,
                          item.isFavorit,
                        );
                        if (success) {
                          lowonganController.triggerRefreshBeranda();
                          lowonganController.triggerRefreshTersimpan();
                        }
                      },
                      icon: Icon(
                        item.isFavorit
                            ? Icons.bookmark_added
                            : Icons.bookmark_added_outlined,
                        color: item.isFavorit ? ColorAsset.primaryColor : null,
                      ),
                    ),
                  ],
                ),
                Divider(
                  color: Colors.grey[300],
                  height: 20,
                ),
                Row(
                  children: [
                    // Spacer for logo
                    const SizedBox(width: 80),
                    // Lokasi dan tipe pekerjaan
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Lokasi kerja
                          Text(
                            item.lokasiKerja2,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[500],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 10),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                TranslationService.translateTimeAgo(
                                    item.waktuLalu),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: ColorAsset.secodaryColor,
                                ),
                              ),
                              Text(
                                " • ",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              Text(
                                TranslationService.translateDateString(
                                    item.tglPosting),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: ColorAsset.secodaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          // Tipe pekerjaan
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              TranslationService.translateBetweenLangs(
                                  item.tipePekerjaan,
                                  "jenis_pekerjaan",
                                  "id",
                                  Get.locale?.languageCode.toLowerCase() ??
                                      "id"),
                              style: const TextStyle(
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
