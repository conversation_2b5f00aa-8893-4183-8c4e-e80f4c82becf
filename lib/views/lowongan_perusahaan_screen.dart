import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart' as urlLauncher;

class LowonganPerusahaanScreen extends StatefulWidget {
  const LowonganPerusahaanScreen({super.key});

  @override
  State<LowonganPerusahaanScreen> createState() =>
      _LowonganPerusahaanScreenState();
}

class _LowonganPerusahaanScreenState extends State<LowonganPerusahaanScreen> {
  final LowonganController lowonganController = Get.put(LowonganController());
  static const _pageSize = 5;
  final PagingController<int, LowonganModel> _pagingController =
      PagingController(firstPageKey: 1);

  late String idKoordinator;
  late String namaPerusahaan;

  @override
  void initState() {
    super.initState();
    if (Get.arguments != null) {
      idKoordinator = Get.arguments['id_koordinator'] ?? '';
      namaPerusahaan = Get.arguments['nama_perusahaan'] ?? '';
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        ever(lowonganController.refreshPerusahaan, (_) {
          _pagingController.refresh(); // refresh list di beranda
        });
        _pagingController.addPageRequestListener((pageKey) {
          _fetchPage(pageKey);
        });

        _pagingController.refresh();
        _pagingController.notifyPageRequestListeners(1); // ⬅️ PENTING
      });
    }
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // ✅ Cegah pemanggilan API jika sudah tahu totalPage dan pageKey > totalPage
      final int totalPage = lowonganController.totalPage.value;
      if (totalPage != 0 && pageKey > totalPage) {
        if (kDebugMode) {
          print('❌ Page $pageKey melebihi totalPage ($totalPage), SKIP fetch.');
        }
        _pagingController.appendLastPage([]);
        return;
      }

      final newItems = await lowonganController.getListJobCompany(
        idKoordinator,
        pageKey,
        _pageSize,
      );

      // Hitung ulang totalPage kalau belum terisi
      final isLastPage = pageKey >= lowonganController.totalPage.value ||
          newItems.length < _pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }

      if (kDebugMode) {
        print('✅ FETCH PAGE $pageKey');
        print('API response count: ${newItems.length}');
        print('Total list after append: ${_pagingController.itemList?.length}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('❌ Error saat fetch page $pageKey: $error');
      }
      _pagingController.error = error;
    }
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    lowonganController.totalPage.value = 0; // Optional: reset totalPage
    _pagingController.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
          child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            forceElevated: true,
            expandedHeight: 60, // Batasi tinggi maksimal
            toolbarHeight: 60,
            floating: true,
            pinned: false,
            snap: true,
            backgroundColor: Colors.white, // Warna tetap putih
            elevation: 0,
            shadowColor: Colors.black.withOpacity(0.0),
            automaticallyImplyLeading: false,
            title: const Text(''),
            leading: IconButton(
              color: Colors.black,
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Get.back(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                height: 100,
                padding: const EdgeInsets.only(left: 30),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: SingleChildScrollView(
                    physics:
                        const NeverScrollableScrollPhysics(), // Supaya tidak ikut scroll utama
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "${"perusahaan.lowongan_di".tr} $namaPerusahaan",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        body: RefreshIndicator(
          onRefresh: _onRefresh,
          child: CustomScrollView(
            slivers: [
              const SliverToBoxAdapter(
                child: SizedBox(
                  height: 16,
                ),
              ),
              PagedSliverList<int, LowonganModel>(
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<LowonganModel>(
                  itemBuilder: (context, item, index) => Padding(
                    padding:
                        const EdgeInsets.only(bottom: 8, left: 8, right: 8),
                    child: cardLowongan(item),
                  ),
                ),
              ),
            ],
          ),
        ),
      )),
    );
  }

  Widget cardLowongan(dynamic item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () async {
          if (item.status == "ultra") {
            await urlLauncher.launch(item.link);
            return;
          }
          Get.toNamed(Routes.lowonganDetail, arguments: item);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo
                  ClipRRect(
                    borderRadius: (isValidImageUrl(item.logoURL) &&
                            item.logoURL.isNotEmpty)
                        ? BorderRadius.circular(0)
                        : BorderRadius.circular(10),
                    child: (isValidImageUrl(item.logoURL) &&
                            item.logoURL.isNotEmpty)
                        ? Image.network(
                            item.logoURL,
                            fit: BoxFit.contain,
                            width: 70,
                            height: 70,
                            errorBuilder: (context, error, stackTrace) {
                              return Image.asset(
                                ImageAssets.logo2,
                                fit: BoxFit.cover,
                                width: 60,
                                height: 60,
                              );
                            },
                          )
                        : Image.asset(
                            ImageAssets.logo2,
                            fit: BoxFit.cover,
                            width: 60,
                            height: 60,
                          ),
                  ),
                  const SizedBox(width: 10),
                  // Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Posisi
                        Text(
                          item.posisi,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Perusahaan
                        Text(
                          item.perusahaan,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[500]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Lamar
                        if (item.isLamar)
                          Row(
                            children: [
                              const Icon(
                                Icons.check_circle_outline,
                                color: Colors.green,
                                size: 14,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                "beranda.lamar".tr,
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  // Favorite
                  if (item.status != "ultra")
                    IconButton(
                      onPressed: () async {
                        final prefs = Get.find<SharedPreferences>();
                        final isLoggedIn = prefs.getBool("auth") ?? false;
                        final isGoogle = prefs.getBool("isGoogle") ?? false;
                        if (isLoggedIn || isGoogle) {
                          item.isFavorit = !item.isFavorit;
                          SchedulerBinding.instance.addPostFrameCallback((_) {
                            setState(() {});
                          });
                          final success = await lowonganController.saveLowongan(
                            item.tempKode,
                            item.isFavorit,
                          );
                          if (success) {
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            lowonganController.triggerRefreshTersimpan();
                          }
                        } else {
                          var confirm = await showConfirmationDialog(
                              context,
                              "dialog_out.explore_judul".tr,
                              "dialog_out.explore_simpan_lowongan".tr,
                              "konfirmasi.iya2".tr,
                              "konfirmasi.tidak".tr);
                          if (confirm == true) {
                            Get.offAllNamed('/login');
                          }
                        }
                      },
                      icon: Icon(
                        item.isFavorit
                            ? Icons.bookmark_added
                            : Icons.bookmark_added_outlined,
                        color: item.isFavorit ? ColorAsset.primaryColor : null,
                      ),
                    ),
                ],
              ),
              Divider(
                color: Colors.grey[300],
                height: 20,
              ),
              Row(
                children: [
                  // Spacer for logo
                  const SizedBox(width: 80),
                  // Lokasi dan tipe pekerjaan
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Lokasi kerja
                        Text(
                          item.lokasiKerja2,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[500],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              TranslationService.translateTimeAgo(
                                  item.waktuLalu),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: ColorAsset.secodaryColor,
                              ),
                            ),
                            Text(
                              " • ",
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                            Flexible(
                              child: Text(
                                TranslationService.translateDateString(
                                    item.tglPosting),
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.normal,
                                  color: ColorAsset.secodaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        // Tipe pekerjaan
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            TranslationService.translateBetweenLangs(
                                item.tipePekerjaan,
                                "jenis_pekerjaan",
                                "id",
                                Get.locale?.languageCode.toLowerCase() ?? "id"),
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
