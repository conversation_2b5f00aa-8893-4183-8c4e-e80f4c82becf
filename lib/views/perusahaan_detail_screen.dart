import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:html_unescape/html_unescape.dart';

class PerusahaanDetailScreen extends StatefulWidget {
  const PerusahaanDetailScreen({super.key});

  @override
  State<PerusahaanDetailScreen> createState() => _PerusahaanDetailScreenState();
}

class _PerusahaanDetailScreenState extends State<PerusahaanDetailScreen> {
  final LowonganController lowonganController = Get.put(LowonganController());
  final unescape = HtmlUnescape();
  late LowonganModel lowongan;
  List<LowonganModel> lowonganList = [];
  @override
  void initState() {
    super.initState();
    if (Get.arguments != null) {
      lowongan = Get.arguments as LowonganModel;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        loadJobCompany(lowongan.idKoordinator);
      });
    }
  }

  void loadJobCompany(String idKoordinator) async {
    var result =
        await lowonganController.getListJobCompany(idKoordinator, 1, 3);

    if (result.isNotEmpty) {
      lowonganList = result;
    } else {
      lowonganList = [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: 16,
                ),
                width: MediaQuery.of(context).size.width,
                child: Row(
                  children: [
                    if (isValidImageUrl(lowongan.logoURL) &&
                        lowongan.logoURL.isNotEmpty)
                      Image.network(
                        lowongan.logoURL,
                        height: 65,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            ImageAssets.logo2,
                            fit: BoxFit.cover,
                            width: 65,
                            height: 65,
                          );
                        },
                      )
                    else
                      Image.asset(
                        ImageAssets.logo2,
                        fit: BoxFit.cover,
                        width: 65,
                        height: 65,
                      ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            lowongan.perusahaan,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "perusahaan.alamat".tr,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            lowongan.alamatPerusahaan,
                            style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.normal,
                                color: Colors.black),
                            maxLines: 5,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            softWrap: true,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "perusahaan.industri".tr,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    lowongan.tipePerusahaan,
                                    style: TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "perusahaan.link".tr,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    lowongan.linkPerusahaan,
                                    style: TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Divider(
                thickness: 7,
                height: 7,
              ),
              SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Material(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: BorderSide(
                      color: Colors.grey,
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "perusahaan.tentang".tr,
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Divider(
                          thickness: 0.5,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        // Text(
                        //   lowongan.descPerusahaan,
                        //   style: TextStyle(
                        //       fontSize: 12,
                        //       fontWeight: FontWeight.normal,
                        //       color: Colors.black),
                        //   textAlign: TextAlign.justify,
                        //   softWrap: true,
                        // ),
                        Html(
                          data: unescape.convert(lowongan.descPerusahaan),
                          style: {
                            "body": Style(
                              fontSize: FontSize(12),
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                              padding: HtmlPaddings.zero,
                              margin: Margins.zero,
                              textAlign: TextAlign.justify,
                            ),
                            "p": Style(
                              fontSize: FontSize(12),
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                              padding: HtmlPaddings.zero,
                              margin: Margins.zero,
                              textAlign: TextAlign.justify,
                            ),
                            "ol": Style(
                              fontSize: FontSize(12),
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                              padding: HtmlPaddings.zero,
                              margin: Margins.zero,
                            ),
                            "li": Style(
                              display: Display.block,
                              margin: Margins.only(bottom: 4),
                            ),
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              SizedBox(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Material(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    elevation: 3,
                    child: Column(
                      children: [
                        // 🔹 Bagian atas tetap di atas
                        Container(
                          width: double.infinity,
                          padding:
                              EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          decoration: BoxDecoration(
                            color: ColorAsset.secodaryColor,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              " ${"perusahaan.lowongan_tersedia".tr} ${lowongan.perusahaan}",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white),
                              softWrap: true,
                            ),
                          ),
                        ),
                        Container(
                            width: MediaQuery.of(context).size.width,
                            padding: EdgeInsets.symmetric(vertical: 12),
                            child: Obx(() {
                              if (lowonganController.isLoading.value) {
                                return Center(
                                    child: CircularProgressIndicator());
                              }

                              if (lowonganList.isEmpty) {
                                return Center(child: Text("data_kosong".tr));
                              }
                              return ListView.separated(
                                shrinkWrap:
                                    true, // ✅ Tambahkan ini agar ListView mengikuti ukuran anak-anaknya
                                physics:
                                    NeverScrollableScrollPhysics(), // ✅ Nonaktifkan scroll internal jika berada di dalam scroll lain
                                separatorBuilder: (context, index) => Divider(
                                  color: Colors.grey, // Warna divider
                                  thickness: 1, // Ketebalan divider
                                  height: 1,
                                ),
                                itemCount: lowonganList.length,
                                itemBuilder: (context, index) {
                                  return Material(
                                    color: Colors.white,
                                    child: InkWell(
                                      onTap: () {
                                        Get.toNamed(Routes.lowonganDetail,
                                            arguments: lowonganList[index]);
                                      },
                                      child: Padding(
                                        // ✅ Tambahkan Padding agar terlihat lebih baik
                                        padding: EdgeInsets.all(12),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    lowonganList[index].posisi,
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                    softWrap: true,
                                                  ),
                                                ),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.location_on,
                                                      size: 15,
                                                    ),
                                                    SizedBox(
                                                      width: 5,
                                                    ),
                                                    Text(
                                                      lowonganList[index]
                                                          .lokasiKerja2,
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                      ),
                                                      softWrap: true,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Icon(
                                                  Icons.date_range,
                                                  size: 15,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      TranslationService
                                                          .translateBetweenLangs(
                                                        lowonganList[index]
                                                            .tipePekerjaan
                                                            .tr,
                                                        "jenis_pekerjaan",
                                                        "id",
                                                        Get.locale?.languageCode
                                                                .toLowerCase() ??
                                                            "id",
                                                      ),
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                      ),
                                                    ),
                                                    Text(
                                                      TranslationService
                                                          .translateDateString(
                                                              lowonganList[
                                                                      index]
                                                                  .tglPosting),
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                      ),
                                                      softWrap: true,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            Align(
                                              alignment: Alignment.centerRight,
                                              child: Text(
                                                TranslationService
                                                    .translateTimeAgo(
                                                        lowonganList[index]
                                                            .waktuLalu),
                                                style: TextStyle(fontSize: 10),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            })),
                        TextButton(
                          onPressed: () {
                            Get.toNamed(Routes.lowonganPerusahaan, arguments: {
                              "id_koordinator": lowongan.idKoordinator,
                              "nama_perusahaan": lowongan.perusahaan,
                            });
                          },
                          child: Text(
                            "tombol.lowongan_lainnya".tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: ColorAsset.primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 15,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
