import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LupaPasswordScreen extends StatefulWidget {
  const LupaPasswordScreen({super.key});

  @override
  State<LupaPasswordScreen> createState() => _LupaPasswordScreenState();
}

class _LupaPasswordScreenState extends State<LupaPasswordScreen> {
  final LoginController authController = Get.find<LoginController>();
  final TextEditingController _emailController = TextEditingController();
  String? emailError;

  @override
  void initState() {
    super.initState();
  }

  final key = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: Colors.white,
          title: Text(
            "lupa_password.judul".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          iconTheme: IconThemeData(
            color: Colors.black,
          ),
        ),
        body: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              children: [
                // 🔹 Form berada di tengah
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: key,
                    child: Column(
                      children: [
                        _buildTextField(
                          "lupa_password.subjudul".tr,
                          emailError ?? '',
                          _emailController,
                          TextInputType.emailAddress,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Padding(
          padding: EdgeInsets.only(
            left: 16.0,
            right: 16.0,
            bottom: MediaQuery.of(context).viewInsets.bottom > 0
                ? MediaQuery.of(context).viewInsets.bottom + 8.0
                : 16.0,
            top: 8.0,
          ),
          child: Obx(
            () => authController.isLoading.value
                ? SizedBox(
                    width: 50,
                    height: 50,
                    child: Center(child: CircularProgressIndicator()),
                  )
                : SizedBox(
                    height: 50,
                    width: 150,
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          emailError = '';
                        });
                        if (_emailController.text.isEmpty) {
                          setState(() {
                            emailError = 'daftar.format_email_invalid'.tr;
                          });
                          return;
                        }
                        authController.reqResetPassword(
                          _emailController.text,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50),
                        ),
                        textStyle: const TextStyle(fontSize: 14),
                      ),
                      child: Text(
                        "tombol.kirim".tr,
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
          ),
        ));
  }

  Widget _buildTextField(String hintText, String validator,
      TextEditingController controller, TextInputType inputType) {
    return FormTextFiled(
      title: "lupa_password.subjudul".tr,
      controller: controller,
      keyboardType: inputType,
      hintText: "lupa_password.email".tr,
      errorText: validator != "" ? validator : null,
      // prefixIcon: icon != null ? Icon(icon) : null,
      // isReadOnly: isReadOnly,
      minLines: 1,
      maxLines: null,
      isRequired: true,
      type: hintText,
    );
  }
}
