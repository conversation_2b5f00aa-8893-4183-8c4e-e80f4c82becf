import 'package:digital_cv_mobile/controllers/chat_message_controller.dart';
import 'package:digital_cv_mobile/controllers/chat_notification_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/models/chat_message_model.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class ChatScreen extends StatefulWidget {
  ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final controller = Get.put(ChatMessageController());
  final ScrollController _scrollController = ScrollController();
  final RxString currentVisibleDate = ''.obs;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Listen to messages changes to update visible date
    ever(controller.messages, (_) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateCurrentVisibleDate();
      });
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Find the current visible date based on scroll position
    _updateCurrentVisibleDate();

    // Trigger auto read when user scrolls (indicates they're reading messages)
    _triggerAutoRead();
  }

  void _updateCurrentVisibleDate() {
    if (controller.messages.isEmpty) {
      currentVisibleDate.value = '';
      return;
    }

    try {
      if (!_scrollController.hasClients) return;

      // Get viewport information
      final scrollOffset = _scrollController.offset;
      final viewportHeight = _scrollController.position.viewportDimension;
      final maxScrollExtent = _scrollController.position.maxScrollExtent;

      // Calculate the center of the viewport
      final centerOffset = scrollOffset + (viewportHeight / 2);

      // Since ListView is reversed, we need to find the message at the center
      // Estimate item height (approximate)
      const estimatedItemHeight = 80.0;
      final totalMessages = controller.messages.length;

      if (totalMessages == 0) return;

      // Calculate which message is at the center of the viewport
      final estimatedIndex = (centerOffset / estimatedItemHeight).floor();
      final adjustedIndex =
          (totalMessages - 1 - estimatedIndex).clamp(0, totalMessages - 1);

      // Find the nearest date separator going backwards from the center
      DateTime? nearestDate;
      for (int i = adjustedIndex; i >= 0; i--) {
        final item = controller.messages[i];
        if (item is DateTime) {
          nearestDate = item;
          break;
        }
      }

      // If no date found going backwards, try going forwards
      if (nearestDate == null) {
        for (int i = adjustedIndex; i < totalMessages; i++) {
          final item = controller.messages[i];
          if (item is DateTime) {
            nearestDate = item;
            break;
          }
        }
      }

      if (nearestDate != null) {
        final dateLabel = _formatDateLabel(nearestDate);
        if (currentVisibleDate.value != dateLabel) {
          currentVisibleDate.value = dateLabel;
        }
      } else if (controller.messages.isNotEmpty) {
        // Fallback: use today's date if no date separator found
        final dateLabel = _formatDateLabel(DateTime.now());
        if (currentVisibleDate.value != dateLabel) {
          currentVisibleDate.value = dateLabel;
        }
      }
    } catch (e) {
      // Handle any errors silently
    }
  }

  String _formatDateLabel(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final inputDate = DateTime(date.year, date.month, date.day);

    final difference = today.difference(inputDate).inDays;

    if (difference == 0) {
      return "Hari Ini";
    } else if (difference == 1) {
      return 'Kemarin';
    } else if (difference < 7) {
      return DateFormat('EEEE', 'id_ID').format(date);
    } else if (date.year == now.year) {
      return DateFormat('d MMMM', 'id_ID').format(date);
    } else {
      return DateFormat('d MMMM yyyy', 'id_ID').format(date);
    }
  }

  String _getTimeBasedGreeting() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 5 && hour < 11) {
      return 'Halo, selamat pagi!';
    } else if (hour >= 11 && hour < 15) {
      return 'Halo, selamat siang!';
    } else if (hour >= 15 && hour < 18) {
      return 'Halo, selamat sore!';
    } else {
      return 'Halo, selamat malam!';
    }
  }

  String _getGreetingMessage() {
    final now = DateTime.now();
    final hour = now.hour;
    final greeting = _getTimeBasedGreeting();

    if (hour >= 5 && hour < 11) {
      return '$greeting Saya ingin menanyakan tentang lamaran saya.';
    } else if (hour >= 11 && hour < 15) {
      return '$greeting Saya ingin menanyakan tentang status lamaran saya.';
    } else if (hour >= 15 && hour < 18) {
      return '$greeting Saya ingin mengetahui perkembangan lamaran saya.';
    } else {
      return '$greeting Maaf mengganggu waktu istirahat. Saya ingin menanyakan tentang lamaran saya.';
    }
  }

  String _getStatusInquiryMessage() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 5 && hour < 11) {
      return 'Selamat pagi! Saya ingin bertanya tentang status lamaran saya. Apakah ada update terbaru?';
    } else if (hour >= 11 && hour < 15) {
      return 'Selamat siang! Saya ingin mengetahui perkembangan lamaran saya. Apakah ada informasi terbaru?';
    } else if (hour >= 15 && hour < 18) {
      return 'Selamat sore! Saya ingin menanyakan status lamaran saya. Bagaimana perkembangannya?';
    } else {
      return 'Selamat malam! Maaf mengganggu waktu istirahat. Saya ingin menanyakan status lamaran saya.';
    }
  }

  String _getInterviewInquiryMessage() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 5 && hour < 11) {
      return 'Selamat pagi! Kapan jadwal interview yang tersedia? Saya siap mengikuti interview.';
    } else if (hour >= 11 && hour < 15) {
      return 'Selamat siang! Saya ingin menanyakan jadwal interview. Kapan waktu yang tersedia?';
    } else if (hour >= 15 && hour < 18) {
      return 'Selamat sore! Apakah sudah ada jadwal interview? Saya siap untuk mengikutinya.';
    } else {
      return 'Selamat malam! Saya ingin menanyakan tentang jadwal interview. Kapan waktu yang tersedia?';
    }
  }

  String _getThankYouMessage() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 5 && hour < 11) {
      return 'Selamat pagi! Terima kasih atas kesempatan yang diberikan. Saya sangat antusias dengan posisi ini.';
    } else if (hour >= 11 && hour < 15) {
      return 'Selamat siang! Terima kasih atas waktu dan kesempatan yang telah diberikan. Saya sangat menghargainya.';
    } else if (hour >= 15 && hour < 18) {
      return 'Selamat sore! Terima kasih atas proses rekrutmen yang profesional. Saya sangat antusias bergabung.';
    } else {
      return 'Selamat malam! Terima kasih atas kesempatan yang diberikan. Saya sangat menghargai waktu yang telah diluangkan.';
    }
  }

  bool _canSendMessage() {
    // Can send if either WebSocket is connected OR HTTP API parameters are available
    return controller.isWebSocketConnected ||
        (controller.currentIdKoordinator != null &&
            controller.currentIdLamaran != null);
  }

  /// Auto read messages when user interacts with chat (scroll, tap, etc.)
  void _triggerAutoRead() {
    if (controller.currentIdLamaran != null) {
      try {
        final notificationController = Get.find<ChatNotificationController>();
        notificationController.autoReadMessages(controller.currentIdLamaran!);
      } catch (e) {
        // Silently handle error - auto read is not critical
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        title: const Text(
          "Chat HRD",
          style: TextStyle(color: Colors.black),
        ),
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // Loading indicator for chat history
              Obx(() {
                if (controller.isLoadingHistory.value) {
                  return Container(
                    padding: const EdgeInsets.all(8),
                    color: Colors.blue.shade50,
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Loading...'),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              Expanded(
                child: Obx(() {
                  // Show empty state when no messages
                  if (controller.messages.isEmpty) {
                    return _buildEmptyState();
                  }

                  return GestureDetector(
                    onTap: () {
                      // Trigger auto read when user taps on chat area
                      _triggerAutoRead();
                    },
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(12),
                      itemCount: controller.messages.length,
                      reverse: true,
                      itemBuilder: (context, index) {
                        dynamic item = controller
                            .messages[controller.messages.length - 1 - index];

                        if (item is DateTime) {
                          return SizedBox.shrink();
                        }

                        ChatMessage message = item as ChatMessage;

                        return Align(
                          alignment: message.isFromCandidate
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: message.isFromCandidate
                                  ? const Color(
                                      0xFFFFF3CD) // Yellow for candidate
                                  : const Color.fromARGB(
                                      255, 215, 243, 255), // Blue for company
                              borderRadius: BorderRadius.only(
                                  topLeft: message.isFromCandidate
                                      ? Radius.circular(12)
                                      : Radius.circular(0),
                                  topRight: message.isFromCandidate
                                      ? Radius.circular(0)
                                      : Radius.circular(12),
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12)),
                            ),
                            constraints:
                                BoxConstraints(maxWidth: Get.width / 1.3),
                            child: Column(
                              crossAxisAlignment: message.isFromCandidate
                                  ? CrossAxisAlignment.start
                                  : CrossAxisAlignment.end,
                              children: [
                                Text(
                                  item.message,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(fontSize: 14),
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Text(
                                  DateFormat('HH:mm', 'id_ID')
                                      .format(item.timestamp),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }),
              ),

              // input bar
              SafeArea(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          onChanged: (val) =>
                              controller.messageInput.text = val,
                          controller: controller.messageInput,
                          maxLines: 5,
                          minLines: 1,
                          keyboardType: TextInputType.multiline,
                          textInputAction: TextInputAction.newline,
                          decoration: InputDecoration(
                            hintText: "Tulis pesan...",
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(25),
                              borderSide:
                                  BorderSide(color: ColorAsset.primaryColor),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(25),
                              borderSide:
                                  BorderSide(color: ColorAsset.primaryColor),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(25),
                              borderSide:
                                  BorderSide(color: ColorAsset.primaryColor),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Obx(() => IconButton(
                            icon: controller.isSendingMessage.value
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation(
                                          Colors.amber.shade600),
                                    ),
                                  )
                                : Icon(
                                    Icons.send,
                                    color: _canSendMessage()
                                        ? Colors.amber
                                        : Colors.grey,
                                  ),
                            onPressed: controller.isSendingMessage.value
                                ? null
                                : _canSendMessage()
                                    ? () => controller.sendMessage(
                                        controller.messageInput.text)
                                    : null,
                          ))
                    ],
                  ),
                ),
              )
            ],
          ),
          // Sticky Date Header (like WhatsApp)
          Positioned(
            top: 8,
            left: 0,
            right: 0,
            child: Obx(() {
              if (currentVisibleDate.value.isEmpty ||
                  controller.messages.isEmpty) {
                return const SizedBox.shrink();
              }
              return Center(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade500,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Text(
                    currentVisibleDate.value,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.3,
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated Chat Icon
            TweenAnimationBuilder<double>(
              duration: const Duration(seconds: 2),
              tween: Tween(begin: 0.8, end: 1.0),
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Image.asset(ImageAssets.logo),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              'Mulai Percakapan',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 12),

            // Subtitle
            Text(
              'Belum ada pesan dalam percakapan ini.\nKirim pesan pertama untuk memulai chat dengan HRD.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),

            // Quick action buttons
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 12,
              runSpacing: 12,
              children: [
                // Say Hello button
                _buildQuickActionButton(
                  icon: Icons.waving_hand,
                  label: 'Sapa HRD',
                  message: _getGreetingMessage(),
                  isPrimary: true,
                  sendImmediately: true,
                ),

                // Ask Status button
                _buildQuickActionButton(
                  icon: Icons.help_outline,
                  label: 'Tanya Status',
                  message: _getStatusInquiryMessage(),
                  isPrimary: false,
                  sendImmediately: false,
                ),

                // Schedule Interview button
                _buildQuickActionButton(
                  icon: Icons.calendar_today,
                  label: 'Jadwal Interview',
                  message: _getInterviewInquiryMessage(),
                  isPrimary: false,
                  sendImmediately: false,
                ),

                // Thank You button
                _buildQuickActionButton(
                  icon: Icons.favorite_outline,
                  label: 'Terima Kasih',
                  message: _getThankYouMessage(),
                  isPrimary: false,
                  sendImmediately: false,
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required String message,
    required bool isPrimary,
    required bool sendImmediately,
  }) {
    return ElevatedButton.icon(
      onPressed: () {
        controller.messageInput.text = message;
        if (sendImmediately) {
          controller.sendMessage(message);
        }
        // If not sending immediately, just fill the text field for user to edit
      },
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: const TextStyle(fontSize: 13),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isPrimary ? Colors.blue.shade500 : Colors.grey.shade200,
        foregroundColor: isPrimary ? Colors.white : Colors.grey.shade700,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: isPrimary ? 2 : 0,
      ),
    );
  }

  Widget _buildConnectionStatus() {
    switch (controller.webSocketStatus) {
      case WebSocketStatus.connecting:
        return const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            ),
            SizedBox(width: 4),
            Text(
              'Connecting...',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            ),
          ],
        );
      case WebSocketStatus.connected:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              'Online',
              style: TextStyle(fontSize: 12, color: Colors.green),
            ),
          ],
        );
      case WebSocketStatus.disconnected:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              'Offline',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        );
      case WebSocketStatus.error:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            const Text(
              'Error',
              style: TextStyle(fontSize: 12, color: Colors.red),
            ),
          ],
        );
    }
  }
}
