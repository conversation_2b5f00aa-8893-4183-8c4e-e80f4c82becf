import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/provinsi_dialog.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/kursus_model.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PelatihanScreen extends StatefulWidget {
  const PelatihanScreen({super.key});

  @override
  State<PelatihanScreen> createState() => _PelatihanScreenState();
}

class _PelatihanScreenState extends State<PelatihanScreen> {
  final FormController formController = Get.find<FormController>();
  final TextEditingController _namaPelatihanController =
      TextEditingController();
  final TextEditingController _namaInstansiController = TextEditingController();
  final TextEditingController _tglMulaiController = TextEditingController();
  final TextEditingController _tglSelesaiController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late KursusModel kursusModel;
  List<Map<String, dynamic>> data = [];
  String? selectedValue;
  String? selectedSertifikat;
  int? idLocation;
  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "dcv.pelatihan.txt_nama_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pelatihan.txt_instansi_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pelatihan.txt_tgl_mulai_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pelatihan.txt_tgl_selesai_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pelatihan.txt_sertifikat_blank".tr,
      "isError": false,
    }
  ];

  @override
  void initState() {
    super.initState();
    kursusModel = KursusModel(
      id: "",
      nama: "",
      tempat: "",
      tglMulai: "",
      tglSelesai: "",
      sertifikat: "",
    );
    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;
      if (args['kursus'] != null) {
        kursusModel = args['kursus'];
      } else {
        kursusModel = KursusModel(
          id: "",
          nama: "",
          tempat: "",
          tglMulai: "",
          tglSelesai: "",
          sertifikat: "",
        );
      }

      if (args['confirm'] != null) {
        selectedValue = args['confirm'] == true
            ? "konfirmasi2.iya".tr
            : "konfirmasi2.tidak".tr;
      } else {
        selectedValue = null;
      }
      _namaPelatihanController.text = kursusModel.nama;
      _namaInstansiController.text = kursusModel.tempat;
      _tglMulaiController.text = kursusModel.tglMulai;
      _tglSelesaiController.text = kursusModel.tglSelesai;
      selectedSertifikat = kursusModel.sertifikat != ""
          ? TranslationService.translateBetweenLangs(
              kursusModel.sertifikat,
              "dcv.pelatihan",
              "id",
              Get.locale?.languageCode.toLowerCase() ?? "id",
            )
          : null;
    }
  }

  DateTime? selectedTglMulai;

  Future<void> _selectTglMulai() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedTglMulai ?? DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 10),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );

    if (pickedDate != null) {
      setState(() {
        selectedTglMulai = pickedDate;
        _tglMulaiController.text =
            "${selectedTglMulai!.year}-${selectedTglMulai!.month.toString().padLeft(2, '0')}-${selectedTglMulai!.day.toString().padLeft(2, '0')}";
      });
    }
  }

  DateTime? selectedTglSelesai;

  Future<void> _selectTglSelesai() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedTglSelesai ?? DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 10),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );

    if (pickedDate != null) {
      setState(() {
        selectedTglSelesai = pickedDate;
        _tglSelesaiController.text =
            "${selectedTglSelesai!.year}-${selectedTglSelesai!.month.toString().padLeft(2, '0')}-${selectedTglSelesai!.day.toString().padLeft(2, '0')}";
      });
    }
  }

  void _bukaPilihLokasi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      LokasiBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      LogService.log.i("Lokasi yang dipilih: ${result.nama}");
      _namaInstansiController.text =
          result.nama; // Set text field dengan nama provinsi
      idLocation = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.pelatihan.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "dcv.pelatihan.txt_konfirm_pelatihan".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: DropdownButtonFormField<String>(
                            value: selectedValue,
                            isDense: true, // Mengurangi padding internal
                            isExpanded:
                                true, // Membuat dropdown mengisi lebar penuh
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 12), // Padding dalam Input
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ), // Ukuran teks dropdown
                            iconSize: 24, // Ukuran icon dropdown
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedValue = newValue!;
                              });
                            },
                            items: [
                              "konfirmasi2.iya".tr,
                              "konfirmasi2.tidak".tr
                            ].map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Container(
                                  height: 40, // Mengatur tinggi dropdown item
                                  alignment: Alignment
                                      .centerLeft, // Menyamakan posisi teks
                                  child: Text(
                                    value,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        selectedValue == "konfirmasi.iya".tr
                            ? SizedBox(
                                child: Column(
                                  children: [
                                    _buildTextField(
                                      "dcv.pelatihan.txt_nama".tr,
                                      errorsMessage[0]['isError']
                                          ? errorsMessage[0]['message']
                                          : "",
                                      _namaPelatihanController,
                                      TextInputType.text,
                                      null,
                                      false,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          "dcv.pelatihan.txt_instansi".tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          "*",
                                          style: TextStyle(
                                            color: Colors.red,
                                          ),
                                        )
                                      ],
                                    ),
                                    FormTextFiled(
                                      controller: _namaInstansiController,
                                      hintText: "dcv.pelatihan.txt_instansi".tr,
                                      onTap: () {
                                        _bukaPilihLokasi();
                                      },
                                      keyboardType: TextInputType.text,
                                      isReadOnly: true,
                                      maxLines: 1,
                                      suffixIcon: Icon(
                                        Icons.arrow_drop_down,
                                        size: 30,
                                      ),
                                      errorText: errorsMessage[1]['isError']
                                          ? errorsMessage[1]['message']
                                          : null,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                    "dcv.pelatihan.txt_tgl_mulai"
                                                        .tr,
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                  Text(
                                                    "*",
                                                    style: TextStyle(
                                                        color: Colors.red),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 4),
                                              FormTextFiled(
                                                controller: _tglMulaiController,
                                                onTap: () {
                                                  _selectTglMulai();
                                                },
                                                hintText:
                                                    "dcv.pelatihan.txt_tgl_mulai"
                                                        .tr,
                                                keyboardType:
                                                    TextInputType.text,
                                                isReadOnly: true,
                                                maxLines: 1,
                                                suffixIcon: Icon(
                                                  Icons.calendar_month_outlined,
                                                  size: 30,
                                                  color: Colors.grey[500],
                                                ),
                                                errorText: errorsMessage[2]
                                                        ['isError']
                                                    ? errorsMessage[2]
                                                        ['message']
                                                    : null,
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                            width: 15), // Jarak antar kolom
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                    "dcv.pelatihan.txt_tgl_selesai"
                                                        .tr,
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                  Text(
                                                    "*",
                                                    style: TextStyle(
                                                        color: Colors.red),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 4),
                                              FormTextFiled(
                                                controller:
                                                    _tglSelesaiController,
                                                onTap: () {
                                                  _selectTglSelesai();
                                                },
                                                hintText:
                                                    "dcv.pelatihan.txt_tgl_selesai"
                                                        .tr,
                                                keyboardType:
                                                    TextInputType.text,
                                                isReadOnly: true,
                                                maxLines: 1,
                                                suffixIcon: Icon(
                                                  Icons.calendar_month_outlined,
                                                  size: 30,
                                                  color: Colors.grey[500],
                                                ),
                                                errorText: errorsMessage[3]
                                                        ['isError']
                                                    ? errorsMessage[3]
                                                        ['message']
                                                    : null,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          "dcv.pelatihan.txt_sertifikat".tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          "*",
                                          style: TextStyle(
                                            color: Colors.red,
                                          ),
                                        )
                                      ],
                                    ),
                                    SizedBox(
                                      height: 8,
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: DropdownButtonFormField<String>(
                                        hint: Text(
                                            "dcv.pelatihan.txt_sertifikat".tr),
                                        value: selectedSertifikat,
                                        isDense:
                                            true, // Mengurangi padding internal
                                        isExpanded:
                                            true, // Membuat dropdown mengisi lebar penuh
                                        decoration: InputDecoration(
                                          contentPadding: EdgeInsets.symmetric(
                                              vertical: 10,
                                              horizontal:
                                                  12), // Padding dalam Input
                                          border: InputBorder.none,
                                        ),
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                        ), // Ukuran teks dropdown
                                        iconSize: 24, // Ukuran icon dropdown
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedSertifikat = newValue!;
                                          });
                                        },
                                        items: [
                                          "dcv.pelatihan.ada".tr,
                                          "dcv.pelatihan.tidak_ada".tr
                                        ].map<DropdownMenuItem<String>>(
                                            (String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Container(
                                              height:
                                                  40, // Mengatur tinggi dropdown item
                                              alignment: Alignment
                                                  .centerLeft, // Menyamakan posisi teks
                                              child: Text(
                                                value,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                    if (errorsMessage[4]['isError'] != false)
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 8.0),
                                        child: Align(
                                          alignment: Alignment.centerLeft,
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.error,
                                                color: Colors.red,
                                                size: 16,
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                errorsMessage[4]['message'],
                                                style: TextStyle(
                                                    color: Colors.red,
                                                    fontSize: 12),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (kursusModel.nama != "") ...[
                    // Tombol Hapus
                    Expanded(
                      child: SizedBox(
                        height: 45,
                        child: Obx(
                          () => formController.isLoading.value
                              ? Center(child: CircularProgressIndicator())
                              : ElevatedButton(
                                  onPressed: () async {
                                    formController
                                        .deleteRiwayatKursus(kursusModel.id);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 0,
                                      vertical: 6,
                                    ),
                                    minimumSize: const Size.fromHeight(45),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                    textStyle: TextStyle(fontSize: 14),
                                  ),
                                  child: Text(
                                    "tombol.hapus".tr,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                  ],
                  // Tombol Simpan
                  Expanded(
                    child: SizedBox(
                      height: 50,
                      child: Obx(
                        () => formController.isLoading.value
                            ? Center(child: CircularProgressIndicator())
                            : ElevatedButton(
                                onPressed: () async {
                                  for (var error in errorsMessage) {
                                    error['isError'] = false;
                                  }
                                  if (selectedValue == "konfirmasi.iya".tr) {
                                    if (_namaPelatihanController.text.isEmpty) {
                                      setState(() {
                                        errorsMessage[0]['isError'] = true;
                                      });
                                      Get.snackbar(
                                          "", "dcv.pelatihan.txt_nama_blank".tr,
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    }
                                    if (_namaInstansiController.text.isEmpty) {
                                      setState(() {
                                        errorsMessage[1]['isError'] = true;
                                      });
                                      Get.snackbar(
                                          "", errorsMessage[1]['message'],
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    }
                                    if (_tglMulaiController.text.isEmpty) {
                                      setState(() {
                                        errorsMessage[2]['isError'] = true;
                                      });
                                      Get.snackbar(
                                          "", errorsMessage[2]['message'],
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    }
                                    if (_tglSelesaiController.text.isEmpty) {
                                      setState(() {
                                        errorsMessage[3]['isError'] = true;
                                      });
                                      Get.snackbar(
                                          "", errorsMessage[3]['message'],
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    }
                                    if (selectedSertifikat == null ||
                                        selectedSertifikat!.isEmpty) {
                                      setState(() {
                                        errorsMessage[4]['isError'] = true;
                                      });
                                      Get.snackbar(
                                          "", errorsMessage[4]['message'],
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    } else {
                                      setState(() {
                                        errorsMessage[4]['isError'] = false;
                                      });
                                    }
                                  }

                                  if (_tglMulaiController.text.isNotEmpty &&
                                      _tglSelesaiController.text.isNotEmpty) {
                                    DateTime tglMulai = DateTime.parse(
                                        _tglMulaiController.text);
                                    DateTime tglSelesai = DateTime.parse(
                                        _tglSelesaiController.text);
                                    if (tglMulai.isAfter(tglSelesai)) {
                                      Get.snackbar(
                                          "",
                                          "dcv.pelatihan.txt_tgl_mulai_error"
                                              .tr,
                                          snackPosition: SnackPosition.BOTTOM,
                                          borderRadius: 10,
                                          margin: EdgeInsets.only(
                                              left: 15, right: 15, bottom: 70),
                                          backgroundColor: Colors.amber,
                                          duration: const Duration(seconds: 4),
                                          animationDuration:
                                              const Duration(milliseconds: 500),
                                          forwardAnimationCurve:
                                              Curves.easeOutBack,
                                          reverseAnimationCurve:
                                              Curves.easeInBack,
                                          colorText: Colors.black,
                                          titleText: SizedBox.shrink(),
                                          icon: Icon(
                                            Icons.warning,
                                            color: Colors.black,
                                          ),
                                          mainButton: TextButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            child: Text(
                                              "tombol.dismiss".tr,
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ));
                                      return;
                                    }
                                  }
                                  setState(() {
                                    errorsMessage[0]['isError'] = false;
                                    errorsMessage[1]['isError'] = false;
                                    errorsMessage[2]['isError'] = false;
                                    errorsMessage[3]['isError'] = false;
                                    errorsMessage[4]['isError'] = false;
                                  });
                                  data.clear();
                                  if (kursusModel.nama != "") {
                                    data.add({
                                      "id_kursus": kursusModel.id,
                                      "kursus": TranslationService
                                          .translateBetweenLangs(
                                              selectedValue ?? "Tidak",
                                              "konfirmasi2",
                                              Get.locale?.languageCode
                                                      .toLowerCase() ??
                                                  "id",
                                              "id"),
                                      "nama_kursus":
                                          _namaPelatihanController.text,
                                      "sertifikat_kursus": TranslationService
                                          .translateBetweenLangs(
                                              selectedSertifikat ?? "Tidak Ada",
                                              "dcv.pelatihan",
                                              Get.locale?.languageCode
                                                      .toLowerCase() ??
                                                  "id",
                                              "id"),
                                      "tempat_kursus":
                                          _namaInstansiController.text,
                                      "tgl_mulai_kursus":
                                          _tglMulaiController.text,
                                      "tgl_selesai_kursus":
                                          _tglSelesaiController.text,
                                    });
                                    formController.updateRiwayatKursus(data);
                                  } else {
                                    data.add({
                                      "kursus": TranslationService
                                          .translateBetweenLangs(
                                              selectedValue ?? "Tidak",
                                              "konfirmasi2",
                                              Get.locale?.languageCode
                                                      .toLowerCase() ??
                                                  "id",
                                              "id"),
                                      "nama_kursus":
                                          _namaPelatihanController.text,
                                      "sertifikat_kursus": TranslationService
                                          .translateBetweenLangs(
                                              selectedSertifikat ?? "Tidak Ada",
                                              "dcv.pelatihan",
                                              Get.locale?.languageCode
                                                      .toLowerCase() ??
                                                  "id",
                                              "id"),
                                      "tempat_kursus":
                                          _namaInstansiController.text,
                                      "tgl_mulai_kursus":
                                          _tglMulaiController.text,
                                      "tgl_selesai_kursus":
                                          _tglSelesaiController.text,
                                    });
                                    formController.saveRiwayatKursus(data);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 0,
                                    vertical: 6,
                                  ),
                                  minimumSize: const Size.fromHeight(45),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                  textStyle: TextStyle(fontSize: 14),
                                ),
                                child: Text(
                                  "tombol.simpan".tr,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
