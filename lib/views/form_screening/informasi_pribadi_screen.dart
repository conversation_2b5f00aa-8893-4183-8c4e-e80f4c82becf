import 'dart:io';
import 'dart:typed_data';

import 'package:crop_your_image/crop_your_image.dart';
import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/posisi_pekerjaan_dialog.dart';
import 'package:digital_cv_mobile/components/provinsi_dialog.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';
import 'package:digital_cv_mobile/models/rh_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class InfoPribadiScreen extends StatefulWidget {
  const InfoPribadiScreen({super.key});

  @override
  State<InfoPribadiScreen> createState() => _InfoPribadiScreenState();
}

class _InfoPribadiScreenState extends State<InfoPribadiScreen> {
  final LocationController locationController = Get.find<LocationController>();
  final ProfileController profileController = Get.find<ProfileController>();
  final FormController formController = Get.find<FormController>();
  final TextEditingController _namaLengkapController = TextEditingController();
  final TextEditingController _tempatLahirController = TextEditingController();
  final TextEditingController _tglLahirController = TextEditingController();
  final TextEditingController _noKtpController = TextEditingController();
  final TextEditingController _noTelpController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _provinsiController = TextEditingController();
  final TextEditingController _kotaController = TextEditingController();
  final TextEditingController _kecamatanController = TextEditingController();
  final TextEditingController _posisiKerjaanController =
      TextEditingController();
  final TextEditingController _rtController = TextEditingController();
  final TextEditingController _rwController = TextEditingController();
  final TextEditingController _alamatTinggalController =
      TextEditingController();
  final TextEditingController _kodePosController = TextEditingController();
  final TextEditingController _linkedInController = TextEditingController();
  final TextEditingController _tautanController = TextEditingController();
  final TextEditingController _instagramController = TextEditingController();
  final TextEditingController _facebookController = TextEditingController();
  final TextEditingController _twitterController = TextEditingController();
  final TextEditingController _tiktokController = TextEditingController();
  List<Map<String, dynamic>> data = [];
  final GlobalKey<FormState> _radioKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _checkboxKey = GlobalKey<FormState>();
  final List<Map<String, dynamic>> errorsMessage = [
    {"message": "dcv.info_pribadi.txt_nama_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_tempat_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_lahir_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_ktp_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_handphone_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_email_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_provinsi_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_kota_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_kecamatan_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_rt_blank".tr, "isError": false},
    {"message": "dcv.info_pribadi.txt_rw_blank".tr, "isError": false},
    {
      "message": "dcv.info_pribadi.txt_alamat_tinggal_blank".tr,
      "isError": false
    },
    {"message": "dcv.info_pribadi.txt_kode_pos_blank".tr, "isError": false},
    {
      "message": "dcv.info_pribadi.txt_posisi_kerjaan_blank".tr,
      "isError": false
    }
  ];
  String countryCode = "+62";
  String initNoHp = "";

  final ImagePicker _picker = ImagePicker();
  Future<void> pickImage() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();

      // Mendapatkan dimensi gambar
      final image = await decodeImageFromList(bytes);
      final imageWidth = image.width.toDouble();
      final imageHeight = image.height.toDouble();

      final cropController = CropController();

      showDialog(
        context: context,
        builder: (context) {
          Uint8List? croppedData;
          bool isCropping = false;

          return StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                insetPadding:
                    EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: SingleChildScrollView(
                  // Membungkus kolom dengan scrollable
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height *
                          0.8, // Membatasi tinggi dialog
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: imageHeight >
                                  MediaQuery.of(context).size.height * 0.6
                              ? MediaQuery.of(context).size.height * 0.6
                              : imageHeight, // Mengatur tinggi sesuai ukuran gambar
                          width: imageWidth,
                          child: Stack(
                            children: [
                              Crop(
                                controller: cropController,
                                image: bytes,
                                aspectRatio: 1,
                                onCropped: (result) {
                                  switch (result) {
                                    case CropSuccess(:final croppedImage):
                                      croppedData = croppedImage;
                                      break;
                                    case CropFailure(:final cause):
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: Text('error.error'.tr),
                                          content: Text(
                                              '${"error.crop_image".tr} $cause'),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context),
                                              child: Text('tombol.ok'.tr),
                                            ),
                                          ],
                                        ),
                                      );
                                      break;
                                  }
                                  setState(() => isCropping = false);

                                  if (croppedData != null) {
                                    final croppedFile = File(
                                        '${Directory.systemTemp.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png');
                                    croppedFile.writeAsBytesSync(croppedData!);

                                    // Tutup dialog crop, lanjut ke upload
                                    Navigator.of(context).pop();
                                    _showUploadDialog(croppedFile);
                                  }
                                },
                              ),
                              if (isCropping)
                                Center(child: CircularProgressIndicator()),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() => isCropping = true);
                            cropController.crop();
                          },
                          icon: Icon(
                            Icons.crop,
                            color: Colors.black,
                          ),
                          label: Text("tombol.crop".tr,
                              style: TextStyle(color: Colors.black)),
                        ),
                        const SizedBox(height: 10),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text("tombol.batal".tr,
                              style: TextStyle(color: Colors.black)),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      );
    }
  }

  void _showUploadDialog(File file) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          title: Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Text(
              "image.upload".tr,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 100,
                  child: ClipOval(
                    child: Image.file(
                      file,
                      height: 200,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        pickImage();
                      },
                      icon: Icon(Icons.photo, color: Colors.black),
                      label: Text("tombol.cari_foto".tr,
                          style: TextStyle(color: Colors.black)),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => profileController.isLoading.value
                          ? CircularProgressIndicator()
                          : ElevatedButton.icon(
                              onPressed: () {
                                profileController.uploadPhoto(file);
                              },
                              icon:
                                  Icon(Icons.cloud_upload, color: Colors.black),
                              label: Text("image.upload".tr,
                                  style: TextStyle(color: Colors.black)),
                            ),
                    ),
                  ],
                )
              ],
            ),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(top: 5, bottom: 20),
              child: Center(
                child: SizedBox(
                  height: 25,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text("tombol.batal".tr,
                        style: TextStyle(color: Colors.black)),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  late RhModel rhModel;
  int? idProvince;
  int? idRegency;
  int? idDistrict;
  int? idLocation;

  int? tempIdRegency;
  int? tempIdDistrict;

  String? selectedGender;
  String? selectedMarital;
  Set<String> selectedSIMs = {};
  Map<String, String> simOptions = {
    "A": "SIM A",
    "B1": "SIM B1",
    "B2": "SIM B2",
    "C": "SIM C",
    "D": "SIM D",
    "SIO (Forklift)": "SIO (Forklift)",
    "Tidak Ada": "konfirmasi.tidak_ada".tr,
  };

  final prefs = Get.find<FlutterSecureStorage>();
  String? pin;
  String? nama;
  String? email;
  String? image;
  String? noTelp;

  Future<void> loadPref() async {
    pin = await prefs.read(key: "pin") ?? '';
    nama = await prefs.read(key: "nama") ?? '';
    noTelp = await prefs.read(key: "no_telepon") ?? '';
    image = await prefs.read(key: "image") ?? '';
    email = await prefs.read(key: "email") ?? '';

    rhModel = RhModel(
        id: pin ?? "",
        nama: nama ?? "",
        tempatLahir: "",
        tglLahir: null,
        jenisKelamin: "",
        ktp: "",
        noTelepon: noTelp ?? "",
        email: email ?? "",
        statusPernikahan: "",
        sim: "",
        provinsi: "",
        kota: "",
        kecamatan: "",
        rt: "",
        rw: "",
        alamat: "",
        kodePos: "",
        alamatLengkap: "",
        pendidikanTerakhir: "",
        diploma: "",
        kursus: "",
        pengalamanKerja: "",
        lamaPengalamanKerja: "",
        lamaPosisiKerja: "",
        perjalananDinas: "",
        minatLokasiKerja: "",
        minatGaji: "",
        bahasaAsing: "",
        kelebihan: "",
        kekurangan: "",
        ilmuKomputerisasi: "",
        memimpinTim: "",
        kemampuanPresentasi: "",
        lingkupPekerjaan: "",
        lingkupPekerjaanNama: "",
        organisasi: "",
        tempatLahirFormatted: "",
        tglLahirFormatted: "");

    final args = Get.arguments ?? 'null';

    LogService.log.d("Arguments : $args");

    if (args != null && args.toString().isNotEmpty) {
      LogService.log.d("Arguments is RhModel: ${args is RhModel}");
      if (args is RhModel) {
        LogService.log.d("Arguments is RhModel: $args");
        rhModel = args;
      } else {
        rhModel = RhModel(
            id: pin ?? "",
            nama: nama ?? "",
            tempatLahir: "",
            tglLahir: null,
            jenisKelamin: "",
            ktp: "",
            noTelepon: noTelp ?? "",
            email: email ?? "",
            statusPernikahan: "",
            sim: "",
            provinsi: "",
            kota: "",
            kecamatan: "",
            rt: "",
            rw: "",
            alamat: "",
            kodePos: "",
            alamatLengkap: "",
            pendidikanTerakhir: "",
            diploma: "",
            kursus: "",
            pengalamanKerja: "",
            lamaPengalamanKerja: "",
            lamaPosisiKerja: "",
            perjalananDinas: "",
            minatLokasiKerja: "",
            minatGaji: "",
            bahasaAsing: "",
            kelebihan: "",
            kekurangan: "",
            ilmuKomputerisasi: "",
            memimpinTim: "",
            kemampuanPresentasi: "",
            lingkupPekerjaan: "",
            lingkupPekerjaanNama: "",
            organisasi: "",
            tempatLahirFormatted: "",
            tglLahirFormatted: "");
      }
    }

    _namaLengkapController.text = rhModel.nama ?? '-';
    _tempatLahirController.text = rhModel.tempatLahir ?? '-';
    _tglLahirController.text = DateFormat('yyyy-MM-dd', 'id_ID')
        .format(rhModel.tglLahir ?? DateTime.now());
    selectedDate = rhModel.tglLahir ?? DateTime.now();
    _noKtpController.text = rhModel.ktp ?? '-';
    _noTelpController.text = rhModel.noTelepon!.replaceFirst('62', "");
    initNoHp = _noTelpController.text.replaceFirst('62', "");
    _emailController.text = rhModel.email ?? '-';

    _provinsiController.text = rhModel.provinsi ?? '-';
    _kotaController.text = rhModel.kota ?? '-';
    _kecamatanController.text = rhModel.kecamatan ?? "-";

    _rtController.text = rhModel.rt ?? "-";
    _rwController.text = rhModel.rw ?? "-";
    _alamatTinggalController.text = rhModel.alamat ?? '-';
    _kodePosController.text = rhModel.kodePos ?? '-';
    selectedGender = rhModel.jenisKelamin != "" ? rhModel.jenisKelamin : null;
    _posisiKerjaanController.text = rhModel.minatPosisi ?? "";
    _linkedInController.text = rhModel.linkLinkedin ?? "";
    _tautanController.text = rhModel.linkPortofolio ?? "";
    _instagramController.text = rhModel.akunInstagram ?? "";
    _facebookController.text = rhModel.akunFb ?? "";
    _twitterController.text = rhModel.akunX ?? "";
    _tiktokController.text = rhModel.akunTiktok ?? "";

    // Initialize selectedMarital with proper mapping
    if (rhModel.statusPernikahan != null &&
        rhModel.statusPernikahan!.isNotEmpty) {
      final translatedStatus = TranslationService.translateBetweenLangs(
        rhModel.statusPernikahan ?? '-',
        "status_marital",
        "id",
        Get.locale?.languageCode.toLowerCase() ?? "id",
      );

      // Map the translated status to dropdown items
      final availableStatuses = [
        "status_marital.status3".tr,
        "status_marital.status2".tr,
        "status_marital.status5".tr,
        "status_marital.status6".tr,
      ];

      // Check if translated status matches any available option
      selectedMarital = availableStatuses.contains(translatedStatus)
          ? translatedStatus
          : null;
    } else {
      selectedMarital = null;
    }
    if (rhModel.sim!.isNotEmpty || rhModel.sim!.length > 1) {
      selectedSIMs = rhModel.sim!.split(',').map((e) => e.trim()).toSet();
    }

    loadProvisi();

    setState(() {});
  }

  void loadProvisi() {
    if (_provinsiController.text.isNotEmpty) {
      getIdProvinsi(_provinsiController.text);
    }
  }

  @override
  void initState() {
    super.initState();

    loadPref();
  }

  void getIdProvinsi(String query) async {
    idProvince =
        int.tryParse(await locationController.getIdProvinsi(query, 1)) ?? 0;

    LogService.log.f("temID provisi : $query, $idProvince");

    if (_kotaController.text.isNotEmpty) {
      getIdKota(_kotaController.text);
    }
    setState(() {});
  }

  void getIdKota(String query) async {
    idRegency = int.tryParse(
            await locationController.getIdKota(query, 1, idProvince ?? 0)) ??
        0;
    tempIdRegency = idRegency;

    LogService.log.f("temID kota : $query, $idRegency");

    if (_kecamatanController.text.isNotEmpty) {
      getIdKecamatan(_kecamatanController.text);
    }
    setState(() {});
  }

  void getIdKecamatan(String query) async {
    idDistrict = int.tryParse(
            await locationController.getIdKecaatan(query, 1, idRegency ?? 0)) ??
        0;
    tempIdDistrict = idDistrict;
    LogService.log.f("temID kecamatan : $query, $idDistrict");
    setState(() {});
  }

  void _bukaPilihProvinsi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      ProvinsiBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _provinsiController.text =
          result.nama; // Set text field dengan nama provinsi
      idProvince = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  void _bukaPilihPosisiKerjaan() async {
    final result = await Get.bottomSheet<PosisiPekerjaanModel>(
      PosisiPekerjaanDialog(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _posisiKerjaanController.text = result.text;
      setState(() {});
      // Simpan result ke state
    }
  }

  void _bukaPilihKota(int idProvince) async {
    final result = await Get.bottomSheet<LokasiModel>(
      KotaBottomSheet(idProvince: idProvince),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _kotaController.text = result.nama; // Set text field dengan nama provinsi
      idRegency = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  void _bukaPilihKecamatan(int idRegency) async {
    final result = await Get.bottomSheet<LokasiModel>(
      KecamatanBottomSheet(idRegency: idRegency),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _kecamatanController.text =
          result.nama; // Set text field dengan nama provinsi
      idDistrict = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  void _bukaPilihLokasi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      LokasiBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _tempatLahirController.text =
          result.nama; // Set text field dengan nama provinsi
      idLocation = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  DateTime? selectedDate;

  // Helper method to ensure selectedMarital matches dropdown items
  String? _getValidMaritalStatus() {
    final availableStatuses = [
      "status_marital.status3".tr,
      "status_marital.status2".tr,
      "status_marital.status5".tr,
      "status_marital.status6".tr,
    ];

    // If selectedMarital is null or empty, return null
    if (selectedMarital == null || selectedMarital!.isEmpty) {
      return null;
    }

    // Check if selectedMarital exactly matches one of the available options
    if (availableStatuses.contains(selectedMarital)) {
      return selectedMarital;
    }

    // If no exact match, return null to show hint
    return null;
  }

  Future<void> _selectDate() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 100),
      lastDate: DateTime.now(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );

    if (pickedDate != null) {
      setState(() {
        selectedDate = pickedDate;
        _tglLahirController.text =
            "${selectedDate!.year}-${selectedDate!.month.toString().padLeft(2, '0')}-${selectedDate!.day.toString().padLeft(2, '0')}";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    LogService.log.d("Path: ${Get.parameters['currentRoute']}");
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.info_pribadi.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Stack(
                          children: [
                            GestureDetector(
                              onTap: () {
                                pickImage();
                              },
                              child: Container(
                                width: 100,
                                height: 100,
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle, boxShadow: []),
                                child: CircleAvatar(
                                  radius: 60, // Warna background jika error
                                  child: ClipOval(
                                    child: Obx(
                                      () => (isValidImageUrl(profileController
                                                  .rxImage.value) &&
                                              profileController
                                                  .rxImage.value.isNotEmpty)
                                          ? Image.network(
                                              profileController.rxImage.value,
                                              fit: BoxFit.cover,
                                              width: 100,
                                              height: 100,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              ImageAssets.logo,
                                              fit: BoxFit.cover,
                                              width: 100,
                                              height: 100,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.person,
                                                  size: 20,
                                                );
                                              },
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: GestureDetector(
                                onTap: () {
                                  pickImage();
                                },
                                child: Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color:
                                        const Color.fromARGB(255, 24, 82, 154),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      _buildTextField(
                          "dcv.info_pribadi.txt_nama".tr,
                          "dcv.info_pribadi.txt_nama".tr,
                          errorsMessage[0]['isError']
                              ? errorsMessage[0]['message']
                              : "",
                          _namaLengkapController,
                          TextInputType.name,
                          null,
                          false,
                          null,
                          true),
                      if (Get.parameters['currentRoute'] == "")
                        buildFormRH()
                      else
                        buildFormCVATS()
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(12),
              child: SizedBox(
                height: 50,
                child: Obx(
                  () => formController.isLoading.value
                      ? Center(child: CircularProgressIndicator())
                      : ElevatedButton(
                          onPressed: () async {
                            if (Get.parameters['currentRoute'] == "") {
                              simpanRH();
                            } else {
                              simpanCVATS();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 100,
                                vertical: 12,
                              ),
                              minimumSize: const Size.fromHeight(45),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50),
                              ),
                              textStyle: TextStyle(fontSize: 14)),
                          child: Text(
                            "tombol.simpan".tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String title,
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly,
      String? type,
      bool? isRequired) {
    return isReadOnly
        ? FormTextFiled(
            title: title,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator,
            minLines: 1,
            maxLines: null,
            type: type,
          )
        : FormTextFiled(
            title: title,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: isRequired ?? true,
            type: type,
            onCountryCodeChanged: (value) {
              if (type == 'phone') {
                setState(() {
                  countryCode = value;
                });
              }
            },
            selectedCountryCode: countryCode,
          );
  }

  Widget buildFormCVATS() {
    return Column(
      children: [
        SizedBox(
          height: 15,
        ),
        FormTextFiled(
          title: "dcv.info_pribadi.posisi_kerjaan".tr,
          controller: _posisiKerjaanController,
          onTap: () {
            _bukaPilihPosisiKerjaan();
          },
          isRequired: true,
          keyboardType: TextInputType.text,
          isReadOnly: true,
          maxLines: 1,
          hintText: "dcv.info_pribadi.posisi_kerjaan".tr,
          suffixIcon: Icon(
            Icons.arrow_drop_down,
            size: 30,
          ),
          errorText: errorsMessage[13]['isError']
              ? errorsMessage[13]['message']
              : null,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_handphone".tr,
          "dcv.info_pribadi.txt_handphone".tr,
          errorsMessage[4]['isError'] ? errorsMessage[4]['message'] : "",
          _noTelpController,
          TextInputType.number,
          null,
          false,
          'phone',
          true,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_email".tr,
          "dcv.info_pribadi.txt_email".tr,
          errorsMessage[5]['isError'] ? errorsMessage[5]['message'] : "",
          _emailController,
          TextInputType.emailAddress,
          null,
          false,
          'email',
          true,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_alamat_tinggal".tr,
          "dcv.info_pribadi.txt_alamat_tinggal".tr,
          errorsMessage[11]['isError'] ? errorsMessage[11]['message'] : "",
          _alamatTinggalController,
          TextInputType.multiline,
          null,
          false,
          null,
          true,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_linkedIn".tr,
          "dcv.info_pribadi.txt_linkedIn".tr,
          "",
          _linkedInController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_tautan_portfolio".tr,
          "dcv.info_pribadi.txt_tautan_portfolio".tr,
          "",
          _tautanController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_instagram".tr,
          "dcv.info_pribadi.txt_instagram".tr,
          "",
          _instagramController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_twitter".tr,
          "dcv.info_pribadi.txt_twitter".tr,
          "",
          _twitterController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_facebook".tr,
          "dcv.info_pribadi.txt_facebook".tr,
          "",
          _facebookController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_tiktok".tr,
          "dcv.info_pribadi.txt_tiktok".tr,
          "",
          _tiktokController,
          TextInputType.text,
          null,
          false,
          null,
          false,
        ),
      ],
    );
  }

  Widget buildFormRH() {
    return Column(
      children: [
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "dcv.info_pribadi.txt_tempat".tr,
                        style: TextStyle(fontSize: 12),
                      ),
                      Text(
                        "*",
                        style: TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  FormTextFiled(
                    controller: _tempatLahirController,
                    onTap: () {
                      _bukaPilihLokasi();
                    },
                    keyboardType: TextInputType.text,
                    isReadOnly: true,
                    hintText: "dcv.info_pribadi.txt_tempat".tr,
                    maxLines: 1,
                    suffixIcon: Icon(
                      Icons.arrow_drop_down,
                      size: 30,
                    ),
                    errorText: errorsMessage[1]['isError']
                        ? errorsMessage[1]['message']
                        : null,
                  )
                ],
              ),
            ),
            SizedBox(width: 16), // Jarak antar kolom
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "dcv.info_pribadi.txt_lahir".tr,
                        style: TextStyle(fontSize: 12),
                      ),
                      Text(
                        "*",
                        style: TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  FormTextFiled(
                    controller: _tglLahirController,
                    onTap: () {
                      _selectDate();
                    },
                    keyboardType: TextInputType.text,
                    isReadOnly: true,
                    maxLines: 1,
                    hintText: "dcv.info_pribadi.txt_lahir".tr,
                    suffixIcon: Icon(
                      Icons.calendar_month_outlined,
                      size: 30,
                      color: Colors.grey[500],
                    ),
                    errorText: errorsMessage[2]['isError']
                        ? errorsMessage[2]['message']
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "dcv.info_pribadi.txt_jk".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FormField<String>(
              key: _radioKey,
              validator: (value) {
                if (selectedGender == null) {
                  return "dcv.info_pribadi.txt_jk_blank".tr;
                }
                return null;
              },
              builder: (FormFieldState<String> field) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Radio<String>(
                          value: "Laki-Laki",
                          groupValue: selectedGender,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                          onChanged: (String? value) {
                            setState(() {
                              selectedGender = value;
                              field.didChange(
                                  value); // agar validator tahu ada perubahan
                            });
                          },
                        ),
                        Text(
                          "dcv.info_pribadi.txt_lk".tr,
                          style: const TextStyle(fontSize: 12),
                        ),
                        const SizedBox(width: 16),
                        Radio<String>(
                          value: "Perempuan",
                          groupValue: selectedGender,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                          onChanged: (String? value) {
                            setState(() {
                              selectedGender = value;
                              field.didChange(
                                  value); // penting: trigger validator
                            });
                          },
                        ),
                        Text(
                          "dcv.info_pribadi.txt_pr".tr,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                    if (field.hasError)
                      Padding(
                        padding: const EdgeInsets.only(left: 12.0, top: 4.0),
                        child: Text(
                          field.errorText ?? '',
                          style:
                              const TextStyle(color: Colors.red, fontSize: 12),
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_handphone".tr,
          "dcv.info_pribadi.txt_handphone".tr,
          errorsMessage[4]['isError'] ? errorsMessage[4]['message'] : "",
          _noTelpController,
          TextInputType.number,
          null,
          false,
          'phone',
          true,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_email".tr,
          "dcv.info_pribadi.txt_email".tr,
          errorsMessage[5]['isError'] ? errorsMessage[5]['message'] : "",
          _emailController,
          TextInputType.emailAddress,
          null,
          false,
          'email',
          true,
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_pernikahan".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: Colors.grey[200],
          ),
          child: DropdownButtonFormField<String>(
            value: _getValidMaritalStatus(),
            hint: Text(
              "dcv.info_pribadi.txt_pernikahan".tr,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            isDense: true, // Mengurangi padding internal
            isExpanded: true, // Membuat dropdown mengisi lebar penuh
            decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                  vertical: 10, horizontal: 12), // Padding dalam Input
              border: OutlineInputBorder(
                // Tambahkan border jika perlu
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide.none,
              ),
            ),
            style: TextStyle(
              fontSize: 14,
              color: Colors.black,
            ), // Ukuran teks dropdown
            iconSize: 24, // Ukuran icon dropdown
            onChanged: (String? newValue) {
              setState(() {
                selectedMarital = newValue;
              });
            },
            items: [
              "status_marital.status3".tr,
              "status_marital.status2".tr,
              "status_marital.status5".tr,
              "status_marital.status6".tr,
            ].map<DropdownMenuItem<String>>((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Container(
                  height: 40, // Mengatur tinggi dropdown item
                  alignment: Alignment.centerLeft, // Menyamakan posisi teks
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_sim".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        FormField<List<String>>(
          key: _checkboxKey,
          initialValue: selectedSIMs.toList(),
          validator: (value) {
            if (selectedSIMs.isEmpty) {
              return "dcv.info_pribadi.txt_sim_blank".tr;
            }
            return null;
          },
          builder: (FormFieldState<List<String>> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Wrap(
                    alignment: WrapAlignment.start,
                    spacing: 10,
                    runSpacing: 4,
                    children: simOptions.keys.map((key) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Checkbox(
                            value: selectedSIMs.contains(key),
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  selectedSIMs.add(key);
                                } else {
                                  selectedSIMs.remove(key);
                                }
                                // Trigger perubahan agar validator aktif
                                field.didChange(selectedSIMs.toList());
                              });
                            },
                            visualDensity: VisualDensity.compact,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                          Text(
                            simOptions[key]!,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
                if (field.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      field.errorText ?? '',
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            );
          },
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_alamat".tr,
              style: TextStyle(
                fontSize: 16,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        Divider(
          color: Colors.grey[300],
          thickness: 1,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_provinsi".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        FormTextFiled(
          controller: _provinsiController,
          onTap: () {
            _bukaPilihProvinsi();
          },
          keyboardType: TextInputType.text,
          isReadOnly: true,
          hintText: "dcv.info_pribadi.txt_provinsi".tr,
          maxLines: 1,
          suffixIcon: Icon(
            Icons.arrow_drop_down,
            size: 30,
          ),
          errorText:
              errorsMessage[6]['isError'] ? errorsMessage[6]['message'] : null,
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_kota".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        FormTextFiled(
          controller: _kotaController,
          onTap: () {
            if (idProvince != null) {
              _bukaPilihKota(idProvince!);
            } else {
              showAnimatedSnackbarWarning(
                  context, "dcv.info_pribadi.txt_provinsi_blank2".tr);
            }
          },
          keyboardType: TextInputType.text,
          isReadOnly: true,
          maxLines: 1,
          hintText: "dcv.info_pribadi.txt_kota".tr,
          suffixIcon: Icon(
            Icons.arrow_drop_down,
            size: 30,
          ),
          errorText:
              errorsMessage[7]['isError'] ? errorsMessage[7]['message'] : null,
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(
              "dcv.info_pribadi.txt_kecamatan".tr,
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              "*",
              style: TextStyle(
                color: Colors.red,
              ),
            )
          ],
        ),
        FormTextFiled(
          controller: _kecamatanController,
          onTap: () {
            if (idRegency != null) {
              _bukaPilihKecamatan(idRegency!);
            } else {
              showAnimatedSnackbarWarning(
                  context, "dcv.info_pribadi.txt_kota_blank2".tr);
            }
          },
          keyboardType: TextInputType.text,
          isReadOnly: true,
          maxLines: 1,
          hintText: "dcv.info_pribadi.txt_kecamatan".tr,
          suffixIcon: Icon(
            Icons.arrow_drop_down,
            size: 30,
          ),
          errorText:
              errorsMessage[8]['isError'] ? errorsMessage[8]['message'] : null,
        ),
        SizedBox(
          height: 15,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextField(
                    "dcv.info_pribadi.txt_rt".tr,
                    "dcv.info_pribadi.txt_rt".tr,
                    errorsMessage[9]['isError']
                        ? errorsMessage[9]['message']
                        : "",
                    _rtController,
                    TextInputType.number,
                    null,
                    false,
                    null,
                    true,
                  ),
                ],
              ),
            ),
            SizedBox(width: 15), // Jarak antar kolom
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextField(
                    "dcv.info_pribadi.txt_rw".tr,
                    "dcv.info_pribadi.txt_rw".tr,
                    errorsMessage[10]['isError']
                        ? errorsMessage[10]['message']
                        : "",
                    _rwController,
                    TextInputType.number,
                    null,
                    false,
                    null,
                    true,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_alamat_tinggal".tr,
          "dcv.info_pribadi.txt_alamat_tinggal".tr,
          errorsMessage[11]['isError'] ? errorsMessage[11]['message'] : "",
          _alamatTinggalController,
          TextInputType.multiline,
          null,
          false,
          null,
          true,
        ),
        SizedBox(
          height: 15,
        ),
        _buildTextField(
          "dcv.info_pribadi.txt_kode_pos".tr,
          "dcv.info_pribadi.txt_kode_pos".tr,
          errorsMessage[12]['isError'] ? errorsMessage[12]['message'] : "",
          _kodePosController,
          TextInputType.number,
          null,
          false,
          null,
          true,
        ),
        SizedBox(
          height: 15,
        ),
      ],
    );
  }

  void simpanRH() async {
    if (_namaLengkapController.text.isEmpty) {
      setState(() {
        errorsMessage[0]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[0]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_tempatLahirController.text.isEmpty) {
      setState(() {
        errorsMessage[1]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[1]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_tglLahirController.text.isEmpty) {
      setState(() {
        errorsMessage[2]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[2]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_noTelpController.text.isEmpty) {
      setState(() {
        errorsMessage[4]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[4]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_emailController.text.isEmpty) {
      setState(() {
        errorsMessage[5]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[5]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (selectedGender == null) {
      Get.snackbar("", "dcv.info_pribadi.txt_marital_blank".tr,
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (selectedMarital == null) {
      Get.snackbar("", "dcv.info_pribadi.txt_marital_blank2".tr,
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (selectedSIMs.isEmpty) {
      Get.snackbar("", "dcv.info_pribadi.txt_sim_blank".tr,
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));

      return;
    }
    if (_provinsiController.text.isEmpty || idProvince == null) {
      setState(() {
        errorsMessage[6]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[6]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_kotaController.text.isEmpty || idRegency == null) {
      setState(() {
        errorsMessage[7]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[7]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_kecamatanController.text.isEmpty || idDistrict == null) {
      setState(() {
        errorsMessage[8]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[8]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_rtController.text.isEmpty) {
      setState(() {
        errorsMessage[9]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[9]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_rwController.text.isEmpty) {
      setState(() {
        errorsMessage[10]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[10]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_alamatTinggalController.text.isEmpty) {
      setState(() {
        errorsMessage[11]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[11]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_kodePosController.text.isEmpty) {
      setState(() {
        errorsMessage[12]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[12]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }

    data.clear();

    var noTelp = _noTelpController.text;
    LogService.log.i("no HP : $initNoHp, ${_noTelpController.text}");
    if (_noTelpController.text != initNoHp) {
      String input = _noTelpController.text;
      if (input.startsWith('0')) {
        input = input.substring(1);
      }
      noTelp = (countryCode + input).replaceAll('+', "");
    }

    data.add({
      "nama": _namaLengkapController.text,
      "tempat_lahir": _tempatLahirController.text,
      "tgl_lahir": _tglLahirController.text,
      "jk": selectedGender,
      "status_pernikahan": TranslationService.translateBetweenLangs(
        selectedMarital ?? "Belum Menikah",
        "status_marital",
        Get.locale?.languageCode.toLowerCase() ?? "id",
        "id",
      ),
      "ktp": _noKtpController.text,
      "no_telepon": noTelp,
      "email": _emailController.text,
      "sim": selectedSIMs.toList(),
      "provinsi": idProvince,
      "kota_tinggal": idRegency == 0 ? tempIdRegency : idRegency,
      "kec_tinggal": idDistrict == 0 ? tempIdDistrict : idDistrict,
      "rt_tinggal": _rtController.text,
      "rw_tinggal": _rwController.text,
      "alamat_tinggal": _alamatTinggalController.text,
      "pos_tinggal": _kodePosController.text,
    });
    final result = await formController.saveInfoPribadi(data);
    if (result) {
      await prefs.write(
          key: "alamat",
          value:
              "${_alamatTinggalController.text} RT ${_rtController.text} RW ${_rwController.text} ${_kecamatanController.text} ${_kotaController.text} ${_provinsiController.text}. ${_kodePosController.text}");
      await prefs.write(key: "email", value: _emailController.text);
      await prefs.write(key: "no_telp", value: noTelp);
      await prefs.write(
          key: "tempat_lahir", value: _tempatLahirController.text);
      await prefs.write(key: "tgl_lahir", value: _tglLahirController.text);
      profileController.rxEmail.value = _emailController.text;
      profileController.noTelp.value = noTelp;
    }
  }

  void simpanCVATS() async {
    if (_namaLengkapController.text.isEmpty) {
      setState(() {
        errorsMessage[0]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[0]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_posisiKerjaanController.text.isEmpty) {
      setState(() {
        errorsMessage[13]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[13]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_noTelpController.text.isEmpty) {
      setState(() {
        errorsMessage[4]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[4]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_emailController.text.isEmpty) {
      setState(() {
        errorsMessage[5]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[5]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }
    if (_alamatTinggalController.text.isEmpty) {
      setState(() {
        errorsMessage[11]['isError'] = true;
      });
      Get.snackbar("", errorsMessage[11]['message'],
          snackPosition: SnackPosition.BOTTOM,
          borderRadius: 10,
          margin: EdgeInsets.only(left: 15, right: 15, bottom: 70),
          backgroundColor: Colors.amber,
          duration: const Duration(seconds: 4),
          animationDuration: const Duration(milliseconds: 500),
          forwardAnimationCurve: Curves.easeOutBack,
          reverseAnimationCurve: Curves.easeInBack,
          colorText: Colors.black,
          titleText: SizedBox.shrink(),
          icon: Icon(
            Icons.warning,
            color: Colors.black,
          ),
          mainButton: TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.black),
            ),
          ));
      return;
    }

    data.clear();

    var noTelp = _noTelpController.text;
    LogService.log.i("no HP : $initNoHp, ${_noTelpController.text}");
    if (_noTelpController.text != initNoHp) {
      String input = _noTelpController.text;
      if (input.startsWith('0')) {
        input = input.substring(1);
      }
      noTelp = (countryCode + input).replaceAll('+', "");
    }

    data.add({
      "nama": _namaLengkapController.text,
      "minat_posisi": _posisiKerjaanController.text,
      "no_telepon": noTelp,
      "email": _emailController.text,
      "alamat_tinggal": _alamatTinggalController.text,
      "linkLinkedin": _linkedInController.text,
      "linkPortofolio": _tautanController.text,
      "akun_instagram": _instagramController.text,
      "akun_fb": _facebookController.text,
      "akun_x": _twitterController.text,
      "akun_tiktok": _tiktokController.text,
    });
    final result = await formController.saveDataCV(data);
    if (result) {
      await prefs.write(key: "email", value: _emailController.text);
      await prefs.write(key: "no_telp", value: noTelp);
      profileController.rxEmail.value = _emailController.text;
      profileController.noTelp.value = noTelp;
    }
  }
}
