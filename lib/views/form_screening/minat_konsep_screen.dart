import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/provinsi_dialog.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/bahasa_model.dart';
import 'package:digital_cv_mobile/models/penguasaan_bahasa_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MinatKonsepScreen extends StatefulWidget {
  const MinatKonsepScreen({super.key});

  @override
  State<MinatKonsepScreen> createState() => _MinatKonsepScreenState();
}

class _MinatKonsepScreenState extends State<MinatKonsepScreen> {
  final MinatKonsepController minatKonsepController =
      Get.put(MinatKonsepController());
  final FormController formController = Get.find<FormController>();

  List<Map<String, dynamic>> data = [];
  final List<Map<String, dynamic>> errorsMessage = [
    {"message": "dcv.minat.txt_kelebihan_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_kekurangan_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_bahasa_dikuasai_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_baca_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_menulis_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_mendengar_blank".tr, "isError": false},
    {"message": "dcv.minat.txt_bicara_blank".tr, "isError": false}
  ];

  Map<String, String> penilaianBahasa = {
    "BS": "bahasa.penilaian.bs".tr,
    "B": "bahasa.penilaian.b".tr,
    "C": "bahasa.penilaian.c".tr,
    "K": "bahasa.penilaian.k".tr,
    "KS": "bahasa.penilaian.ks".tr,
  };

  @override
  void initState() {
    super.initState();

    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;
      if (args['bahasa'] != null) {
        if ((args['bahasa'] as List).isNotEmpty) {
          minatKonsepController.bahasaAsing =
              (args['bahasa'] as List<PenguasaanBahasaModel>).obs;
          minatKonsepController.selectedValue.value = "Ya";
        } else {
          minatKonsepController.bahasaAsing = RxList<PenguasaanBahasaModel>();
          minatKonsepController.selectedValue.value = "Tidak";
        }
        LogService.log.i("Bahasa: ${args['bahasa']}");
      } else {
        minatKonsepController.bahasaAsing = RxList<PenguasaanBahasaModel>();
        minatKonsepController.selectedValue.value = "Tidak";
      }

      if (args['kelebihan'] is List<String> &&
          (args['kelebihan'] as List<String>).isNotEmpty) {
        minatKonsepController.listKelebihan =
            (args['kelebihan'] as List<String>).obs;
      } else {
        minatKonsepController.listKelebihan = <String>[].obs;
      }

      if (args['kekurangan'] is List<String> &&
          (args['kekurangan'] as List<String>).isNotEmpty) {
        minatKonsepController.listKekurangan =
            (args['kekurangan'] as List<String>).obs;
      } else {
        minatKonsepController.listKekurangan = <String>[].obs;
      }

      if (args['komputerisasi'] != null && args['komputerisasi'] != "") {
        try {
          // Asumsikan 'komputerisasi' hanya kirim 1 id (karena radio button)
          final dynamic rawValue = args['komputerisasi'];

          // Jika list, ambil elemen pertama
          final id = rawValue is List && rawValue.isNotEmpty
              ? int.tryParse(rawValue.first.toString())
              : int.tryParse(rawValue.toString());

          if (id != null) {
            minatKonsepController.selectedKomputerisasiId.value = id;
          }
        } catch (e) {
          LogService.log.e("Error parsing komputerisasi arg: $e");
          minatKonsepController.selectedKomputerisasiId.value = -1;
        }
      } else {
        minatKonsepController.selectedKomputerisasiId.value = -1;
      }

      if (args['memimpin'] != null && args['memimpin'] != "") {
        // Ambil nilai penilaian dari args
        String penilaianValue = args['memimpin'];

        // Tentukan index dari penilaian yang dipilih
        int index =
            int.tryParse(penilaianValue) ?? 0; // Mengonversi menjadi integer

        // Pastikan index sesuai dengan opsi yang tersedia (1, 2, 3)
        if (index >= 1 &&
            index <= minatKonsepController.jmlOrangOptions.length) {
          // Set value pada controller sesuai dengan opsi yang dipilih
          minatKonsepController.selectedjmlOrangOption.value =
              minatKonsepController.jmlOrangOptions[index - 1];
        } else {
          // Jika nilai tidak valid, set ke nilai default (kosong)
          minatKonsepController.selectedjmlOrangOption.value = "";
        }
      } else {
        minatKonsepController.selectedjmlOrangOption.value = "";
      }

      if (args['penilaian'] != null && args['penilaian'] != "") {
        // Ambil nilai penilaian dari args
        String penilaianValue = args['penilaian'];

        // Tentukan index dari penilaian yang dipilih
        int index =
            int.tryParse(penilaianValue) ?? 0; // Mengonversi menjadi integer

        // Pastikan index sesuai dengan opsi yang tersedia (1, 2, 3)
        if (index >= 1 &&
            index <= minatKonsepController.penilaianOptions.length) {
          // Set value pada controller sesuai dengan opsi yang dipilih
          minatKonsepController.selectedjPenilaianOption.value =
              minatKonsepController.penilaianOptions[index - 1];
        } else {
          // Jika nilai tidak valid, set ke nilai default (kosong)
          minatKonsepController.selectedjPenilaianOption.value = "";
        }
      } else {
        minatKonsepController.selectedjPenilaianOption.value = "";
      }

      List<int> selectedList = [];
      if (args['lingkup'] != null && args['lingkup'] is List) {
        selectedList = (args['lingkup'] as List)
            .map((e) => int.tryParse(e.toString()))
            .whereType<int>()
            .toList();
      }

      // Panggil getRuangLingkup dengan selectedList
      minatKonsepController.getRuangLingkup(preselected: selectedList);
    } else {
      minatKonsepController.getRuangLingkup();
    }
  }

  void _bukaPilihBahasa() async {
    final result = await Get.bottomSheet<BahasaModel>(
      BahasaBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      LogService.log.i("Bahasa yang dipilih: ${result.text}");
      minatKonsepController.bahasaAsingController.text =
          result.text; // Set text field dengan nama bahasa
      setState(() {});
      // Simpan result ke state
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.minat.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Text(
                            "dcv.minat.txt_konfirm_bahasa".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            "*",
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          )
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Radio<String>(
                                value: "Ya",
                                groupValue:
                                    minatKonsepController.selectedValue.value,
                                materialTapTargetSize: MaterialTapTargetSize
                                    .shrinkWrap, // Hilangkan padding default
                                visualDensity:
                                    VisualDensity.compact, // Lebih kecil lagi
                                onChanged: (String? value) {
                                  setState(() {
                                    minatKonsepController.selectedValue.value =
                                        value!;
                                  });
                                },
                              ),
                              Text(
                                'konfirmasi2.iya'.tr,
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                          SizedBox(width: 16), // Beri jarak antar radio button
                          Row(
                            children: [
                              Radio<String>(
                                value: "Tidak",
                                groupValue:
                                    minatKonsepController.selectedValue.value,
                                materialTapTargetSize: MaterialTapTargetSize
                                    .shrinkWrap, // Hilangkan padding default
                                visualDensity:
                                    VisualDensity.compact, // Lebih kecil lagi
                                onChanged: (String? value) {
                                  setState(() {
                                    minatKonsepController.selectedValue.value =
                                        value!;
                                    minatKonsepController.bahasaAsing.clear();
                                  });
                                },
                              ),
                              Text(
                                'konfirmasi2.tidak'.tr,
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Obx(
                        () => minatKonsepController.selectedValue.value == "Ya"
                            ? Column(
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.minat.txt_bahasa_dikuasai".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 7,
                                        child: FormTextFiled(
                                          controller: minatKonsepController
                                              .bahasaAsingController,
                                          hintText:
                                              "dcv.minat.txt_bahasa_dikuasai"
                                                  .tr,
                                          keyboardType: TextInputType.text,
                                          errorText: errorsMessage[2]['isError']
                                              ? errorsMessage[2]['message']
                                              : null,
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Material(
                                          color: Colors.white,
                                          child: InkWell(
                                            onTap: () {
                                              if (minatKonsepController
                                                  .bahasaAsingController
                                                  .text
                                                  .isEmpty) {
                                                Get.snackbar(
                                                  "",
                                                  "dcv.minat.txt_bahasa_dikuasai_blank"
                                                      .tr,
                                                  snackPosition:
                                                      SnackPosition.TOP,
                                                  backgroundColor: Colors.amber,
                                                  colorText: Colors.black,
                                                  titleText: SizedBox.shrink(),
                                                  icon: Icon(
                                                    Icons.warning,
                                                    color: Colors.black,
                                                  ),
                                                );
                                                return;
                                              }
                                              if (minatKonsepController
                                                  .selectedBaca.value.isEmpty) {
                                                Get.snackbar(
                                                  "",
                                                  "dcv.minat.txt_baca_blank"
                                                      .tr
                                                      .tr,
                                                  snackPosition:
                                                      SnackPosition.TOP,
                                                  backgroundColor: Colors.amber,
                                                  colorText: Colors.black,
                                                  titleText: SizedBox.shrink(),
                                                  icon: Icon(
                                                    Icons.warning,
                                                    color: Colors.black,
                                                  ),
                                                );
                                                return;
                                              }
                                              if (minatKonsepController
                                                  .selectedDengar
                                                  .value
                                                  .isEmpty) {
                                                Get.snackbar(
                                                  "",
                                                  "dcv.minat.txt_mendengar_blank"
                                                      .tr,
                                                  snackPosition:
                                                      SnackPosition.TOP,
                                                  backgroundColor: Colors.amber,
                                                  colorText: Colors.black,
                                                  titleText: SizedBox.shrink(),
                                                  icon: Icon(
                                                    Icons.warning,
                                                    color: Colors.black,
                                                  ),
                                                );
                                                return;
                                              }
                                              if (minatKonsepController
                                                  .selectedTulis
                                                  .value
                                                  .isEmpty) {
                                                Get.snackbar(
                                                  "",
                                                  "dcv.minat.txt_menulis_blank"
                                                      .tr,
                                                  snackPosition:
                                                      SnackPosition.TOP,
                                                  backgroundColor: Colors.amber,
                                                  colorText: Colors.black,
                                                  titleText: SizedBox.shrink(),
                                                  icon: Icon(
                                                    Icons.warning,
                                                    color: Colors.black,
                                                  ),
                                                );
                                                return;
                                              }
                                              if (minatKonsepController
                                                  .selectedBicara
                                                  .value
                                                  .isEmpty) {
                                                Get.snackbar(
                                                  "",
                                                  "dcv.minat.txt_bicara_blank"
                                                      .tr,
                                                  snackPosition:
                                                      SnackPosition.TOP,
                                                  backgroundColor: Colors.amber,
                                                  colorText: Colors.black,
                                                  titleText: SizedBox.shrink(),
                                                  icon: Icon(
                                                    Icons.warning,
                                                    color: Colors.black,
                                                  ),
                                                );
                                                return;
                                              }
                                              minatKonsepController.addBahasa();
                                            },
                                            child: Icon(
                                              Icons.add_circle,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 5),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.minat.txt_baca".tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: Colors.grey[200],
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              child: DropdownButtonFormField<
                                                  String>(
                                                value: minatKonsepController
                                                        .selectedBaca
                                                        .value
                                                        .isNotEmpty
                                                    ? minatKonsepController
                                                        .selectedBaca.value
                                                    : null,
                                                isDense: true,
                                                isExpanded: true,
                                                decoration: InputDecoration(
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 12),
                                                    border: InputBorder.none,
                                                    hintText:
                                                        "dcv.minat.txt_baca"
                                                            .tr),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black,
                                                ),
                                                iconSize: 24,
                                                onChanged: (String? newValue) {
                                                  setState(() {
                                                    minatKonsepController
                                                        .selectedBaca
                                                        .value = newValue!;
                                                  });
                                                },
                                                items: penilaianBahasa.entries
                                                    .map<
                                                        DropdownMenuItem<
                                                            String>>((entry) {
                                                  return DropdownMenuItem<
                                                      String>(
                                                    value: entry.key,
                                                    child: Container(
                                                      height: 40,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Text(
                                                        entry
                                                            .value, // Tampilkan label user-friendly
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 15), // Jarak antar kolom
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.minat.txt_menulis".tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              height: 50,
                                              decoration: BoxDecoration(
                                                  color: Colors.grey[200],
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              child: DropdownButtonFormField<
                                                  String>(
                                                value: minatKonsepController
                                                        .selectedTulis
                                                        .value
                                                        .isNotEmpty
                                                    ? minatKonsepController
                                                        .selectedTulis.value
                                                    : null,
                                                isDense: true,
                                                isExpanded: true,
                                                decoration: InputDecoration(
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 12),
                                                    border: InputBorder.none,
                                                    hintText:
                                                        "dcv.minat.txt_menulis"
                                                            .tr),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black,
                                                ),
                                                iconSize: 24,
                                                onChanged: (String? newValue) {
                                                  setState(() {
                                                    minatKonsepController
                                                        .selectedTulis
                                                        .value = newValue!;
                                                  });
                                                },
                                                items: penilaianBahasa.entries
                                                    .map<
                                                        DropdownMenuItem<
                                                            String>>((entry) {
                                                  return DropdownMenuItem<
                                                      String>(
                                                    value: entry.key,
                                                    child: Container(
                                                      height: 40,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Text(
                                                        entry
                                                            .value, // Tampilkan label user-friendly
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 5),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.minat.txt_mendengar".tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              height: 50,
                                              decoration: BoxDecoration(
                                                  color: Colors.grey[200],
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              child: DropdownButtonFormField<
                                                  String>(
                                                value: minatKonsepController
                                                        .selectedDengar
                                                        .value
                                                        .isNotEmpty
                                                    ? minatKonsepController
                                                        .selectedDengar.value
                                                    : null,
                                                isDense: true,
                                                isExpanded: true,
                                                decoration: InputDecoration(
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 12),
                                                    border: InputBorder.none,
                                                    hintText:
                                                        "dcv.minat.txt_mendengar"
                                                            .tr),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black,
                                                ),
                                                iconSize: 24,
                                                onChanged: (String? newValue) {
                                                  setState(() {
                                                    minatKonsepController
                                                        .selectedDengar
                                                        .value = newValue!;
                                                  });
                                                },
                                                items: penilaianBahasa.entries
                                                    .map<
                                                        DropdownMenuItem<
                                                            String>>((entry) {
                                                  return DropdownMenuItem<
                                                      String>(
                                                    value: entry.key,
                                                    child: Container(
                                                      height: 40,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Text(
                                                        entry
                                                            .value, // Tampilkan label user-friendly
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 15), // Jarak antar kolom
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.minat.txt_bicara".tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              height: 50,
                                              decoration: BoxDecoration(
                                                  color: Colors.grey[200],
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              child: DropdownButtonFormField<
                                                  String>(
                                                value: minatKonsepController
                                                        .selectedBicara
                                                        .value
                                                        .isNotEmpty
                                                    ? minatKonsepController
                                                        .selectedBicara.value
                                                    : null,
                                                isDense: true,
                                                isExpanded: true,
                                                decoration: InputDecoration(
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 12),
                                                    border: InputBorder.none,
                                                    hintText:
                                                        "dcv.minat.txt_bicara"
                                                            .tr),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black,
                                                ),
                                                iconSize: 24,
                                                onChanged: (String? newValue) {
                                                  setState(() {
                                                    minatKonsepController
                                                        .selectedBicara
                                                        .value = newValue!;
                                                  });
                                                },
                                                items: penilaianBahasa.entries
                                                    .map<
                                                        DropdownMenuItem<
                                                            String>>((entry) {
                                                  return DropdownMenuItem<
                                                      String>(
                                                    value: entry.key,
                                                    child: Container(
                                                      height: 40,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Text(
                                                        entry
                                                            .value, // Tampilkan label user-friendly
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    children: [
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Container(
                                        alignment: Alignment.centerLeft,
                                        child: Obx(
                                          () => Wrap(
                                            alignment: WrapAlignment.start,
                                            crossAxisAlignment:
                                                WrapCrossAlignment.start,
                                            spacing: 8,
                                            runSpacing: 8,
                                            children: [
                                              for (var item
                                                  in minatKonsepController
                                                      .bahasaAsing)
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        border: Border.all(
                                                          color: ColorAsset
                                                              .primaryColor,
                                                          width: 0.5,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(6),
                                                      ),
                                                      padding:
                                                          const EdgeInsets.all(
                                                              5),
                                                      child: Column(
                                                        mainAxisSize: MainAxisSize
                                                            .min, // ⬅️ Kunci: supaya tinggi dan lebar secukupnya
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(item.bahasa,
                                                              style: const TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold)),
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text(
                                                                  "${"dcv.minat.txt_baca".tr}: ",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                              Text(
                                                                  penilaianBahasa[item
                                                                          .membaca] ??
                                                                      item
                                                                          .membaca,
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                            ],
                                                          ),
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text(
                                                                  "${"dcv.minat.txt_menulis".tr}: ",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                              Text(
                                                                  penilaianBahasa[item
                                                                          .menulis] ??
                                                                      item
                                                                          .menulis,
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                            ],
                                                          ),
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text(
                                                                  "${"dcv.minat.txt_mendengar".tr}: ",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                              Text(
                                                                  penilaianBahasa[item
                                                                          .mendengar] ??
                                                                      item
                                                                          .mendengar,
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                            ],
                                                          ),
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text(
                                                                  "${"dcv.minat.txt_bicara".tr}: ",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                              Text(
                                                                  penilaianBahasa[item
                                                                          .berbicara] ??
                                                                      item
                                                                          .berbicara,
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          11)),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    InkWell(
                                                        onTap: () {
                                                          minatKonsepController
                                                              .deleteBahasa(
                                                            minatKonsepController
                                                                .bahasaAsing
                                                                .indexOf(item),
                                                          );
                                                        },
                                                        child: Icon(
                                                          Icons.close_rounded,
                                                          color: Colors.red,
                                                        ))
                                                  ],
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                ],
                              )
                            : SizedBox(),
                      ),
                      Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: _buildTextField(
                              "dcv.minat.txt_kelebihan".tr,
                              errorsMessage[0]['isError']
                                  ? errorsMessage[0]['message']
                                  : '',
                              minatKonsepController.kelebihanController,
                              TextInputType.text,
                              null,
                              false,
                            ),
                          ),
                          Container(
                            width: 50,
                            height: 50,
                            margin: EdgeInsets.only(top: 26),
                            alignment: Alignment.center,
                            child: Material(
                              color: Colors.white,
                              child: InkWell(
                                onTap: () {
                                  if (minatKonsepController
                                      .kelebihanController.text.isNotEmpty) {
                                    minatKonsepController.addKelebihan();
                                  } else {
                                    Get.snackbar(
                                        "", "dcv.minat.txt_kelebihan_blank".tr,
                                        snackPosition: SnackPosition.BOTTOM,
                                        borderRadius: 10,
                                        margin: EdgeInsets.only(
                                            left: 15, right: 15, bottom: 70),
                                        backgroundColor: Colors.amber,
                                        duration: const Duration(seconds: 4),
                                        animationDuration:
                                            const Duration(milliseconds: 500),
                                        forwardAnimationCurve:
                                            Curves.easeOutBack,
                                        reverseAnimationCurve:
                                            Curves.easeInBack,
                                        colorText: Colors.black,
                                        titleText: SizedBox.shrink(),
                                        icon: Icon(
                                          Icons.warning,
                                          color: Colors.black,
                                        ),
                                        mainButton: TextButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          child: Text(
                                            "tombol.dismiss".tr,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ));
                                  }
                                },
                                child: Icon(
                                  Icons.add_circle,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Obx(
                        () => Column(
                          children: [
                            SizedBox(
                              height: 5,
                            ),
                            Container(
                              alignment: Alignment
                                  .centerLeft, // Pastikan wrap mulai dari kiri
                              child: Wrap(
                                alignment: WrapAlignment
                                    .start, // Memastikan wrap mulai dari kiri
                                crossAxisAlignment: WrapCrossAlignment
                                    .start, // Menjaga elemen tetap di atas
                                spacing: 8, // Jarak horizontal antar button
                                runSpacing: 8, // Jarak antar baris button
                                children: [
                                  for (var i = 0;
                                      i <
                                          minatKonsepController
                                              .listKelebihan.length;
                                      i++)
                                    if (!(i == 0 &&
                                        minatKonsepController.listKelebihan[0]
                                            .trim()
                                            .isEmpty))
                                      Wrap(
                                        crossAxisAlignment:
                                            WrapCrossAlignment.center,
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color: Colors.grey[600]!,
                                                width: 0.5,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            padding: EdgeInsets.all(5),
                                            child: Text(
                                              minatKonsepController
                                                  .listKelebihan[i],
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[500]),
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              minatKonsepController
                                                  .deleteKelebihan(i);
                                            },
                                            child: Icon(
                                              Icons.close_rounded,
                                              color: Colors.red,
                                            ),
                                          )
                                        ],
                                      ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 15,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 5,
                            child: _buildTextField(
                              "dcv.minat.txt_kekurangan".tr,
                              errorsMessage[1]['isError']
                                  ? errorsMessage[1]['message']
                                  : '',
                              minatKonsepController.kekuranganController,
                              TextInputType.text,
                              null,
                              false,
                            ),
                          ),
                          Container(
                            width: 50,
                            height: 50,
                            margin: EdgeInsets.only(top: 26),
                            alignment: Alignment.center,
                            child: InkWell(
                              onTap: () {
                                if (minatKonsepController
                                    .kekuranganController.text.isNotEmpty) {
                                  minatKonsepController.addKekurangan();
                                } else {
                                  Get.snackbar(
                                      "", "dcv.minat.txt_kekurangan_blank".tr,
                                      snackPosition: SnackPosition.BOTTOM,
                                      borderRadius: 10,
                                      margin: EdgeInsets.only(
                                          left: 15, right: 15, bottom: 70),
                                      backgroundColor: Colors.amber,
                                      duration: const Duration(seconds: 4),
                                      animationDuration:
                                          const Duration(milliseconds: 500),
                                      forwardAnimationCurve: Curves.easeOutBack,
                                      reverseAnimationCurve: Curves.easeInBack,
                                      colorText: Colors.black,
                                      titleText: SizedBox.shrink(),
                                      icon: Icon(
                                        Icons.warning,
                                        color: Colors.black,
                                      ),
                                      mainButton: TextButton(
                                        onPressed: () {
                                          Get.back();
                                        },
                                        child: Text(
                                          "tombol.dismiss".tr,
                                          style: TextStyle(color: Colors.black),
                                        ),
                                      ));
                                }
                              },
                              child: Icon(
                                Icons.add_circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Obx(
                        () => Column(
                          children: [
                            SizedBox(
                              height: 5,
                            ),
                            Container(
                              alignment: Alignment
                                  .centerLeft, // Pastikan wrap mulai dari kiri
                              child: Wrap(
                                alignment: WrapAlignment
                                    .start, // Memastikan wrap mulai dari kiri
                                crossAxisAlignment: WrapCrossAlignment
                                    .start, // Menjaga elemen tetap di atas
                                spacing: 8, // Jarak horizontal antar button
                                runSpacing: 8, // Jarak antar baris button
                                children: [
                                  for (var i = 0;
                                      i <
                                          minatKonsepController
                                              .listKekurangan.length;
                                      i++)
                                    if (!(i == 0 &&
                                        minatKonsepController.listKekurangan[0]
                                            .trim()
                                            .isEmpty))
                                      Wrap(
                                        crossAxisAlignment:
                                            WrapCrossAlignment.center,
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color: Colors.grey[600]!,
                                                width: 0.5,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            padding: EdgeInsets.all(5),
                                            child: Text(
                                              minatKonsepController
                                                  .listKekurangan[i],
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[500]),
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              minatKonsepController
                                                  .deleteKekurangan(i);
                                            },
                                            child: Icon(
                                              Icons.close_rounded,
                                              color: Colors.red,
                                            ),
                                          )
                                        ],
                                      ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 15,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            "dcv.minat.komputerisasi.judul".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            "*",
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          )
                        ],
                      ),
                      Obx(() => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: minatKonsepController
                                .komputerisasiOptions.entries
                                .map((entry) {
                              final id = entry.key;
                              final label = entry.value;

                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Radio<int>(
                                    value: id,
                                    groupValue: minatKonsepController
                                        .selectedKomputerisasiId.value,
                                    onChanged: (int? value) {
                                      if (value != null) {
                                        minatKonsepController
                                            .selectedKomputerisasiId
                                            .value = value;
                                      }
                                    },
                                    visualDensity: const VisualDensity(
                                        horizontal: -4, vertical: -3),
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  Expanded(
                                    child: Text(
                                      label,
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          )),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: [
                          Text(
                            "dcv.minat.jml_org.judul".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            "*",
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          )
                        ],
                      ),
                      Obx(
                        () => Column(
                          children: minatKonsepController.jmlOrangOptions
                              .map((option) {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment
                                  .center, // Agar vertikal rata
                              children: [
                                Radio<String>(
                                  value: option,
                                  groupValue: minatKonsepController
                                      .selectedjmlOrangOption.value,
                                  onChanged: (String? value) {
                                    setState(() {
                                      minatKonsepController
                                          .selectedjmlOrangOption
                                          .value = value!;
                                    });
                                  },
                                  visualDensity: const VisualDensity(
                                      horizontal: -4,
                                      vertical: -3), // Kurangi ukuran
                                  materialTapTargetSize: MaterialTapTargetSize
                                      .shrinkWrap, // Hilangkan padding klik
                                ),
                                Text(option,
                                    style: const TextStyle(fontSize: 12)),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: [
                          Expanded(
                            flex: 9,
                            child: Text(
                              "dcv.minat.txt_penilaian".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            ),
                          )
                        ],
                      ),
                      Obx(
                        () => Wrap(
                          alignment: WrapAlignment.start,
                          crossAxisAlignment: WrapCrossAlignment.start,
                          spacing: 8, // Jarak antar radio button
                          runSpacing: 4, // Jarak antar baris jika wrap
                          children: minatKonsepController.penilaianOptions
                              .map((option) {
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Radio<String>(
                                  value: option,
                                  groupValue: minatKonsepController
                                      .selectedjPenilaianOption.value,
                                  onChanged: (String? value) {
                                    setState(() {
                                      minatKonsepController
                                          .selectedjPenilaianOption
                                          .value = value!;
                                    });
                                  },
                                  visualDensity: const VisualDensity(
                                      horizontal: -4,
                                      vertical: -3), // Kurangi ukuran
                                  materialTapTargetSize: MaterialTapTargetSize
                                      .shrinkWrap, // Hilangkan padding klik
                                ),
                                Text(option,
                                    style: const TextStyle(fontSize: 12)),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: [
                          Text(
                            "dcv.minat.ruang_lingkup.judul".tr,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            "*",
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          )
                        ],
                      ),
                      Obx(
                        () => Align(
                          alignment: Alignment.topLeft,
                          child: Wrap(
                            alignment:
                                WrapAlignment.start, // Align ke kiri (start)
                            spacing: 10, // Menghilangkan jarak antar item
                            runSpacing:
                                4, // Jarak antar baris jika melebihi lebar layar
                            children: minatKonsepController
                                .lingkupPekerjaanOptions.entries
                                .map((entry) {
                              final id = entry.key;
                              final label = entry.value;
                              return Row(
                                mainAxisSize: MainAxisSize
                                    .min, // Supaya tidak mengambil seluruh lebar
                                children: [
                                  Checkbox(
                                    value: minatKonsepController
                                            .selectedRuangLingkup[id] ??
                                        false,
                                    onChanged: (bool? value) {
                                      minatKonsepController
                                              .selectedRuangLingkup[id] =
                                          value ?? false;
                                    },
                                    visualDensity: VisualDensity
                                        .compact, // Mengurangi padding bawaan
                                    materialTapTargetSize: MaterialTapTargetSize
                                        .shrinkWrap, // Mengecilkan area sentuh
                                  ),
                                  Expanded(
                                    child: Text(
                                      TranslationService.translateBetweenLangs(
                                        label,
                                        "dcv.minat.ruang_lingkup",
                                        "id",
                                        Get.locale?.languageCode
                                                .toLowerCase() ??
                                            "id",
                                      ),
                                      style: const TextStyle(fontSize: 12),
                                      softWrap: true,
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 15,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(12),
              child: SizedBox(
                height: 50,
                child: Obx(
                  () => formController.isLoading.value
                      ? Center(
                          child: CircularProgressIndicator(),
                        )
                      : ElevatedButton(
                          onPressed: () {
                            // Validasi form
                            bool isValid = true;
                            setState(() {
                              for (int i = 0; i < errorsMessage.length; i++) {
                                errorsMessage[i]['isError'] = false;
                              }
                            });

                            // Validasi kelebihan
                            if (minatKonsepController.listKelebihan.isEmpty) {
                              setState(() {
                                errorsMessage[0]['isError'] = true;
                              });
                              Get.snackbar("", errorsMessage[0]['message'],
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }

                            // Validasi kekurangan
                            if (minatKonsepController.listKekurangan.isEmpty) {
                              setState(() {
                                errorsMessage[1]['isError'] = true;
                              });
                              Get.snackbar("", errorsMessage[1]['message'],
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }

                            // Validasi bahasa asing jika dipilih "Ya"
                            if (minatKonsepController.selectedValue.value ==
                                "Ya") {
                              if (minatKonsepController.bahasaAsing.isEmpty) {
                                setState(() {
                                  errorsMessage[2]['isError'] = true;
                                });
                                Get.snackbar("", errorsMessage[2]['message'],
                                    snackPosition: SnackPosition.BOTTOM,
                                    borderRadius: 10,
                                    margin: EdgeInsets.only(
                                        left: 15, right: 15, bottom: 70),
                                    backgroundColor: Colors.amber,
                                    duration: const Duration(seconds: 4),
                                    animationDuration:
                                        const Duration(milliseconds: 500),
                                    forwardAnimationCurve: Curves.easeOutBack,
                                    reverseAnimationCurve: Curves.easeInBack,
                                    colorText: Colors.black,
                                    titleText: SizedBox.shrink(),
                                    icon: Icon(
                                      Icons.warning,
                                      color: Colors.black,
                                    ),
                                    mainButton: TextButton(
                                      onPressed: () {
                                        Get.back();
                                      },
                                      child: Text(
                                        "tombol.dismiss".tr,
                                        style: TextStyle(color: Colors.black),
                                      ),
                                    ));
                                isValid = false;
                                return;
                              }
                            }

                            // Validasi komputerisasi
                            if (minatKonsepController
                                    .selectedKomputerisasiId.value ==
                                -1) {
                              Get.snackbar(
                                  "", "dcv.minat.komputerisasi.validation".tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }

                            // Validasi jumlah orang yang bisa dipimpin
                            if (minatKonsepController
                                .selectedjmlOrangOption.value.isEmpty) {
                              Get.snackbar(
                                  "", "dcv.minat.jml_org.validation".tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }

                            // Validasi penilaian presentasi
                            if (minatKonsepController
                                .selectedjPenilaianOption.value.isEmpty) {
                              Get.snackbar(
                                  "", "dcv.minat.txt_penilaian_blank".tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }

                            // Validasi ruang lingkup pekerjaan
                            bool hasSelectedRuangLingkup = minatKonsepController
                                .selectedRuangLingkup.values
                                .any((value) => value == true);
                            if (!hasSelectedRuangLingkup) {
                              Get.snackbar(
                                  "", "dcv.minat.ruang_lingkup.validation".tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              isValid = false;
                              return;
                            }
                            data.clear();
                            data.add({
                              "penguasaan_bahasa":
                                  TranslationService.translateBetweenLangs(
                                      minatKonsepController.selectedValue.value,
                                      "konfirmasi2",
                                      Get.locale?.languageCode.toLowerCase() ??
                                          "id",
                                      "id"),
                              "bahasa_asing": minatKonsepController.bahasaAsing
                                  .map((element) => element.toJson())
                                  .toList(),
                              "kelebihan":
                                  minatKonsepController.listKelebihan.join(","),
                              "kekurangan": minatKonsepController.listKekurangan
                                  .join(","),
                              "komputerisasi": minatKonsepController
                                  .selectedKomputerisasiId.value
                                  .toString(),
                              "memimpin_tim": minatKonsepController
                                      .jmlOrangOptions
                                      .indexOf(minatKonsepController
                                          .selectedjmlOrangOption.value) +
                                  1,
                              "presentasi": minatKonsepController
                                      .penilaianOptions
                                      .indexOf(minatKonsepController
                                          .selectedjPenilaianOption.value) +
                                  1,
                              "rlp": minatKonsepController
                                  .selectedRuangLingkup.keys
                                  .where((key) => minatKonsepController
                                      .selectedRuangLingkup[key]!)
                                  .map((key) => key.toString())
                                  .toList()
                                  .join(","),
                            });
                            formController.saveRiwayatMinatKonsep(data);
                          },
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 100,
                                vertical: 6,
                              ),
                              minimumSize: const Size.fromHeight(45),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50),
                              ),
                              textStyle: TextStyle(fontSize: 14)),
                          child: Text(
                            "tombol.simpan".tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
