import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/provinsi_dialog.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:digital_cv_mobile/models/organisasi_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PengalamanOrganisasiScreen extends StatefulWidget {
  const PengalamanOrganisasiScreen({super.key});

  @override
  State<PengalamanOrganisasiScreen> createState() =>
      _PengalamanOrganisasiScreenState();
}

class _PengalamanOrganisasiScreenState
    extends State<PengalamanOrganisasiScreen> {
  final FormController formController = Get.find<FormController>();
  final TextEditingController _jabatanController = TextEditingController();
  final TextEditingController _tempatController = TextEditingController();
  final TextEditingController _namaController = TextEditingController();
  final TextEditingController _tahunController = TextEditingController();
  late OrganisasiModel organisasiModel;
  final _formKey = GlobalKey<FormState>();
  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "dcv.pengalaman_org.txt_nama_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_org.txt_tempat_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_org.txt_jabatan_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_org.txt_tahun_blank".tr,
      "isError": false,
    },
  ];

  List<Map<String, dynamic>> data = [];
  String? selectedValue;
  int? idLocation;

  @override
  void initState() {
    super.initState();

    organisasiModel = OrganisasiModel(
      id: "",
      nama: "",
      jabatan: "",
      tahun: "",
      tempat: "",
    );
    selectedValue = null;
    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;
      if (args['organisasi'] != null) {
        organisasiModel = args['organisasi'];
      } else {
        organisasiModel = OrganisasiModel(
          id: "",
          nama: "",
          jabatan: "",
          tahun: "",
          tempat: "",
        );
      }

      if (args['confirm'] != null || args['confirm'] != "") {
        if (args['confirm'] == true) {
          selectedValue = TranslationService.translateBetweenLangs(
            "Ya",
            "konfirmasi2",
            "id",
            Get.locale?.languageCode.toLowerCase() ?? "id",
          );
        } else {
          selectedValue = TranslationService.translateBetweenLangs(
            "Tidak",
            "konfirmasi2",
            "id",
            Get.locale?.languageCode.toLowerCase() ?? "id",
          );
        }
      } else {
        selectedValue = TranslationService.translateBetweenLangs(
          "Tidak",
          "konfirmasi2",
          "id",
          Get.locale?.languageCode.toLowerCase() ?? "id",
        );
      }

      _namaController.text = organisasiModel.nama;
      _tempatController.text = organisasiModel.tempat;
      _jabatanController.text = organisasiModel.jabatan;
      _tahunController.text = organisasiModel.tahun;
    }
  }

  void _bukaPilihLokasi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      LokasiBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      LogService.log.i("Lokasi yang dipilih: ${result.nama}");
      _tempatController.text =
          result.nama; // Set text field dengan nama provinsi
      idLocation = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  DateTime? selectedTahunMasuk;

  Future<void> _selectTahun() async {
    final DateTime? pickedDate = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("date_picker.pilih_tahun".tr),
          content: SizedBox(
            width: 300,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(DateTime.now().year - 100),
              lastDate: DateTime(DateTime.now().year + 100),
              selectedDate: selectedTahunMasuk ?? DateTime.now(),
              onChanged: (DateTime dateTime) {
                Navigator.pop(context, dateTime); // ✅ kirim nilai balik
              },
            ),
          ),
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        selectedTahunMasuk = pickedDate;
        _tahunController.text = pickedDate.year.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.pengalaman_org.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "dcv.pengalaman_org.txt_konfirm_org".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: DropdownButtonFormField<String>(
                            value: selectedValue,
                            isDense: true, // Mengurangi padding internal
                            isExpanded:
                                true, // Membuat dropdown mengisi lebar penuh
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 12), // Padding dalam Input
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ), // Ukuran teks dropdown
                            iconSize: 24, // Ukuran icon dropdown
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedValue = newValue!;
                                LogService.log
                                    .i("Selected value ORG: $selectedValue");
                              });
                            },
                            items: ['konfirmasi.iya'.tr, 'konfirmasi.tidak'.tr]
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Container(
                                  height: 40, // Mengatur tinggi dropdown item
                                  alignment: Alignment
                                      .centerLeft, // Menyamakan posisi teks
                                  child: Text(
                                    value,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        TranslationService.translateBetweenLangs(
                                  selectedValue ?? "Tidak",
                                  "konfirmasi2",
                                  Get.locale?.languageCode.toLowerCase() ??
                                      "id",
                                  "id",
                                ) ==
                                "Ya"
                            ? SizedBox(
                                child: Column(
                                  children: [
                                    _buildTextField(
                                      "dcv.pengalaman_org.txt_nama".tr,
                                      errorsMessage[0]['isError']
                                          ? errorsMessage[0]['message']
                                          : '',
                                      _namaController,
                                      TextInputType.name,
                                      null,
                                      false,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          "dcv.pengalaman_org.txt_tempat".tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          "*",
                                          style: TextStyle(
                                            color: Colors.red,
                                          ),
                                        )
                                      ],
                                    ),
                                    FormTextFiled(
                                      controller: _tempatController,
                                      onTap: () {
                                        _bukaPilihLokasi();
                                      },
                                      keyboardType: TextInputType.text,
                                      hintText:
                                          "dcv.pengalaman_org.txt_tempat".tr,
                                      isReadOnly: true,
                                      maxLines: 1,
                                      suffixIcon: Icon(
                                        Icons.arrow_drop_down,
                                        size: 30,
                                      ),
                                      errorText: errorsMessage[1]['isError']
                                          ? errorsMessage[1]['message']
                                          : null,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    _buildTextField(
                                      "dcv.pengalaman_org.txt_jabatan".tr,
                                      errorsMessage[2]['isError']
                                          ? errorsMessage[2]['message']
                                          : '',
                                      _jabatanController,
                                      TextInputType.text,
                                      null,
                                      false,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          "dcv.pengalaman_org.txt_tahun".tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          "*",
                                          style: TextStyle(
                                            color: Colors.red,
                                          ),
                                        )
                                      ],
                                    ),
                                    FormTextFiled(
                                      controller: _tahunController,
                                      onTap: () {
                                        _selectTahun();
                                      },
                                      keyboardType: TextInputType.text,
                                      hintText:
                                          "dcv.pengalaman_org.txt_tahun".tr,
                                      isReadOnly: true,
                                      maxLines: 1,
                                      suffixIcon: Icon(
                                        Icons.calendar_month_outlined,
                                        size: 30,
                                        color: Colors.grey[500],
                                      ),
                                      errorText: errorsMessage[3]['isError']
                                          ? errorsMessage[3]['message']
                                          : null,
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox()
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (organisasiModel.nama != "") ...[
                    SizedBox(
                      height: 45,
                      child: Obx(
                        () => formController.isLoading.value
                            ? const Center(child: CircularProgressIndicator())
                            : ElevatedButton(
                                onPressed: () async {
                                  formController.deleteRiwayatOrganisasi(
                                      organisasiModel.id);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 6,
                                  ),
                                  minimumSize: const Size(0, 45),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                  textStyle: const TextStyle(fontSize: 14),
                                ),
                                child: Text(
                                  "tombol.hapus".tr,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                  SizedBox(
                    height: 45,
                    child: Obx(
                      () => formController.isLoading.value
                          ? const Center(child: CircularProgressIndicator())
                          : ElevatedButton(
                              onPressed: () async {
                                if (TranslationService.translateBetweenLangs(
                                      selectedValue ?? "Tidak",
                                      "konfirmasi2",
                                      Get.locale?.languageCode.toLowerCase() ??
                                          "id",
                                      "id",
                                    ) ==
                                    "Ya") {
                                  setState(() {
                                    errorsMessage[0]['isError'] = false;
                                    errorsMessage[1]['isError'] = false;
                                    errorsMessage[2]['isError'] = false;
                                    errorsMessage[3]['isError'] = false;
                                  });
                                  if (_namaController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[0]['isError'] = true;
                                    });
                                    Get.snackbar(
                                        "", errorsMessage[0]['message'],
                                        snackPosition: SnackPosition.BOTTOM,
                                        borderRadius: 10,
                                        margin: EdgeInsets.only(
                                            left: 15, right: 15, bottom: 70),
                                        backgroundColor: Colors.amber,
                                        duration: const Duration(seconds: 4),
                                        animationDuration:
                                            const Duration(milliseconds: 500),
                                        forwardAnimationCurve:
                                            Curves.easeOutBack,
                                        reverseAnimationCurve:
                                            Curves.easeInBack,
                                        colorText: Colors.black,
                                        titleText: SizedBox.shrink(),
                                        icon: Icon(
                                          Icons.warning,
                                          color: Colors.black,
                                        ),
                                        mainButton: TextButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          child: Text(
                                            "tombol.dismiss".tr,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ));
                                    return;
                                  }
                                  if (_tempatController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[1]['isError'] = true;
                                    });
                                    Get.snackbar(
                                        "", errorsMessage[1]['message'],
                                        snackPosition: SnackPosition.BOTTOM,
                                        borderRadius: 10,
                                        margin: EdgeInsets.only(
                                            left: 15, right: 15, bottom: 70),
                                        backgroundColor: Colors.amber,
                                        duration: const Duration(seconds: 4),
                                        animationDuration:
                                            const Duration(milliseconds: 500),
                                        forwardAnimationCurve:
                                            Curves.easeOutBack,
                                        reverseAnimationCurve:
                                            Curves.easeInBack,
                                        colorText: Colors.black,
                                        titleText: SizedBox.shrink(),
                                        icon: Icon(
                                          Icons.warning,
                                          color: Colors.black,
                                        ),
                                        mainButton: TextButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          child: Text(
                                            "tombol.dismiss".tr,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ));
                                    return;
                                  }
                                  if (_jabatanController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[2]['isError'] = true;
                                    });
                                    Get.snackbar(
                                        "", errorsMessage[2]['message'],
                                        snackPosition: SnackPosition.BOTTOM,
                                        borderRadius: 10,
                                        margin: EdgeInsets.only(
                                            left: 15, right: 15, bottom: 70),
                                        backgroundColor: Colors.amber,
                                        duration: const Duration(seconds: 4),
                                        animationDuration:
                                            const Duration(milliseconds: 500),
                                        forwardAnimationCurve:
                                            Curves.easeOutBack,
                                        reverseAnimationCurve:
                                            Curves.easeInBack,
                                        colorText: Colors.black,
                                        titleText: SizedBox.shrink(),
                                        icon: Icon(
                                          Icons.warning,
                                          color: Colors.black,
                                        ),
                                        mainButton: TextButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          child: Text(
                                            "tombol.dismiss".tr,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ));
                                    return;
                                  }

                                  if (_tahunController.text.isEmpty) {
                                    setState(() {
                                      errorsMessage[3]['isError'] = true;
                                    });
                                    Get.snackbar(
                                        "", errorsMessage[3]['message'],
                                        snackPosition: SnackPosition.BOTTOM,
                                        borderRadius: 10,
                                        margin: EdgeInsets.only(
                                            left: 15, right: 15, bottom: 70),
                                        backgroundColor: Colors.amber,
                                        duration: const Duration(seconds: 4),
                                        animationDuration:
                                            const Duration(milliseconds: 500),
                                        forwardAnimationCurve:
                                            Curves.easeOutBack,
                                        reverseAnimationCurve:
                                            Curves.easeInBack,
                                        colorText: Colors.black,
                                        titleText: SizedBox.shrink(),
                                        icon: Icon(
                                          Icons.warning,
                                          color: Colors.black,
                                        ),
                                        mainButton: TextButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          child: Text(
                                            "tombol.dismiss".tr,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ));
                                    return;
                                  }
                                }

                                data.clear();
                                if (organisasiModel.nama != "") {
                                  data.add({
                                    "id_organisasi": organisasiModel.id,
                                    "organisasi": TranslationService
                                        .translateBetweenLangs(
                                      selectedValue ?? "Tidak",
                                      "konfirmasi2",
                                      Get.locale?.languageCode.toLowerCase() ??
                                          "id",
                                      "id",
                                    ),
                                    "nama": _namaController.text,
                                    "jabatan": _jabatanController.text,
                                    "tahun": _tahunController.text,
                                    "tempat": _tempatController.text,
                                  });
                                  formController.updateRiwayatOrganisasi(data);
                                } else {
                                  data.add({
                                    "organisasi": TranslationService
                                        .translateBetweenLangs(
                                      selectedValue ?? "Tidak",
                                      "konfirmasi2",
                                      Get.locale?.languageCode.toLowerCase() ??
                                          "id",
                                      "id",
                                    ),
                                    "nama": _namaController.text,
                                    "jabatan": _jabatanController.text,
                                    "tahun": _tahunController.text,
                                    "tempat": _tempatController.text,
                                  });
                                  formController.saveRiwayatOrganisasi(data);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                elevation: 0,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 6,
                                ),
                                minimumSize: const Size(0, 45),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                textStyle: const TextStyle(fontSize: 14),
                              ),
                              child: Text(
                                "tombol.simpan".tr,
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
