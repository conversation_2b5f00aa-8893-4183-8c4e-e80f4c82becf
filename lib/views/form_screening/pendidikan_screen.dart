import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/pendidikan_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RiwayatPendidikanScreen extends StatefulWidget {
  const RiwayatPendidikanScreen({super.key});

  @override
  State<RiwayatPendidikanScreen> createState() =>
      _RiwayatPendidikanScreenState();
}

class _RiwayatPendidikanScreenState extends State<RiwayatPendidikanScreen> {
  final FormController formController = Get.find<FormController>();
  final TextEditingController _namaLengkapController = TextEditingController();
  final TextEditingController _jurusanController = TextEditingController();
  final TextEditingController _tahunMasukController = TextEditingController();
  final TextEditingController _tahunLulusController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late PendidikanModel pendidikanModel;
  List<Map<String, dynamic>> data = [];
  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "dcv.pendidikan.txt_nama_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_jurusan_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_masuk_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_lulus_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_jenjang_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_jenjang_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pendidikan.txt_ket_blank".tr,
      "isError": false,
    }
  ];

  String? selectedKeterangan;
  String? selectedValue;
  String? selectedDiploma;
  String? pendidikanTerakhir;
  Map<int, String> pendidikanOptions = {
    1: "dcv.pendidikan.jenjang1".tr,
    2: "dcv.pendidikan.jenjang2".tr,
    3: "dcv.pendidikan.jenjang3".tr,
    4: "dcv.pendidikan.jenjang4".tr,
    5: "dcv.pendidikan.jenjang5".tr,
    6: "dcv.pendidikan.jenjang6".tr,
    7: "dcv.pendidikan.jenjang7".tr,
  };
  Map<int, String> keteranganOptions = {
    1: "dcv.pendidikan.ket1".tr,
    2: "dcv.pendidikan.ket2".tr,
    3: "dcv.pendidikan.ket3".tr,
    4: "dcv.pendidikan.ket4".tr,
    5: "dcv.pendidikan.ket5".tr
  };
  @override
  void initState() {
    super.initState();
    pendidikanModel = PendidikanModel(
      id: "",
      namaSekolah: "",
      jenjang: "",
      jurusan: "",
      tahunMulai: "",
      tahunSelesai: "",
      ket: "",
    );
    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;
      if (args['pendidikan'] != null) {
        pendidikanModel = args['pendidikan'];
      } else {
        pendidikanModel = PendidikanModel(
          id: "",
          namaSekolah: "",
          jenjang: "",
          jurusan: "",
          tahunMulai: "",
          tahunSelesai: "",
          ket: "",
        );
      }
      if (args['diploma'] != null || args['diploma'] != "") {
        selectedDiploma = args['diploma'] != "" ? args['diploma'] : null;
        debugPrint("Selected Diploma: $selectedDiploma");
      }

      if (args['pendidikan_terakhir'] != null ||
          args['pendidikan_terakhir'] != "") {
        pendidikanTerakhir = args['pendidikan_terakhir'] != ""
            ? TranslationService.translateBetweenLangs(
                args['pendidikan_terakhir'],
                "dcv.pendidikan",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id",
              )
            : null;
        debugPrint("Pendidikan Terakhir: $pendidikanTerakhir");
      }

      selectedValue = pendidikanModel.jenjang != ""
          ? TranslationService.translateBetweenLangs(
              pendidikanModel.jenjang,
              "dcv.pendidikan",
              "id",
              Get.locale?.languageCode.toLowerCase() ?? "id",
            )
          : null;
      _namaLengkapController.text = pendidikanModel.namaSekolah;
      _jurusanController.text = pendidikanModel.jurusan;
      _tahunMasukController.text = pendidikanModel.tahunMulai;
      _tahunLulusController.text = pendidikanModel.tahunSelesai;
      selectedKeterangan = pendidikanModel.ket != ""
          ? TranslationService.translateBetweenLangs(
              pendidikanModel.ket,
              "dcv.pendidikan",
              "id",
              Get.locale?.languageCode.toLowerCase() ?? "id",
            )
          : null;
    }
  }

  DateTime? selectedTahunMasuk;
  DateTime? selectedTahunLulus;

  Future<void> _selectTahunMasuk() async {
    final DateTime? pickedDate = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("date_picker.pilih_tahun_masuk".tr),
          content: SizedBox(
            width: 300,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(DateTime.now().year - 100),
              lastDate: DateTime(DateTime.now().year + 100),
              selectedDate: selectedTahunMasuk ?? DateTime.now(),
              onChanged: (DateTime dateTime) {
                Navigator.pop(context, dateTime); // ✅ kirim nilai balik
              },
            ),
          ),
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        selectedTahunMasuk = pickedDate;
        _tahunMasukController.text = pickedDate.year.toString();
      });
    }
  }

  Future<void> _selectTahunLulus() async {
    final DateTime? pickedDate = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("date_picker.pilih_tahun_lulus".tr),
          content: SizedBox(
            width: 300,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(DateTime.now().year - 100),
              lastDate: DateTime(DateTime.now().year + 100),
              selectedDate: selectedTahunLulus ?? DateTime.now(),
              onChanged: (DateTime dateTime) {
                Navigator.pop(context, dateTime); // ✅ kirim nilai balik
              },
            ),
          ),
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        selectedTahunLulus = pickedDate;
        _tahunLulusController.text = pickedDate.year.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.pendidikan.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "dcv.pendidikan.txt_pendidikan_terakhir".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: Colors.grey[200],
                          ),
                          child: DropdownButtonFormField<String>(
                            hint: Text(
                                "dcv.pendidikan.txt_pendidikan_terakhir_hint"
                                    .tr),
                            value: pendidikanTerakhir,
                            isDense: true,
                            isExpanded: true,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 12),
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ),
                            iconSize: 24,
                            onChanged: (String? newValue) {
                              setState(() {
                                pendidikanTerakhir = newValue!;
                                // Perbaiki logika: jika TIDAK jenjang 5, 6, 7, set diploma ke "Tidak"
                                if (pendidikanTerakhir !=
                                        "dcv.pendidikan.jenjang5".tr &&
                                    pendidikanTerakhir !=
                                        "dcv.pendidikan.jenjang6".tr &&
                                    pendidikanTerakhir !=
                                        "dcv.pendidikan.jenjang7".tr) {
                                  selectedDiploma = "Tidak";
                                }
                              });
                            },
                            items: pendidikanOptions.entries
                                .map<DropdownMenuItem<String>>((entry) {
                              return DropdownMenuItem<String>(
                                value: entry.value,
                                child: Container(
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    entry.value,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        if (errorsMessage[4]['isError'] != false)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    errorsMessage[4]['message'],
                                    style: TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        if (TranslationService.translateBetweenLangs(
                                    pendidikanTerakhir ?? "SD",
                                    "dcv.pendidikan",
                                    "id",
                                    Get.locale?.languageCode.toLowerCase() ??
                                        "id") ==
                                "dcv.pendidikan.jenjang5".tr ||
                            TranslationService.translateBetweenLangs(
                                    pendidikanTerakhir ?? "SD",
                                    "dcv.pendidikan",
                                    "id",
                                    Get.locale?.languageCode.toLowerCase() ??
                                        "id") ==
                                "dcv.pendidikan.jenjang6".tr ||
                            TranslationService.translateBetweenLangs(
                                    pendidikanTerakhir ?? "SD",
                                    "dcv.pendidikan",
                                    "id",
                                    Get.locale?.languageCode.toLowerCase() ??
                                        "id") ==
                                "dcv.pendidikan.jenjang7".tr)
                          Column(
                            children: [
                              SizedBox(
                                height: 15,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "dcv.pendidikan.txt_diploma".tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    "*",
                                    style: TextStyle(
                                      color: Colors.red,
                                    ),
                                  )
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: "Ya",
                                        groupValue: selectedDiploma,
                                        materialTapTargetSize: MaterialTapTargetSize
                                            .shrinkWrap, // Hilangkan padding default
                                        visualDensity: VisualDensity
                                            .compact, // Lebih kecil lagi
                                        onChanged: (String? value) {
                                          setState(() {
                                            selectedDiploma = value;
                                          });
                                        },
                                      ),
                                      Text(
                                        "konfirmasi2.iya".tr,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                      width:
                                          16), // Beri jarak antar radio button
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: "Tidak",
                                        groupValue: selectedDiploma,
                                        materialTapTargetSize: MaterialTapTargetSize
                                            .shrinkWrap, // Hilangkan padding default
                                        visualDensity: VisualDensity
                                            .compact, // Lebih kecil lagi
                                        onChanged: (String? value) {
                                          setState(() {
                                            selectedDiploma = value;
                                          });
                                        },
                                      ),
                                      Text(
                                        "konfirmasi2.tidak".tr,
                                        style: TextStyle(fontSize: 12),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        SizedBox(
                          height: 10,
                        ),
                        Divider(
                          thickness: 1,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Text(
                              "dcv.pendidikan.txt_jenjang".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: Colors.grey[200],
                          ),
                          child: DropdownButtonFormField<String>(
                            hint: Text("dcv.pendidikan.txt_jenjang_hint".tr),
                            value: selectedValue,
                            isDense: true, // Mengurangi padding internal
                            isExpanded:
                                true, // Membuat dropdown mengisi lebar penuh
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 12), // Padding dalam Input
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ), // Ukuran teks dropdown
                            iconSize: 24, // Ukuran icon dropdown
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedValue = newValue!;
                              });
                            },
                            items: pendidikanOptions.entries
                                .map<DropdownMenuItem<String>>((entry) {
                              return DropdownMenuItem<String>(
                                value: entry.value,
                                child: Container(
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    entry.value,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        if (errorsMessage[5]['isError'] != false)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    errorsMessage[5]['message'],
                                    style: TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        SizedBox(
                          height: 15,
                        ),
                        _buildTextField(
                          "dcv.pendidikan.txt_nama".tr,
                          errorsMessage[0]['isError']
                              ? errorsMessage[0]['message']
                              : "",
                          _namaLengkapController,
                          TextInputType.text,
                          null,
                          false,
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        _buildTextField(
                          "dcv.pendidikan.txt_jurusan".tr,
                          errorsMessage[1]['isError']
                              ? errorsMessage[1]['message']
                              : "",
                          _jurusanController,
                          TextInputType.text,
                          null,
                          false,
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.pendidikan.txt_masuk".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  FormTextFiled(
                                    controller: _tahunMasukController,
                                    onTap: () {
                                      _selectTahunMasuk();
                                    },
                                    keyboardType: TextInputType.text,
                                    isReadOnly: true,
                                    hintText: "dcv.pendidikan.txt_masuk".tr,
                                    maxLines: 1,
                                    suffixIcon: Icon(
                                      Icons.calendar_month_outlined,
                                      size: 30,
                                      color: Colors.grey[500],
                                    ),
                                    errorText: errorsMessage[2]['isError']
                                        ? errorsMessage[2]['message']
                                        : null,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.pendidikan.txt_lulus".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  FormTextFiled(
                                    controller: _tahunLulusController,
                                    onTap: () {
                                      _selectTahunLulus();
                                    },
                                    keyboardType: TextInputType.text,
                                    isReadOnly: true,
                                    hintText: "dcv.pendidikan.txt_lulus".tr,
                                    maxLines: 1,
                                    suffixIcon: Icon(
                                      Icons.calendar_month_outlined,
                                      size: 30,
                                      color: Colors.grey[500],
                                    ),
                                    errorText: errorsMessage[3]['isError']
                                        ? errorsMessage[3]['message']
                                        : null,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          children: [
                            Text(
                              "dcv.pendidikan.txt_ket".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: Colors.grey[200],
                          ),
                          child: DropdownButtonFormField<String>(
                            hint: Text("dcv.pendidikan.txt_ket_hint".tr),
                            value: selectedKeterangan,
                            isDense: true, // Mengurangi padding internal
                            isExpanded:
                                true, // Membuat dropdown mengisi lebar penuh
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 12), // Padding dalam Input
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ), // Ukuran teks dropdown
                            iconSize: 24, // Ukuran icon dropdown
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedKeterangan = newValue!;
                              });
                            },
                            items: keteranganOptions.entries
                                .map<DropdownMenuItem<String>>((entry) {
                              return DropdownMenuItem<String>(
                                value: entry.value,
                                child: Container(
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    entry.value,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        if (errorsMessage[6]['isError'] != false)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    errorsMessage[6]['message'],
                                    style: TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        SizedBox(
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      if (pendidikanModel.namaSekolah != "") ...[
                        Expanded(
                          child: SizedBox(
                            height: 45,
                            child: Obx(
                              () => formController.isLoading.value
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : ElevatedButton(
                                      onPressed: () async {
                                        formController.deleteRiwayatPendidikan(
                                            pendidikanModel.id);
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        elevation: 0,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 6),
                                        minimumSize: const Size.fromHeight(45),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                        textStyle:
                                            const TextStyle(fontSize: 14),
                                      ),
                                      child: Text(
                                        "tombol.hapus".tr,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Expanded(
                        child: SizedBox(
                          height: 50,
                          child: Obx(
                            () => formController.isLoading.value
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : ElevatedButton(
                                    onPressed: () async {
                                      for (var error in errorsMessage) {
                                        error['isError'] = false;
                                      }
                                      if (pendidikanTerakhir == null ||
                                          pendidikanTerakhir!.isEmpty) {
                                        setState(() {
                                          errorsMessage[4]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[4]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }

                                      if (selectedValue == null ||
                                          selectedValue!.isEmpty) {
                                        setState(() {
                                          errorsMessage[5]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[5]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }

                                      if (_namaLengkapController == null ||
                                          _namaLengkapController.text.isEmpty) {
                                        setState(() {
                                          errorsMessage[0]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[0]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }
                                      if (_jurusanController == null ||
                                          _jurusanController.text.isEmpty) {
                                        setState(() {
                                          errorsMessage[1]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[1]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }
                                      if (_tahunMasukController == null ||
                                          _tahunMasukController.text.isEmpty) {
                                        setState(() {
                                          errorsMessage[2]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[2]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }
                                      if (_tahunLulusController == null ||
                                          _tahunLulusController.text.isEmpty) {
                                        setState(() {
                                          errorsMessage[3]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[3]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }

                                      if (int.parse(
                                              _tahunLulusController.text) <
                                          int.parse(
                                              _tahunMasukController.text)) {
                                        Get.snackbar(
                                            "",
                                            "dcv.pendidikan.txt_tahun_lulus_validation"
                                                .tr,
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }

                                      if (selectedKeterangan == null ||
                                          selectedKeterangan!.isEmpty) {
                                        setState(() {
                                          errorsMessage[6]['isError'] = true;
                                        });
                                        Get.snackbar(
                                            "", errorsMessage[6]['message'],
                                            snackPosition: SnackPosition.BOTTOM,
                                            borderRadius: 10,
                                            margin: EdgeInsets.only(
                                                left: 15,
                                                right: 15,
                                                bottom: 70),
                                            backgroundColor: Colors.amber,
                                            duration:
                                                const Duration(seconds: 4),
                                            animationDuration: const Duration(
                                                milliseconds: 500),
                                            forwardAnimationCurve:
                                                Curves.easeOutBack,
                                            reverseAnimationCurve:
                                                Curves.easeInBack,
                                            colorText: Colors.black,
                                            titleText: SizedBox.shrink(),
                                            icon: Icon(
                                              Icons.warning,
                                              color: Colors.black,
                                            ),
                                            mainButton: TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                "tombol.dismiss".tr,
                                                style: TextStyle(
                                                    color: Colors.black),
                                              ),
                                            ));
                                        return;
                                      }

                                      // if (_formKey.currentState!.validate()) {
                                      data.clear();
                                      if (pendidikanModel.namaSekolah != "") {
                                        data.add({
                                          "id_riwayat": pendidikanModel.id,
                                          "pendidikan_tinggi":
                                              TranslationService
                                                  .translateBetweenLangs(
                                            pendidikanTerakhir ?? "SD",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "cek_diploma": TranslationService
                                              .translateBetweenLangs(
                                            selectedDiploma ?? "Tidak",
                                            "dcv.konfirmasi2",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "jenjang_pendidikan":
                                              TranslationService
                                                  .translateBetweenLangs(
                                            selectedValue ?? "SD",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "nama_sekolah_pendidikan":
                                              _namaLengkapController.text,
                                          "jurusan_pendidikan":
                                              _jurusanController.text,
                                          "tahun_mulai_pendidikan":
                                              _tahunMasukController.text,
                                          "tahun_selesai_pendidikan":
                                              _tahunLulusController.text,
                                          "ket_pendidikan": TranslationService
                                              .translateBetweenLangs(
                                            selectedKeterangan ??
                                                "Lulus Berijazah",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                        });
                                        formController
                                            .updateRiwayatPendidikan(data);
                                      } else {
                                        data.add({
                                          "pendidikan_tinggi":
                                              TranslationService
                                                  .translateBetweenLangs(
                                            pendidikanTerakhir ?? "SD",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "cek_diploma": TranslationService
                                              .translateBetweenLangs(
                                            selectedDiploma ?? "Tidak",
                                            "dcv.konfirmasi2",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "jenjang_pendidikan":
                                              TranslationService
                                                  .translateBetweenLangs(
                                            selectedValue ?? "SD",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                          "nama_sekolah_pendidikan":
                                              _namaLengkapController.text,
                                          "jurusan_pendidikan":
                                              _jurusanController.text,
                                          "tahun_mulai_pendidikan":
                                              _tahunMasukController.text,
                                          "tahun_selesai_pendidikan":
                                              _tahunLulusController.text,
                                          "ket_pendidikan": TranslationService
                                              .translateBetweenLangs(
                                            selectedKeterangan ??
                                                "Lulus Berijazah",
                                            "dcv.pendidikan",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                            "id",
                                          ),
                                        });
                                        formController
                                            .saveRiwayatPendidikan(data);
                                      }
                                      // }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 6),
                                      minimumSize: const Size.fromHeight(45),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                      textStyle: const TextStyle(fontSize: 14),
                                    ),
                                    child: Text(
                                      "tombol.simpan".tr,
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
