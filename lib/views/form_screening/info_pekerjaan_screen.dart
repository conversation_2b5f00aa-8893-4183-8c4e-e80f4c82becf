import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/provinsi_dialog.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/helpers/formater.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:get/get.dart';

class InfoPekerjaanScreen extends StatefulWidget {
  const InfoPekerjaanScreen({super.key});

  @override
  State<InfoPekerjaanScreen> createState() => _InfoPekerjaanScreenState();
}

class _InfoPekerjaanScreenState extends State<InfoPekerjaanScreen> {
  final FormController formController = Get.find<FormController>();
  final TextEditingController _gajiController = TextEditingController();
  final TextEditingController _lokasiController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<Map<String, dynamic>> data = [];
  String? selectedValue;
  String? gaji;
  String? lokasi;
  int? idLocation;
  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "dcv.info_kerja.txt_gaji_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.info_kerja.txt_lokasi_blank".tr,
      "isError": false,
    },
  ];

  @override
  void initState() {
    super.initState();

    selectedValue = "konfirmasi2.tidak".tr;
    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;

      if (args['dinas'] != null || args['dinas'] != "") {
        selectedValue = args['dinas'] != ""
            ? TranslationService.translateBetweenLangs(
                args['dinas'],
                "konfirmasi2",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id")
            : null;
      } else {
        selectedValue = "konfirmasi2.tidak".tr;
      }

      if (args['gaji'] != null || args['gaji'] != "") {
        gaji = args['gaji'];
      } else {
        gaji = null;
      }

      if (args['lokasi'] != null || args['lokasi'] != "") {
        lokasi = args['lokasi'];
      } else {
        lokasi = null;
      }

      _gajiController.text = gaji != null ? formatRupiah(gaji!) : "";
      _lokasiController.text = lokasi != null ? lokasi! : "";
    }
  }

  void _bukaPilihLokasi() async {
    final result = await Get.bottomSheet<LokasiModel>(
      LokasiBottomSheet(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );

    if (result != null) {
      _lokasiController.text =
          result.nama; // Set text field dengan nama provinsi
      idLocation = result.id as int;
      setState(() {});
      // Simpan result ke state
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.info_kerja.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "dcv.info_kerja.txt_dinas".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: DropdownButtonFormField<String>(
                            value: selectedValue,
                            isDense: true, // Mengurangi padding internal
                            isExpanded:
                                true, // Membuat dropdown mengisi lebar penuh
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 12), // Padding dalam Input
                              border: InputBorder.none,
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                            ), // Ukuran teks dropdown
                            iconSize: 24, // Ukuran icon dropdown
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedValue = newValue!;
                              });
                            },
                            items: [
                              'konfirmasi2.iya'.tr,
                              'konfirmasi2.tidak'.tr
                            ].map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Container(
                                  height: 40, // Mengatur tinggi dropdown item
                                  alignment: Alignment
                                      .centerLeft, // Menyamakan posisi teks
                                  child: Text(
                                    value,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          children: [
                            Text(
                              "dcv.info_kerja.txt_gaji".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 1, // Memberikan ruang lebih ke tombol
                              child: SizedBox(
                                height: 51,
                                child: ElevatedButton(
                                  onPressed: null,
                                  style: ElevatedButton.styleFrom(
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 6,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.horizontal(
                                        left: Radius.circular(6),
                                      ),
                                      side: BorderSide.none,
                                    ),
                                    textStyle: const TextStyle(fontSize: 14),
                                  ),
                                  child: const Text(
                                    "Rp",
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                                flex:
                                    4, // Memberikan lebih banyak ruang ke TextFormField
                                child: FormTextFiled(
                                  controller: _gajiController,
                                  inputFormatters: [
                                    CurrencyInputFormatter(
                                      thousandSeparator:
                                          ThousandSeparator.Period,
                                      mantissaLength: 0, // tanpa desimal
                                    )
                                  ],
                                  keyboardType: TextInputType.number,
                                  hintText: "dcv.info_kerja.txt_gaji".tr,
                                  type: "otp",
                                )),
                          ],
                        ),
                        if (errorsMessage[0]['isError'] != false)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    errorsMessage[0]['message'],
                                    style: TextStyle(
                                        color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          children: [
                            Text(
                              "dcv.info_kerja.txt_lokasi".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        FormTextFiled(
                          controller: _lokasiController,
                          onTap: () {
                            _bukaPilihLokasi();
                          },
                          keyboardType: TextInputType.text,
                          hintText: "dcv.info_kerja.txt_lokasi".tr,
                          isReadOnly: true,
                          maxLines: 1,
                          suffixIcon: Icon(
                            Icons.arrow_drop_down,
                            size: 30,
                          ),
                          errorText: errorsMessage[1]['isError']
                              ? errorsMessage[1]['message']
                              : null,
                        ),
                        SizedBox(
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(12),
              child: SizedBox(
                height: 50,
                child: Obx(
                  () => formController.isLoading.value
                      ? Center(
                          child: CircularProgressIndicator(),
                        )
                      : ElevatedButton(
                          onPressed: () {
                            if (_gajiController.text.isEmpty) {
                              setState(() {
                                errorsMessage[0]['isError'] = true;
                              });

                              Get.snackbar("", errorsMessage[0]['message'],
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              return;
                            }
                            if (_lokasiController.text.isEmpty) {
                              setState(() {
                                errorsMessage[1]['isError'] = true;
                              });
                              Get.snackbar("", errorsMessage[1]['message'],
                                  snackPosition: SnackPosition.BOTTOM,
                                  borderRadius: 10,
                                  margin: EdgeInsets.only(
                                      left: 15, right: 15, bottom: 70),
                                  backgroundColor: Colors.amber,
                                  duration: const Duration(seconds: 4),
                                  animationDuration:
                                      const Duration(milliseconds: 500),
                                  forwardAnimationCurve: Curves.easeOutBack,
                                  reverseAnimationCurve: Curves.easeInBack,
                                  colorText: Colors.black,
                                  titleText: SizedBox.shrink(),
                                  icon: Icon(
                                    Icons.warning,
                                    color: Colors.black,
                                  ),
                                  mainButton: TextButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    child: Text(
                                      "tombol.dismiss".tr,
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ));
                              return;
                            }

                            data.clear();
                            data.add({
                              "perjalanan_dinas":
                                  TranslationService.translateBetweenLangs(
                                selectedValue ?? "Tidak",
                                "konfirmasi",
                                Get.locale?.languageCode.toLowerCase() ?? "id",
                                "id",
                              ),
                              "minat_lokasi_kerja": _lokasiController.text,
                              "temp_gaji":
                                  toNumericString(_gajiController.text),
                            });
                            formController.saveInfoPekerjaan(data);
                          },
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 100,
                                vertical: 6,
                              ),
                              minimumSize: const Size.fromHeight(45),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50),
                              ),
                              textStyle: TextStyle(fontSize: 14)),
                          child: Text(
                            "tombol.simpan".tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
