import 'package:digital_cv_mobile/components/base/form_textfield.dart';
import 'package:digital_cv_mobile/components/month-year-picker/dialogs.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/formater.dart';
import 'package:digital_cv_mobile/models/pekerjaan_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:get/get.dart';

class PengalamanKerjaScreen extends StatefulWidget {
  const PengalamanKerjaScreen({super.key});

  @override
  State<PengalamanKerjaScreen> createState() => _PengalamanKerjaScreenState();
}

class _PengalamanKerjaScreenState extends State<PengalamanKerjaScreen> {
  final FormController formController = Get.find<FormController>();
  final TextEditingController _totPengalamanController =
      TextEditingController();
  // final TextEditingController _totPosisiSamaController =
  //     TextEditingController();
  final TextEditingController _totNamaPerusahaanController =
      TextEditingController();
  final TextEditingController _totJabatanController = TextEditingController();
  final TextEditingController _totPGajiController = TextEditingController();
  final TextEditingController _totTahunMasukController =
      TextEditingController();
  final TextEditingController _totTahunKeluarController =
      TextEditingController();
  final TextEditingController _totAlasanBerhentiController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late PekerjaanModel pekerjaanModel;
  String totPengalamanKerja = "";
  String totPosisiSama = "";
  List<Map<String, dynamic>> data = [];
  String? selectedValue;
  String? selectedStatusKerja;
  bool isStillWorking = false;
  DateTime? tempDateKeluar;

  Map<String, String> pengalamanOptions = {
    "Ya": "konfirmasi.ada".tr,
    "Tidak": "konfirmasi.tidak_ada".tr,
    "Fresh Graduate": "konfirmasi.freshgrad".tr,
  };

  Map<String, String> statusKerjaOptions = {
    "(Magang)": "dcv.pengalaman_kerja.status1".tr,
    "(Freelance)": "dcv.pengalaman_kerja.status2".tr,
    "(Full Time)": "dcv.pengalaman_kerja.status3".tr,
  };

  final List<Map<String, dynamic>> errorsMessage = [
    {
      "message": "dcv.pengalaman_kerja.txt_total_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_posisi_sama_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_nama_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_jabatan_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_status_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_gaji_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_thn_masuk_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_thn_keluar_blank".tr,
      "isError": false,
    },
    {
      "message": "dcv.pengalaman_kerja.txt_alasan_berhenti_blank".tr,
      "isError": false,
    },
  ];

  @override
  void initState() {
    super.initState();

    pekerjaanModel = PekerjaanModel(
      id: "",
      namaPerusahaan: "",
      jabatan: "",
      statusKerja: "",
      gaji: "",
      tahunMulai: "",
      tahunSelesai: "",
      alasanBerhenti: "",
    );
    selectedValue = null;
    selectedStatusKerja = null;
    if (Get.arguments != null) {
      final Map args = Get.arguments as Map;
      if (args['pekerjaan'] != null) {
        pekerjaanModel = args['pekerjaan'];
      } else {
        pekerjaanModel = PekerjaanModel(
          id: "",
          namaPerusahaan: "",
          jabatan: "",
          statusKerja: "",
          gaji: "",
          tahunMulai: "",
          tahunSelesai: "",
          alasanBerhenti: "",
        );
      }

      if (args['pengalaman'] != null || args['pengalaman'] != "") {
        selectedValue = args['pengalaman'] != "" ? args['pengalaman'] : null;
      } else {
        selectedValue = pekerjaanModel.namaPerusahaan != "" ? "Ya" : "Tidak";
      }

      if (args['tot_pengalaman'] != null || args['tot_pengalaman'] != "") {
        totPengalamanKerja = args['tot_pengalaman'];
      }

      // if (args['tot_pengalaman_posisi_sama'] != null ||
      //     args['tot_pengalaman_posisi_sama'] != "") {
      //   totPosisiSama = args['tot_pengalaman_posisi_sama'];
      // }

      _totPengalamanController.text =
          totPengalamanKerja != "" ? totPengalamanKerja.toString() : "";
      // _totPosisiSamaController.text =
      //     totPosisiSama != "" ? totPosisiSama.toString() : "";
      _totNamaPerusahaanController.text = pekerjaanModel.namaPerusahaan;
      _totJabatanController.text = pekerjaanModel.jabatan;
      selectedStatusKerja =
          pekerjaanModel.statusKerja != "" ? pekerjaanModel.statusKerja : null;
      _totPGajiController.text = formatRupiah(pekerjaanModel.gaji);
      _totTahunMasukController.text = pekerjaanModel.tahunMulai;
      _totTahunKeluarController.text = pekerjaanModel.tahunSelesai;
      _totAlasanBerhentiController.text = pekerjaanModel.alasanBerhenti;
    }
  }

  DateTime? selectedTahunMasuk;
  DateTime? selectedTahunKeluar;
  Future<void> _selectTahunMasuk() async {
    final DateTime? pickedDate = await showMonthYearPicker(
      context: context,
      initialDate: selectedTahunMasuk ?? DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 100),
      lastDate: DateTime(DateTime.now().year + 10),
      initialMonthYearPickerMode: MonthYearPickerMode.year,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            primaryColor: ColorAsset.primaryColor,
            colorScheme: ColorScheme.light(
              primary: ColorAsset.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
              secondary: ColorAsset.primaryColor,
              onSecondary: Colors.white,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.black,
              ),
            ),
            dialogBackgroundColor: Colors.white,
            chipTheme: ChipThemeData(
              backgroundColor: ColorAsset.primaryColor,
              selectedColor: ColorAsset.primaryColor,
              labelStyle: TextStyle(color: Colors.white),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        selectedTahunMasuk = pickedDate;
        _totTahunMasukController.text =
            "${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.year}";
      });
    }
  }

  Future<void> _selectTahunKeluar() async {
    final DateTime? pickedDate = await showMonthYearPicker(
      context: context,
      initialDate: selectedTahunKeluar ?? DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 100),
      lastDate: DateTime(DateTime.now().year + 10),
      initialMonthYearPickerMode: MonthYearPickerMode.year,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            primaryColor: ColorAsset.primaryColor,
            colorScheme: ColorScheme.light(
              primary: Colors.white,
              onPrimary: Colors.black,
              surface: Colors.white,
              onSurface: Colors.black,
              secondary: ColorAsset.primaryColor,
              onSecondary: Colors.white,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.black,
              ),
            ),
            dialogBackgroundColor: Colors.white,
            chipTheme: ChipThemeData(
              backgroundColor: ColorAsset.primaryColor,
              selectedColor: ColorAsset.primaryColor,
              labelStyle: TextStyle(color: Colors.white),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        selectedTahunKeluar = pickedDate;
        _totTahunKeluarController.text =
            "${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.year}";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.white,
        title: Text(
          "dcv.pengalaman_kerja.judul".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "dcv.pengalaman_kerja.txt_confirm_pengalaman".tr,
                              style: TextStyle(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              "*",
                              style: TextStyle(
                                color: Colors.red,
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: DropdownButtonFormField<String>(
                            value:
                                (selectedValue != null || selectedValue != "")
                                    ? selectedValue
                                    : null,
                            isDense: true,
                            hint: Text(
                              "dcv.pengalaman_kerja.txt_confirm_pengalaman".tr,
                            ),
                            isExpanded: true,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 12),
                              border: InputBorder.none,
                            ),
                            style: TextStyle(fontSize: 14, color: Colors.black),
                            iconSize: 24,
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedValue = newValue!;
                              });
                            },
                            items: pengalamanOptions.entries
                                .where((entry) => entry.key.isNotEmpty)
                                .map((entry) {
                              return DropdownMenuItem<String>(
                                value: entry.key,
                                child: Container(
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    entry.value,
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.black),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        selectedValue == "Ya"
                            ? Column(
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.pengalaman_kerja.txt_total".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                          flex:
                                              4, // Memberikan lebih banyak ruang ke TextFormField
                                          child: FormTextFiled(
                                            controller:
                                                _totPengalamanController,
                                            keyboardType: TextInputType.text,
                                            hintText:
                                                "dcv.pengalaman_kerja.txt_total"
                                                    .tr,
                                            type: "total_pengalaman",
                                          )),
                                      Expanded(
                                        flex:
                                            1, // Memberikan ruang lebih ke tombol
                                        child: Container(
                                          alignment: Alignment.center,
                                          height: 51,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[300],
                                            borderRadius:
                                                BorderRadius.horizontal(
                                              right: Radius.circular(6),
                                            ),
                                          ),
                                          child: Text(
                                            "dcv.pelatihan.txt_tahun".tr,
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (errorsMessage[0]['isError'] != false)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.error,
                                              color: Colors.red,
                                              size: 16,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              errorsMessage[0]['message'],
                                              style: TextStyle(
                                                  color: Colors.red,
                                                  fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Divider(
                                    thickness: 5,
                                    height: 5,
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  _buildTextField(
                                    "dcv.pengalaman_kerja.txt_nama".tr,
                                    errorsMessage[2]['isError']
                                        ? errorsMessage[2]['message']
                                        : '',
                                    _totNamaPerusahaanController,
                                    TextInputType.name,
                                    null,
                                    false,
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  _buildTextField(
                                    "dcv.pengalaman_kerja.txt_jabatan".tr,
                                    errorsMessage[3]['isError']
                                        ? errorsMessage[3]['message']
                                        : '',
                                    _totJabatanController,
                                    TextInputType.name,
                                    null,
                                    false,
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.pengalaman_kerja.txt_status".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: DropdownButtonFormField<String>(
                                      hint: Text(
                                          "dcv.pengalaman_kerja.txt_status".tr),
                                      value: (selectedStatusKerja != null ||
                                              selectedStatusKerja != "")
                                          ? selectedStatusKerja
                                          : null,
                                      isDense: true,
                                      isExpanded: true,
                                      decoration: InputDecoration(
                                        contentPadding: EdgeInsets.symmetric(
                                            vertical: 10, horizontal: 12),
                                        border: InputBorder.none,
                                      ),
                                      style: TextStyle(
                                          fontSize: 14, color: Colors.black),
                                      iconSize: 24,
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          selectedStatusKerja = newValue!;
                                        });
                                      },
                                      items: statusKerjaOptions.entries
                                          .where((entry) =>
                                              entry.key.isNotEmpty ||
                                              entry.key != "")
                                          .map((entry) {
                                        return DropdownMenuItem<String>(
                                          value: entry.key,
                                          child: Container(
                                            padding: EdgeInsets.only(left: 10),
                                            height: 40,
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              entry.value,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                  if (errorsMessage[4]['isError'] != false)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.error,
                                              color: Colors.red,
                                              size: 16,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              errorsMessage[4]['message'],
                                              style: TextStyle(
                                                  color: Colors.red,
                                                  fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "dcv.pengalaman_kerja.txt_gaji".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    ],
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        flex:
                                            1, // Memberikan ruang lebih ke tombol
                                        child: SizedBox(
                                          height: 50,
                                          child: ElevatedButton(
                                            onPressed: null,
                                            style: ElevatedButton.styleFrom(
                                              elevation: 0,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 20,
                                                vertical: 6,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.horizontal(
                                                  left: Radius.circular(6),
                                                ),
                                                side: BorderSide.none,
                                              ),
                                              textStyle:
                                                  const TextStyle(fontSize: 14),
                                            ),
                                            child: const Text(
                                              "Rp",
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                          flex:
                                              4, // Memberikan lebih banyak ruang ke TextFormField
                                          child: FormTextFiled(
                                            controller: _totPGajiController,
                                            inputFormatters: [
                                              CurrencyInputFormatter(
                                                thousandSeparator:
                                                    ThousandSeparator.Period,
                                                mantissaLength:
                                                    0, // tanpa desimal
                                              )
                                            ],
                                            keyboardType: TextInputType.number,
                                            hintText:
                                                "dcv.pengalaman_kerja.txt_gaji"
                                                    .tr,
                                            type: "otp",
                                          )),
                                    ],
                                  ),
                                  if (errorsMessage[5]['isError'] != false)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.error,
                                              color: Colors.red,
                                              size: 16,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              errorsMessage[5]['message'],
                                              style: TextStyle(
                                                  color: Colors.red,
                                                  fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.pengalaman_kerja.txt_thn_masuk"
                                                      .tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 4),
                                            FormTextFiled(
                                              controller:
                                                  _totTahunMasukController,
                                              onTap: () {
                                                _selectTahunMasuk();
                                              },
                                              hintText:
                                                  "dcv.pengalaman_kerja.txt_thn_masuk"
                                                      .tr,
                                              keyboardType: TextInputType.text,
                                              isReadOnly: true,
                                              maxLines: 1,
                                              suffixIcon: Icon(
                                                Icons.calendar_month_outlined,
                                                size: 30,
                                                color: Colors.grey[500],
                                              ),
                                              errorText: errorsMessage[6]
                                                      ['isError']
                                                  ? errorsMessage[6]['message']
                                                  : null,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 16), // Jarak antar kolom
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  "dcv.pengalaman_kerja.txt_thn_keluar"
                                                      .tr,
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                                Text(
                                                  "*",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 4),
                                            FormTextFiled(
                                              controller:
                                                  _totTahunKeluarController,
                                              onTap: () {
                                                _selectTahunKeluar();
                                              },
                                              hintText:
                                                  "dcv.pengalaman_kerja.txt_thn_keluar"
                                                      .tr,
                                              keyboardType: TextInputType.text,
                                              isReadOnly: true,
                                              maxLines: 1,
                                              suffixIcon: Icon(
                                                Icons.calendar_month_outlined,
                                                size: 30,
                                                color: Colors.grey[500],
                                              ),
                                              errorText: errorsMessage[7]
                                                      ['isError']
                                                  ? errorsMessage[7]['message']
                                                  : null,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Checkbox(
                                          value: isStillWorking,
                                          onChanged: (bool? value) {
                                            setState(() {
                                              isStillWorking = value!;
                                              if (value) {
                                                tempDateKeluar =
                                                    selectedTahunKeluar;
                                                selectedTahunKeluar =
                                                    DateTime.now();
                                              } else {
                                                selectedTahunKeluar =
                                                    DateTime.now();
                                              }
                                              _totTahunKeluarController.text =
                                                  "${selectedTahunKeluar?.month.toString().padLeft(2, '0')}-${selectedTahunKeluar?.year}";
                                            });
                                          },
                                        ),
                                        Text(
                                          "dcv.pengalaman_kerja.txt_still_work"
                                              .tr,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  _buildTextField(
                                    "dcv.pengalaman_kerja.txt_alasan_berhenti"
                                        .tr,
                                    errorsMessage[8]['isError']
                                        ? errorsMessage[8]['message']
                                        : '',
                                    _totAlasanBerhentiController,
                                    TextInputType.multiline,
                                    null,
                                    false,
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                ],
                              )
                            : SizedBox()
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      if (pekerjaanModel.namaPerusahaan != "") ...[
                        Expanded(
                          child: SizedBox(
                            height: 45,
                            child: Obx(
                              () => formController.isLoading.value
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : ElevatedButton(
                                      onPressed: () async {
                                        formController.deleteRiwayatPekerjaan(
                                            pekerjaanModel.id);
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        elevation: 0,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 6),
                                        minimumSize: const Size.fromHeight(45),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                        textStyle:
                                            const TextStyle(fontSize: 14),
                                      ),
                                      child: Text(
                                        "tombol.hapus".tr,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Expanded(
                        child: SizedBox(
                          height: 50,
                          child: Obx(
                            () => formController.isLoading.value
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : ElevatedButton(
                                    onPressed: () {
                                      if (selectedValue == "Ya") {
                                        for (var i = 0;
                                            i < errorsMessage.length;
                                            i++) {
                                          setState(() {
                                            errorsMessage[i]['isError'] = false;
                                          });
                                        }
                                        if (_totPengalamanController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[0]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[0]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totNamaPerusahaanController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[2]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[2]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totJabatanController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[3]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[3]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (selectedStatusKerja == null ||
                                            selectedStatusKerja == "") {
                                          setState(() {
                                            errorsMessage[4]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[4]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totPGajiController.text.isEmpty) {
                                          setState(() {
                                            errorsMessage[5]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[5]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totTahunMasukController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[6]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[6]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totTahunKeluarController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[7]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[7]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        if (_totAlasanBerhentiController
                                            .text.isEmpty) {
                                          setState(() {
                                            errorsMessage[8]['isError'] = true;
                                          });
                                          Get.snackbar(
                                              "", errorsMessage[8]['message'],
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              borderRadius: 10,
                                              margin: EdgeInsets.only(
                                                  left: 15,
                                                  right: 15,
                                                  bottom: 70),
                                              backgroundColor: Colors.amber,
                                              duration:
                                                  const Duration(seconds: 4),
                                              animationDuration: const Duration(
                                                  milliseconds: 500),
                                              forwardAnimationCurve:
                                                  Curves.easeOutBack,
                                              reverseAnimationCurve:
                                                  Curves.easeInBack,
                                              colorText: Colors.black,
                                              titleText: SizedBox.shrink(),
                                              icon: Icon(
                                                Icons.warning,
                                                color: Colors.black,
                                              ),
                                              mainButton: TextButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                child: Text(
                                                  "tombol.dismiss".tr,
                                                  style: TextStyle(
                                                      color: Colors.black),
                                                ),
                                              ));
                                          return;
                                        }
                                        setState(() {
                                          for (var element in errorsMessage) {
                                            element['isError'] = false;
                                          }
                                        });

                                        // Validasi tahun masuk vs tahun keluar
                                        if (selectedTahunMasuk != null &&
                                            selectedTahunKeluar != null) {
                                          // Convert to comparable format (YYYY-MM)
                                          String tahunMasukStr =
                                              _totTahunMasukController.text;
                                          String tahunKeluarStr =
                                              _totTahunKeluarController.text;

                                          if (tahunMasukStr.isNotEmpty &&
                                              tahunKeluarStr.isNotEmpty) {
                                            // Parse MM-YYYY format to DateTime for comparison
                                            List<String> masukParts =
                                                tahunMasukStr.split('-');
                                            List<String> keluarParts =
                                                tahunKeluarStr.split('-');

                                            if (masukParts.length == 2 &&
                                                keluarParts.length == 2) {
                                              int masukMonth =
                                                  int.parse(masukParts[0]);
                                              int masukYear =
                                                  int.parse(masukParts[1]);
                                              int keluarMonth =
                                                  int.parse(keluarParts[0]);
                                              int keluarYear =
                                                  int.parse(keluarParts[1]);

                                              DateTime masukDate = DateTime(
                                                  masukYear, masukMonth);
                                              DateTime keluarDate = DateTime(
                                                  keluarYear, keluarMonth);

                                              if (masukDate
                                                  .isAfter(keluarDate)) {
                                                Get.snackbar(
                                                    "",
                                                    "controller.tahun_masuk_lebih"
                                                        .tr,
                                                    snackPosition:
                                                        SnackPosition.BOTTOM,
                                                    borderRadius: 10,
                                                    margin: EdgeInsets.only(
                                                        left: 15,
                                                        right: 15,
                                                        bottom: 70),
                                                    backgroundColor:
                                                        Colors.amber,
                                                    duration: const Duration(
                                                        seconds: 4),
                                                    animationDuration:
                                                        const Duration(
                                                            milliseconds: 500),
                                                    forwardAnimationCurve:
                                                        Curves.easeOutBack,
                                                    reverseAnimationCurve:
                                                        Curves.easeInBack,
                                                    colorText: Colors.black,
                                                    titleText:
                                                        SizedBox.shrink(),
                                                    icon: Icon(
                                                      Icons.warning,
                                                      color: Colors.black,
                                                    ),
                                                    mainButton: TextButton(
                                                      onPressed: () {
                                                        Get.back();
                                                      },
                                                      child: Text(
                                                        "tombol.dismiss".tr,
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black),
                                                      ),
                                                    ));
                                                return;
                                              }
                                            }
                                          }
                                        }
                                      }

                                      data.clear();
                                      if (pekerjaanModel.namaPerusahaan != "") {
                                        data.add({
                                          "id_pekerjaan": pekerjaanModel.id,
                                          "pengalaman_kerja": selectedValue,
                                          "total_pengalaman_kerja":
                                              _totPengalamanController.text,
                                          "pengalaman_posisi_sama": '',
                                          "nama":
                                              _totNamaPerusahaanController.text,
                                          "jabatan": _totJabatanController.text,
                                          "status": selectedStatusKerja,
                                          "gaji": toNumericString(
                                              _totPGajiController.text),
                                          "tahun_mulai":
                                              _totTahunMasukController.text,
                                          "tahun_selesai":
                                              _totTahunKeluarController.text,
                                          "alasan":
                                              _totAlasanBerhentiController.text,
                                        });
                                        formController
                                            .updateRiwayatPekerjaan(data);
                                      } else {
                                        data.add({
                                          "pengalaman_kerja": selectedValue,
                                          "total_pengalaman_kerja":
                                              _totPengalamanController.text,
                                          "pengalaman_posisi_sama": '',
                                          "nama":
                                              _totNamaPerusahaanController.text,
                                          "jabatan": _totJabatanController.text,
                                          "status": selectedStatusKerja,
                                          "gaji": toNumericString(
                                              _totPGajiController.text),
                                          "tahun_mulai":
                                              _totTahunMasukController.text,
                                          "tahun_selesai":
                                              _totTahunKeluarController.text,
                                          "alasan":
                                              _totAlasanBerhentiController.text,
                                        });
                                        formController
                                            .saveRiwayatPekerjaan(data);
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 6),
                                      minimumSize: const Size.fromHeight(45),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                      textStyle: const TextStyle(fontSize: 14),
                                    ),
                                    child: Text(
                                      "tombol.simpan".tr,
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String hintText,
      String validator,
      TextEditingController controller,
      TextInputType inputType,
      IconData? icon,
      bool isReadOnly) {
    return isReadOnly
        ? FormTextFiled(
            title: hintText,
            isReadOnly: isReadOnly,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            minLines: 1,
            maxLines: null,
          )
        : FormTextFiled(
            title: hintText,
            controller: controller,
            keyboardType: inputType,
            hintText: hintText,
            errorText: validator != "" ? validator : null,
            prefixIcon: icon != null ? Icon(icon) : null,
            isReadOnly: isReadOnly,
            minLines: 1,
            maxLines: null,
            isRequired: true,
            type: hintText,
          );
  }
}
