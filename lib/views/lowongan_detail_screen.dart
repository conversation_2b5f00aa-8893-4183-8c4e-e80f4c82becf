import 'package:digital_cv_mobile/components/dialog_confirmation.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/lamaran_controller.dart';
import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/models/lowongan_model.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/views/apply_lowongan/page_view_apply.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:html_unescape/html_unescape.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class LowonganDetailScreen extends StatefulWidget {
  const LowonganDetailScreen({super.key});

  @override
  State<LowonganDetailScreen> createState() => _LowonganDetailScreenState();
}

class _LowonganDetailScreenState extends State<LowonganDetailScreen>
    with SingleTickerProviderStateMixin {
  final LowonganController lowonganController = Get.find<LowonganController>();
  final LamaranController lamaranController = Get.put(LamaranController());
  final MinatKonsepController minatKonsepController =
      Get.find<MinatKonsepController>();
  late ScrollController _scrollController;
  bool _showFloatingButton = false;
  final GlobalKey _buttonKey = GlobalKey(); // Key untuk tombol asli
  late AnimationController _animationController;
  // late Animation<Offset> _slideAnimation;
  late LowonganModel lowongan;
  late final Set<String> checkFields;
  final unescape = HtmlUnescape();
  int _selectedTab = 0; // Tab yang dipilih, default ke 0 (Deskripsi)

  @override
  void initState() {
    super.initState();
    // Validasi Get.arguments
    // if (Get.arguments != null && Get.arguments is LowonganModel) {
    //   lowongan = Get.arguments as LowonganModel;
    // } else {
    //   // Jika tidak valid, tampilkan notifikasi dan kembali ke halaman sebelumnya
    //   // Future.microtask(() {
    //   //   Get.snackbar('Error', 'Data lowongan tidak ditemukan');
    //   //   Get.back();
    //   // });
    // }
    final args = Get.arguments;
    if (args is LowonganModel) {
      lowongan = args;
      checkFields = (lowongan.checkLowongan ?? "").split("|").toSet();
    } else {
      // Kalau null atau salah tipe, langsung keluarin alert dan back
      Future.microtask(() {
        showAnimatedSnackbarError(context, 'error.detail_lowongan_empty'.tr);
        Get.back();
      });
    }

    minatKonsepController.getRuangLingkup();

    _scrollController = ScrollController();
    _scrollController.addListener(_checkButtonVisibility);

    // Animasi untuk slide dari bawah ke atas
    _animationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 300));
    // _slideAnimation = Tween<Offset>(begin: Offset(0, 1), end: Offset(0, 0))
    //     .animate(CurvedAnimation(
    //   parent: _animationController,
    //   curve: Curves.easeOut,
    // ));

    // **Cek visibilitas tombol saat pertama kali layar dibuka**
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkButtonVisibility();
    });
  }

  void _checkButtonVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final RenderBox? renderBox =
          _buttonKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final double position = renderBox.localToGlobal(Offset.zero).dy;
        final double screenHeight = MediaQuery.of(context).size.height;

        bool shouldShow = position > screenHeight;

        if (shouldShow != _showFloatingButton) {
          setState(() {
            _showFloatingButton = shouldShow;
          });
          if (shouldShow) {
            _animationController.forward(); // Munculkan dengan animasi
          } else {
            _animationController.reverse(); // Sembunyikan dengan animasi
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  //atur permission internet, query all package, dan intent filter di manifest
  void launchMap(String address) async {
    String query = Uri.encodeFull(address);
    Uri googleUrl =
        Uri.parse("https://www.google.com/maps/search/?api=1&query=$query");

    if (await canLaunchUrl(googleUrl)) {
      await launchUrl(
        googleUrl,
        mode: LaunchMode.externalApplication,
      );
    } else {
      showAnimatedSnackbarError(context, "error.location_empty".tr);
    }
  }

  bool isValidImageUrl(String url) {
    if (url.isEmpty) return false;
    final uri = Uri.tryParse(url);
    return uri != null &&
        uri.hasAbsolutePath &&
        (uri.isScheme("http") || uri.isScheme("https"));
  }

  @override
  Widget build(BuildContext context) {
    // final LowonganModel? lowongan =
    //     Get.arguments is LowonganModel ? Get.arguments as LowonganModel : null;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              lowongan.posisi,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Material(
              color: Colors.transparent, // Tambahkan warna agar InkWell bekerja
              child: InkWell(
                onTap: () {
                  Get.toNamed(Routes.perusahaan, arguments: lowongan);
                },
                child: Text(
                  lowongan.perusahaan,
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 11,
                    color: const Color.fromRGBO(0, 0, 0, 1),
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                InkWell(
                  borderRadius: BorderRadius.circular(100),
                  onTap: () async {
                    final prefs = Get.find<SharedPreferences>();
                    final isLoggedIn = prefs.getBool("auth") ?? false;
                    final isGoogle = prefs.getBool("isGoogle") ?? false;
                    if (isLoggedIn || isGoogle) {
                      // Ubah status favorit dulu (optimistic update)
                      lowongan.isFavorit = !lowongan.isFavorit;

                      // Trigger rebuild agar icon berubah
                      SchedulerBinding.instance.addPostFrameCallback((_) {
                        setState(() {});
                      });

                      // Simpan ke backend

                      final success = await lowonganController.saveLowongan(
                        lowongan.tempKode,
                        lowongan.isFavorit,
                      );
                      if (success) {
                        await Future.delayed(const Duration(milliseconds: 300));
                        lowonganController.triggerRefreshTersimpan();
                        // Refresh Beranda (karena list tersimpan berubah)
                        lowonganController.triggerRefreshBeranda();
                      }
                    } else {
                      var confirm = await showConfirmationDialog(
                          context,
                          "dialog_out.explore_judul".tr,
                          "dialog_out.explore_simpan_lowongan".tr,
                          "konfirmasi.iya2".tr,
                          "konfirmasi.tidak".tr);
                      if (confirm == true) {
                        Get.offAllNamed('/login');
                      }
                    }
                  },
                  child: lowongan.isFavorit
                      ? Icon(
                          Icons.bookmark_added,
                          color: ColorAsset.primaryColor,
                        )
                      : Icon(Icons.bookmark_added_outlined),
                ),
                SizedBox(
                  width: 10,
                ),
                InkWell(
                  borderRadius: BorderRadius.circular(100),
                  onTap: () async {
                    final result = await Share.shareWithResult(
                      '${"share.cek".tr} http://digitalcv.id/candidate/dashboard/beranda/detailJob?q=${lowongan.tempKode}',
                      subject: 'Digital CV - Lowongan Pekerjaan',
                    );

                    if (result.status == ShareResultStatus.success) {
                      debugPrint('Thank you for sharing my website!');
                      showAnimatedSnackbarSuccess(
                          context, 'share.terima_kasih'.tr);
                    } else {
                      debugPrint('Sharing was not successful.');
                    }
                  },
                  child: Icon(Icons.share),
                )
              ],
            ),
          )
        ],
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Color(0xFFFFE31D),
                      shape: BoxShape.rectangle,
                      border: null,
                    ),
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.center, // Menengahkan teks
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.warning,
                              size: 15,
                            ),
                            Text(
                              "detail_lowongan.perhatian".tr,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          ],
                        ),
                        Text(
                          "${lowongan.perusahaan} ${"detail_lowongan.perhatian_desc".tr}",
                          style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.normal,
                              color: Colors.black),
                          maxLines: 5,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.justify,
                          softWrap: true,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  cardInfoDetail(),
                  SizedBox(
                    height: 10,
                  ),
                  // Tab menu
                  tabMenu(),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_selectedTab == 0) buildMenuTab1(),
                        if (_selectedTab == 1) buildMenuTab2(),
                        if (_selectedTab == 2) buildMenuTab3(),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Divider(
                    thickness: 7,
                    height: 7,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Text(
                            "detail_lowongan.tentang_perusahaan".tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              Get.toNamed(Routes.perusahaan,
                                  arguments: lowongan);
                            },
                            child: Container(
                              height: 50,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Row(
                                children: [
                                  if (isValidImageUrl(lowongan.logoURL) &&
                                      lowongan.logoURL.isNotEmpty)
                                    Expanded(
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Image.network(
                                          lowongan.logoURL,
                                          height: 50,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                            return Image.asset(
                                              ImageAssets.logo2,
                                              fit: BoxFit.cover,
                                              width: 50,
                                              height: 50,
                                            );
                                          },
                                        ),
                                      ),
                                    )
                                  else
                                    Expanded(
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Image.asset(
                                          ImageAssets.logo2,
                                          fit: BoxFit.cover,
                                          width: 50,
                                          height: 50,
                                        ),
                                      ),
                                    ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    flex: 5,
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            lowongan.perusahaan,
                                            style: TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            TranslationService
                                                .translateBetweenLangs(
                                              lowongan.tipePerusahaan,
                                              "spesialisasi",
                                              "id",
                                              Get.locale?.languageCode
                                                      .toLowerCase() ??
                                                  "id",
                                            ),
                                            style: TextStyle(
                                              fontSize: 10,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.centerRight,
                                      child: Icon(Icons.keyboard_arrow_right),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              launchMap(lowongan.alamatPerusahaan);
                            },
                            child: Container(
                              height: 75,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.center, // Tambahkan ini
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        "detail_lowongan.alamat".tr,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Icon(
                                        Icons.location_on,
                                        size: 20,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: 8), // Memberi sedikit jarak
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      lowongan.alamatPerusahaan,
                                      style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.normal,
                                          color: Colors.black),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.start,
                                      softWrap: true,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Divider(
                    thickness: 7,
                    height: 7,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  // Container(
                  //   width: MediaQuery.of(context).size.width,
                  //   key: _buttonKey, // Key untuk tombol asli
                  //   padding: EdgeInsets.all(10),
                  //   child: lowongan.isLamar
                  //       ? ElevatedButton(
                  //           onPressed: () {},
                  //           style: ElevatedButton.styleFrom(
                  //             padding: EdgeInsets.symmetric(vertical: 15),
                  //             backgroundColor: Colors.grey,
                  //             shape: RoundedRectangleBorder(
                  //               borderRadius: BorderRadius.circular(50),
                  //             ),
                  //           ),
                  //           child: Text("tombol.sudah_lamar".tr),
                  //         )
                  //       : ElevatedButton(
                  //           onPressed: () async {
                  //             var result =
                  //                 await showDialogApply(context, lowongan);

                  //             if (result == true) {
                  //               lowongan.isLamar = true;
                  //               setState(() {});
                  //               lowonganController.triggerRefreshTersimpan();
                  //               lowonganController.triggerRefreshBeranda();
                  //               lamaranController.triggerRefreshRiwayatLamar();
                  //             }
                  //           },
                  //           style: ElevatedButton.styleFrom(
                  //             padding: EdgeInsets.symmetric(vertical: 15),
                  //             shape: RoundedRectangleBorder(
                  //               borderRadius: BorderRadius.circular(50),
                  //             ),
                  //           ),
                  //           child: Text("tombol.lamar".tr),
                  //         ),
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(color: Colors.white),
        child: lowongan.isLamar
            ? ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 15),
                  backgroundColor: Colors.grey,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                child: Text("tombol.sudah_lamar".tr),
              )
            : ElevatedButton(
                onPressed: () async {
                  final prefs = Get.find<SharedPreferences>();
                  final isLoggedIn = prefs.getBool("auth") ?? false;
                  final isGoogle = prefs.getBool("isGoogle") ?? false;
                  if (isLoggedIn || isGoogle) {
                    var result = await showDialogApply(context, lowongan);

                    if (result == true) {
                      lowongan.isLamar = true;
                      setState(() {});
                      lowonganController.triggerRefreshTersimpan();
                      lowonganController.triggerRefreshBeranda();
                      lamaranController.triggerRefreshRiwayatLamar();
                    }
                  } else {
                    var confirm = await showConfirmationDialog(
                        context,
                        "dialog_out.explore_judul".tr,
                        "dialog_out.explore_subjudul".tr,
                        "konfirmasi.iya2".tr,
                        "konfirmasi.tidak".tr);
                    if (confirm == true) {
                      Get.offAllNamed('/login');
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                child: Text(
                  "tombol.lamar".tr,
                  style: TextStyle(color: Colors.black, fontSize: 14),
                ),
              ),
      ),
    );
  }

  Padding tabMenu() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Center(
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Tab 1: Deskripsi Pekerjaan
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTab = 0;
                  });
                },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 250),
                  curve: Curves.easeInOut,
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Column(
                    children: [
                      AnimatedDefaultTextStyle(
                        duration: Duration(milliseconds: 250),
                        style: TextStyle(
                          fontWeight: _selectedTab == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: _selectedTab == 0
                              ? Color(0xFF0B3978)
                              : Colors.grey,
                          fontSize: 14,
                        ),
                        child: Text("detail_lowongan.desc_pekerjaan".tr),
                      ),
                      SizedBox(height: 4),
                      // Garis bawah lebih panjang dari teks
                      AnimatedContainer(
                        duration: Duration(milliseconds: 250),
                        height: 3,
                        width: 135, // Lebih panjang dari text
                        margin: EdgeInsets.symmetric(horizontal: 0),
                        color: _selectedTab == 0
                            ? Color(0xFF0B3978)
                            : Colors.transparent,
                      ),
                    ],
                  ),
                ),
              ),
              // SizedBox(width: 5),
              // Tab 2: Kriteria Umum
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTab = 1;
                  });
                },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 250),
                  curve: Curves.easeInOut,
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Column(
                    children: [
                      AnimatedDefaultTextStyle(
                        duration: Duration(milliseconds: 250),
                        style: TextStyle(
                          fontWeight: _selectedTab == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: _selectedTab == 1
                              ? Color(0xFF0B3978)
                              : Colors.grey,
                          fontSize: 14,
                        ),
                        child: Text("detail_lowongan.kualifikasi".tr),
                      ),
                      SizedBox(height: 4),
                      AnimatedContainer(
                        duration: Duration(milliseconds: 250),
                        height: 3,
                        width: 120, // Lebih panjang dari text
                        margin: EdgeInsets.symmetric(horizontal: 0),
                        color: _selectedTab == 1
                            ? Color(0xFF0B3978)
                            : Colors.transparent,
                      ),
                    ],
                  ),
                ),
              ),
              // SizedBox(width: 5),
              // Tab 3: Kriteria Khusus
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTab = 2;
                  });
                },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 250),
                  curve: Curves.easeInOut,
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Column(
                    children: [
                      AnimatedDefaultTextStyle(
                        duration: Duration(milliseconds: 250),
                        style: TextStyle(
                          fontWeight: _selectedTab == 2
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: _selectedTab == 2
                              ? Color(0xFF0B3978)
                              : Colors.grey,
                          fontSize: 14,
                        ),
                        child: Text("detail_lowongan.kriteria".tr),
                      ),
                      SizedBox(height: 4),
                      AnimatedContainer(
                        duration: Duration(milliseconds: 250),
                        height: 3,
                        width: 120, // Lebih panjang dari text
                        margin: EdgeInsets.symmetric(horizontal: 0),
                        color: _selectedTab == 2
                            ? Color(0xFF0B3978)
                            : Colors.transparent,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget cardInfoDetail() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (isValidImageUrl(lowongan.logoURL) && lowongan.logoURL.isNotEmpty)
            Image.network(
              lowongan.logoURL,
              height: 100,
              errorBuilder: (context, error, stackTrace) {
                return Image.asset(
                  ImageAssets.logo2,
                  fit: BoxFit.cover,
                  width: 100,
                  height: 100,
                );
              },
            )
          else
            Image.asset(
              ImageAssets.logo2,
              fit: BoxFit.cover,
              width: 100,
              height: 100,
            ),
          SizedBox(height: 10),
          Text(
            lowongan.posisi,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 5),
          Text(
            lowongan.perusahaan,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade500,
            ),
          ),
          Divider(
            thickness: 1,
            height: 20,
          ),
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      lowongan.lokasiKerja2,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[400]),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        TranslationService.translateBetweenLangs(
                            lowongan.tipePekerjaan,
                            "jenis_pekerjaan",
                            "id",
                            Get.locale?.languageCode.toLowerCase() ?? "id"),
                        style: const TextStyle(
                          fontSize: 12,
                        ),
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      "${"detail_lowongan.diposting".tr} ${TranslationService.translateTimeAgo(lowongan.waktuLalu)}",
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ]),
        ],
      ),
    );
  }

  Widget buildMenuTab1() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "detail_lowongan.desc_pekerjaan".tr,
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 5,
        ),
        Html(
          data: unescape.convert(lowongan.note),
          style: {
            "body": Style(
              fontSize: FontSize(12),
              color: Colors.black,
              fontWeight: FontWeight.normal,
              padding: HtmlPaddings.zero,
              margin: Margins.zero,
              textAlign: TextAlign.start,
            ),
            "p": Style(
              fontSize: FontSize(12),
              color: Colors.black,
              fontWeight: FontWeight.normal,
              padding: HtmlPaddings.zero,
              margin: Margins.zero,
              textAlign: TextAlign.start,
            ),
            "ol": Style(
              fontSize: FontSize(12),
              color: Colors.black,
              fontWeight: FontWeight.normal,
              padding: HtmlPaddings.symmetric(horizontal: 15),
              margin: Margins.zero,
              textAlign: TextAlign.start,
            ),
            "li": Style(
                padding: HtmlPaddings.only(left: 0),
                margin: Margins.only(bottom: 4, left: 0),
                textAlign: TextAlign.start),
          },
        ),
        SizedBox(
          height: 10,
        ),
      ],
    );
  }

  Widget buildMenuTab2() {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        "detail_lowongan.kualifikasi".tr,
        style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      SizedBox(
        height: 5,
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (var item in [
            if (lowongan.sistemKerja != "" &&
                checkFields.contains("sistem_kerja"))
              "${"detail_lowongan.traveling".tr}: ${TranslationService.translateBetweenLangs(
                lowongan.sistemKerja,
                "detail_lowongan",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id",
              )}",
            if (lowongan.kPendidikan != "" &&
                checkFields.contains("k_pendidikan"))
              "${"detail_lowongan.pendidikan".tr}: ${TranslationService.translateBetweenLangs(
                lowongan.kPendidikan,
                "dcv.pendidikan",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id",
              )}",
            if (lowongan.kJurusan != "" && checkFields.contains("k_jurusan"))
              "${"detail_lowongan.jurusan".tr}: ${lowongan.kJurusan.replaceAll("|", ", ")}",
            if (lowongan.kUsia != "" && checkFields.contains("k_usia"))
              "${"detail_lowongan.usia".tr}: ${lowongan.kUsia} ${"detail_lowongan.var_usia".tr}",
            if (lowongan.kSim != "" ||
                lowongan.kSim != "Tidak Ada" ||
                checkFields.contains("sim")) // misal kamu simpan sebagai 'sim'
              "${"detail_lowongan.sim".tr}: ${lowongan.kSim}",
            if (lowongan.kJk != "" &&
                checkFields.contains("k_jk")) // jika kamu simpan jk
              "${"detail_lowongan.jk".tr}: ${TranslationService.translateBetweenLangs(
                lowongan.kJk,
                "dcv.info_pribadi",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id",
              )}",
            if (checkFields.contains("k_pengalaman"))
              lowongan.kPengalaman != "0"
                  ? "${"detail_lowongan.p_kerja".tr}: ${lowongan.kPengalaman} ${"detail_lowongan.tahun".tr}"
                  : "${"detail_lowongan.p_kerja".tr}: ${"konfirmasi.freshgrad".tr}",
            if (lowongan.kPengalamanSama != "0" &&
                checkFields.contains("k_pengalaman_sama"))
              "${"detail_lowongan.p_kerja_bidang".tr}: ${lowongan.kPengalamanSama} ${"detail_lowongan.tahun".tr}",
            if (lowongan.kStatus != "" &&
                lowongan.kStatus != "Tidak" &&
                checkFields.contains("k_status"))
              "${"detail_lowongan.status".tr}: ${TranslationService.translateBetweenLangs(
                lowongan.kStatus,
                "status_marital",
                "id",
                Get.locale?.languageCode.toLowerCase() ?? "id",
              )}",
          ])
            Text(
              "• $item",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.normal,
                color: Colors.black,
              ),
              maxLines: 5,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.left,
              softWrap: true,
            ),
        ],
      ),
    ]);
  }

  Widget buildMenuTab3() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "detail_lowongan.kriteria".tr,
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 5,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bahasa yang Dikuasai
            if (lowongan.kb != "" && checkFields.contains("kb"))
              // Text(
              //   "• ${"detail_lowongan.bahasa".tr}: ${TranslationService.translateBetweenLangs(
              //     lowongan.kb,
              //     "bahasa.list",
              //     "id",
              //     Get.locale?.languageCode.toLowerCase() ??
              //         "id",
              //   )}",
              //   style: TextStyle(fontSize: 12),
              // ),
              Text(
                "• ${"detail_lowongan.bahasa".tr}: ${lowongan.kb.split(',').map((e) {
                  return TranslationService.translateBetweenLangs(
                    e.trim(), // trim untuk menghapus spasi berlebih
                    "bahasa.list",
                    "id",
                    Get.locale?.languageCode.toLowerCase() ?? "id",
                  );
                }).join(', ')}",
                style: TextStyle(fontSize: 12),
              ),

            // Komputerisasi
            if (lowongan.kk != "" && checkFields.contains("kk")) ...[
              Text(
                "• ${"detail_lowongan.komputerisasi".tr}:",
                style: TextStyle(
                  fontSize: 12,
                ),
              ),
              ...lowongan.kk
                  .split(',')
                  .map((e) => int.tryParse(e.trim()))
                  .whereType<int>()
                  .map((id) {
                int index = lowongan.kk
                    .split(',')
                    .map((e) => int.tryParse(e.trim()))
                    .whereType<int>()
                    .toList()
                    .indexOf(id);
                return Row(
                  children: [
                    SizedBox(
                      width: 7,
                    ),
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              "${index + 1}. ",
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                          Expanded(
                            flex: 20,
                            child: Text(
                              minatKonsepController.komputerisasiOptions[id] ??
                                  '-',
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                );
              }),
            ],

            if (lowongan.km != "" && checkFields.contains("km"))
              Text(
                "• ${"detail_lowongan.memimpin".tr}: ${minatKonsepController.getMemimpinText(lowongan.km)}",
                style: TextStyle(fontSize: 12),
              ),

            // Kemampuan Berbicara di Depan Umum
            if (lowongan.kbdu != "" && checkFields.contains("kbdu"))
              Text(
                "• ${"detail_lowongan.presentasi".tr}: ${minatKonsepController.getPenilaianText(lowongan.kbdu)}",
                style: TextStyle(fontSize: 12),
              ),

            // Ruang Lingkup Pekerjaan
            if (lowongan.rlp != "" && checkFields.contains("rlp")) ...[
              Text(
                "• ${"detail_lowongan.rlp".tr}:",
                style: TextStyle(
                  fontSize: 12,
                ),
              ),
              ...lowongan.rlp
                  .split(',')
                  .map((e) => int.tryParse(e.trim()))
                  .whereType<int>()
                  .toList()
                  .asMap()
                  .entries
                  .map((entry) {
                final index = entry.key;
                final id = entry.value;
                return Obx(() => Row(
                      children: [
                        const SizedBox(width: 7),
                        Expanded(
                          child: AnimatedOpacity(
                            opacity: minatKonsepController
                                        .lingkupPekerjaanOptions[id] !=
                                    null
                                ? 1.0
                                : 0.0,
                            duration: const Duration(milliseconds: 500),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text("${index + 1}. ",
                                      style: const TextStyle(fontSize: 12)),
                                ),
                                Expanded(
                                  flex: 20,
                                  child: Text(
                                    minatKonsepController
                                                .lingkupPekerjaanOptions[id] !=
                                            null
                                        ? TranslationService
                                            .translateBetweenLangs(
                                            minatKonsepController
                                                .lingkupPekerjaanOptions[id]!,
                                            "dcv.minat.ruang_lingkup",
                                            "id",
                                            Get.locale?.languageCode
                                                    .toLowerCase() ??
                                                "id",
                                          )
                                        : minatKonsepController
                                                .lingkupPekerjaanOptions[id] ??
                                            '',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
            ],
          ],
        ),
      ],
    );
  }
}
