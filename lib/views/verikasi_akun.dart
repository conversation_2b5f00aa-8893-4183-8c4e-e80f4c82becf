import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerikasiAkun extends StatefulWidget {
  final String? email;
  const VerikasiAkun({super.key, this.email});

  @override
  State<VerikasiAkun> createState() => _VerikasiAkunState();
}

class _VerikasiAkunState extends State<VerikasiAkun> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  ImageAssets.logo,
                  width: 150,
                  height: 150,
                ),
                SizedBox(height: 20),
                Text(
                  "Terima kasih sudah bergabung dengan digitalcv.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  "Untuk mengaktifkan akun Anda, silakan klik tautan verifikasi yang sudah dikirimkan ke alamat email ${widget.email ?? ''}",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 20),
                SizedBox(
                  width: Get.width,
                  height: 50,
                  child: ElevatedButton(
                      onPressed: () async {
                        setState(() {
                          isLoading = true;
                        });
                        var response = await AuthService().kirimUlangVerifikasi(
                          email: widget.email ?? '',
                        );
                        setState(() {
                          isLoading = false;
                        });
                      },
                      child: isLoading
                          ? Center(
                              child: CircularProgressIndicator(
                              color: Colors.black,
                            ))
                          : Text(
                              "Kirim Ulang",
                              style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500),
                            )),
                ),
                SizedBox(
                  height: 10,
                ),
                TextButton(
                    onPressed: () async {
                      Get.dialog(
                        Center(
                          child: CircularProgressIndicator(
                            color: ColorAsset.primaryColor,
                          ),
                        ),
                      );
                      var response = await AuthService().checkStatusVerifikasi(
                        email: widget.email ?? '',
                      );
                      Get.back();
                      Get.offAllNamed(Routes.home);
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: const [
                        Icon(Icons.refresh_rounded, color: Colors.blueAccent),
                        SizedBox(width: 5),
                        Text(
                          "Refresh halaman",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blueAccent,
                          ),
                        ),
                      ],
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }
}
