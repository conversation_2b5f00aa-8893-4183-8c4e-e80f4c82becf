import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProvinsiBottomSheet extends StatefulWidget {
  const ProvinsiBottomSheet({super.key});

  @override
  State<ProvinsiBottomSheet> createState() => _ProvinsiBottomSheetState();
}

class _ProvinsiBottomSheetState extends State<ProvinsiBottomSheet> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.provinsiList.isEmpty) {
        controller.getProvinsi("", 1);
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getProvinsi("", 1);
    } else {
      controller.getProvinsi(query, 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("lokasi.pilih_provinsi".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "lokasi.cari_provinsi".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                // if (searchController.text.length < 3) {
                //   return const Center(
                //       child: Text("Masukkan minimal 3 karakter"));
                // } else {
                //   if (controller.lokasiList.isEmpty) {
                //     return const Center(child: Text("Tidak ada data"));
                //   }
                // }

                if (controller.provinsiList.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.provinsiList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final provinsi = controller.provinsiList[index];
                    return ListTile(
                      title: Text(provinsi.nama),
                      onTap: () {
                        Get.back(result: provinsi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class KotaBottomSheet extends StatefulWidget {
  final idProvince;
  const KotaBottomSheet({super.key, this.idProvince});

  @override
  State<KotaBottomSheet> createState() => _KotaBottomSheetState();
}

class _KotaBottomSheetState extends State<KotaBottomSheet> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.kotaList.isEmpty) {
        controller.getKota("", 1, widget.idProvince);
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getKota("", 1, widget.idProvince);
    } else {
      controller.getKota(query, 1, widget.idProvince);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("lokasi.pilih_kota".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "lokasi.cari_kota".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.kotaList.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.kotaList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final provinsi = controller.kotaList[index];
                    return ListTile(
                      title: Text(provinsi.nama),
                      onTap: () {
                        Get.back(result: provinsi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class KecamatanBottomSheet extends StatefulWidget {
  final idRegency;
  const KecamatanBottomSheet({super.key, this.idRegency});

  @override
  State<KecamatanBottomSheet> createState() => _KecamatanBottomSheetState();
}

class _KecamatanBottomSheetState extends State<KecamatanBottomSheet> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.kecamatanList.isEmpty) {
        controller.getKecamatan("", 1, widget.idRegency);
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getKecamatan("", 1, widget.idRegency);
    } else {
      controller.getKecamatan(query, 1, widget.idRegency);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("lokasi.pilih_kecamatan".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "lokasi.cari_kecamatan".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.kecamatanList.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.kecamatanList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final provinsi = controller.kecamatanList[index];
                    return ListTile(
                      title: Text(provinsi.nama),
                      onTap: () {
                        Get.back(result: provinsi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class LokasiBottomSheet extends StatefulWidget {
  const LokasiBottomSheet({super.key});

  @override
  State<LokasiBottomSheet> createState() => _LokasiBottomSheetState();
}

class _LokasiBottomSheetState extends State<LokasiBottomSheet> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super
        .initState(); // Delay satu frame agar pemanggilan tidak langsung saat build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.lokasiList.isEmpty) {
        controller.getLokasi("", 1, 50);
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getLokasi("", 1, 50);
    } else {
      controller.getLokasi(query, 1, 50);
    }
  }

  @override
  Widget build(BuildContext context) {
    // final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(Get.context!).unfocus(),
      child: Container(
        height: Get.height * 0.5,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("lokasi.pilih_lokasi".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "lokasi.cari_lokasi".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.lokasiList.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.lokasiList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final provinsi = controller.lokasiList[index];
                    return ListTile(
                      title: Text(provinsi.nama),
                      onTap: () {
                        Get.back(result: provinsi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class BahasaBottomSheet extends StatefulWidget {
  const BahasaBottomSheet({super.key});

  @override
  State<BahasaBottomSheet> createState() => _BahasaBottomSheetState();
}

class _BahasaBottomSheetState extends State<BahasaBottomSheet> {
  final MinatKonsepController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super
        .initState(); // Delay satu frame agar pemanggilan tidak langsung saat build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.bahasaListAll.isEmpty) {
        controller.getBahasa("");
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getBahasa("");
    } else {
      controller.getBahasa(query);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("bahasa.pilih_bahasa".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "bahasa.cari_bahasa".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.bahasaListAll.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.bahasaListAll.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final provinsi = controller.bahasaListAll[index];
                    return ListTile(
                      title: Text(provinsi.text),
                      onTap: () {
                        Get.back(result: provinsi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
