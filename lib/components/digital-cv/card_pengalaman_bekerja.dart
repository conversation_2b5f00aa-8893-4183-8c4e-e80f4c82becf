import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/formater.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardPengalamanBekerja extends StatelessWidget {
  const CardPengalamanBekerja({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            children: [
                              Obx(() {
                                if (infoCVController.pekerjaanList.isNotEmpty) {
                                  return Column(
                                    children: [
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.person,
                                                size: 18,
                                                color: ColorAsset.primaryColor,
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                timelineData[3]["group"]!,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                          GestureDetector(
                                            onTap: () async {
                                              var result = await Get.toNamed(
                                                  Routes.pengalamanKerja,
                                                  arguments: {
                                                    "pekerjaan": null,
                                                    "pengalaman":
                                                        infoCVController
                                                            .rhList[0]
                                                            .pengalamanKerja,
                                                    "tot_pengalaman":
                                                        infoCVController.rhList
                                                                .isNotEmpty
                                                            ? infoCVController
                                                                .rhList[0]
                                                                .lamaPengalamanKerja
                                                            : 0,
                                                    // "tot_pengalaman_posisi_sama": infoCVController.rhList.isNotEmpty ? infoCVController.rhList[0].lamaPosisiKerja : 0,
                                                  });

                                              if (result == true) {
                                                infoCVController.loadRH();
                                                infoCVController
                                                    .loadRiwayatPekerjaan();
                                              }
                                            },
                                            child: Icon(
                                              Icons.add,
                                              size: 18,
                                              color: ColorAsset.secodaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      Divider(
                                        thickness: 1,
                                        height: 1,
                                        color: Colors.grey[300],
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  );
                                }
                                return SizedBox();
                              }),
                              Obx(() => Skeletonizer(
                                  enabled:
                                      infoCVController.isLoadingPekerjaan.value,
                                  child: infoCVController.pekerjaanList.isEmpty
                                      ? Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  if (infoCVController
                                                          .rhList.isNotEmpty &&
                                                      infoCVController.rhList[0]
                                                              .pengalamanKerja ==
                                                          "konfirmasi.tidak_ada"
                                                              .tr)
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .primaryColor,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          timelineData[3]
                                                                  ["group"] ??
                                                              "",
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  else if (infoCVController
                                                          .rhList.isNotEmpty &&
                                                      infoCVController.rhList[0]
                                                              .pengalamanKerja !=
                                                          "konfirmasi.freshgrad"
                                                              .tr)
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .primaryColor,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          "konfirmasi.tidak_ada_pengalaman"
                                                              .tr,
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  else
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .primaryColor,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          infoCVController
                                                                  .rhList
                                                                  .isNotEmpty
                                                              ? infoCVController
                                                                          .rhList[
                                                                              0]
                                                                          .pengalamanKerja ==
                                                                      "konfirmasi.freshgrad"
                                                                          .tr
                                                                  ? "konfirmasi.freshgrad"
                                                                      .tr
                                                                  : infoCVController
                                                                              .rhList[
                                                                                  0]
                                                                              .pengalamanKerja ==
                                                                          "Ya"
                                                                      ? "tombol.tambah_data"
                                                                          .tr
                                                                      : "data_kosong"
                                                                          .tr
                                                              : "data_kosong"
                                                                  .tr,
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  if (infoCVController
                                                          .rhList.isNotEmpty &&
                                                      infoCVController.rhList[0]
                                                              .pengalamanKerja ==
                                                          "konfirmasi.freshgrad"
                                                              .tr)
                                                    SizedBox(height: 8),
                                                  if (infoCVController
                                                          .rhList.isNotEmpty &&
                                                      infoCVController.rhList[0]
                                                              .pengalamanKerja ==
                                                          "konfirmasi.konfirmasi.freshgradkonfirmasi.freshgrad"
                                                              .tr)
                                                    Text(
                                                      infoCVController.rhList[0]
                                                                  .pengalamanKerja ==
                                                              "konfirmasi.freshgrad"
                                                                  .tr
                                                          ? "konfirmasi.freshgrad"
                                                              .tr
                                                          : infoCVController
                                                                      .rhList[0]
                                                                      .pengalamanKerja ==
                                                                  "Ya"
                                                              ? "tombol.tambah_data"
                                                                  .tr
                                                              : "data_kosong"
                                                                  .tr,
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  if (infoCVController
                                                      .rhList.isNotEmpty)
                                                    if (infoCVController
                                                            .rhList[0]
                                                            .pengalamanKerja ==
                                                        "ya")
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.person,
                                                            size: 18,
                                                            color: ColorAsset
                                                                .primaryColor,
                                                          ),
                                                          SizedBox(width: 8),
                                                          Text(
                                                            timelineData[3]
                                                                    ["group"] ??
                                                                "",
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = infoCVController
                                                        .rhList.isNotEmpty
                                                    ? await Get.toNamed(
                                                        Routes.pengalamanKerja,
                                                        arguments: {
                                                          "pekerjaan": null,
                                                          "pengalaman":
                                                              infoCVController
                                                                  .rhList[0]
                                                                  .pengalamanKerja,
                                                          "tot_pengalaman":
                                                              infoCVController
                                                                      .rhList
                                                                      .isNotEmpty
                                                                  ? infoCVController
                                                                      .rhList[0]
                                                                      .lamaPengalamanKerja
                                                                  : 0,
                                                        },
                                                      )
                                                    : await Get.toNamed(
                                                        Routes.pengalamanKerja);

                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                  infoCVController
                                                      .loadRiwayatPekerjaan();
                                                }
                                              },
                                              child: Icon(
                                                Icons.add,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Column(
                                          children: List.generate(
                                            infoCVController
                                                .pekerjaanList.length,
                                            (index) {
                                              final item = infoCVController
                                                  .pekerjaanList[index];
                                              return Column(
                                                children: [
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              item.jabatan,
                                                              style:
                                                                  const TextStyle(
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                            Text(
                                                              item.namaPerusahaan,
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                            Text(
                                                              "${ubahFormatBulan(item.tahunMulai)} - ${ubahFormatBulan(item.tahunSelesai)}",
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                        onTap: () async {
                                                          var result =
                                                              await Get.toNamed(
                                                            Routes
                                                                .pengalamanKerja,
                                                            arguments: {
                                                              "pekerjaan": item,
                                                              "pengalaman":
                                                                  infoCVController
                                                                      .rhList[0]
                                                                      .pengalamanKerja,
                                                              "tot_pengalaman": infoCVController
                                                                      .rhList
                                                                      .isNotEmpty
                                                                  ? infoCVController
                                                                      .rhList[0]
                                                                      .lamaPengalamanKerja
                                                                  : "",
                                                            },
                                                          );

                                                          if (result == true) {
                                                            infoCVController
                                                                .loadRH();
                                                            infoCVController
                                                                .loadRiwayatPekerjaan();
                                                          }
                                                        },
                                                        child: Icon(
                                                          Icons.edit,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .secodaryColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  if (index !=
                                                      infoCVController
                                                              .pekerjaanList
                                                              .length -
                                                          1)
                                                    Divider(
                                                      height: 16,
                                                      color: Colors.grey[300],
                                                    ),
                                                ],
                                              );
                                            },
                                          ),
                                        ))),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
