import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardMinatdanKonsepPribadi extends StatelessWidget {
  const CardMinatdanKonsepPribadi({
    super.key,
    required this.timelineData,
    required this.infoCVController,
    required this.minatKonsepController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;
  final MinatKonsepController minatKonsepController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Obx(() => Skeletonizer(
                                enabled: infoCVController.isLoadingBahasa.value,
                                child: (infoCVController.rhList.isEmpty
                                    ? Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.person,
                                                      color: ColorAsset
                                                          .primaryColor,
                                                      size: 18,
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(
                                                      timelineData[6]["group"]!,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () async {
                                              var result = await Get.toNamed(
                                                Routes.minatKonsep,
                                                arguments: {
                                                  "bahasa": null,
                                                  "kelebihan": null,
                                                  "kekurangan": null,
                                                  "kopmuterisasi": null,
                                                  "memimpin": null,
                                                  "penilaian": null,
                                                  "lingkup": null,
                                                },
                                              );

                                              if (result == true) {
                                                infoCVController.loadRH();
                                                infoCVController
                                                    .loadPenguasaanBahasa();
                                              }
                                            },
                                            child: Icon(
                                              Icons.add,
                                              size: 18,
                                              color: ColorAsset.secodaryColor,
                                            ),
                                          ),
                                        ],
                                      )
                                    : infoCVController.rhList[0].bahasaAsing ==
                                                "" &&
                                            infoCVController
                                                    .rhList[0].kelebihan ==
                                                "" &&
                                            infoCVController
                                                    .rhList[0].kekurangan ==
                                                "" &&
                                            infoCVController.rhList[0]
                                                    .ilmuKomputerisasi ==
                                                "" &&
                                            infoCVController.rhList[0]
                                                    .kemampuanPresentasi ==
                                                "" &&
                                            infoCVController
                                                    .rhList[0].memimpinTim ==
                                                "" &&
                                            infoCVController.rhList[0]
                                                    .lingkupPekerjaan ==
                                                ""
                                        ? Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          color: ColorAsset
                                                              .primaryColor,
                                                          size: 18,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          timelineData[6]
                                                              ["group"]!,
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              GestureDetector(
                                                onTap: () async {
                                                  var result =
                                                      await Get.toNamed(
                                                    Routes.minatKonsep,
                                                    arguments: {
                                                      "bahasa": null,
                                                      "kelebihan": null,
                                                      "kekurangan": null,
                                                      "kopmuterisasi": null,
                                                      "memimpin": null,
                                                      "penilaian": null,
                                                      "lingkup": null,
                                                    },
                                                  );

                                                  if (result == true) {
                                                    infoCVController.loadRH();
                                                    infoCVController
                                                        .loadPenguasaanBahasa();
                                                  }
                                                },
                                                child: Icon(
                                                  Icons.add,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.secodaryColor,
                                                ),
                                              ),
                                            ],
                                          )
                                        : Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          color: ColorAsset
                                                              .primaryColor,
                                                          size: 18,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          timelineData[6]
                                                              ["group"]!,
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    Divider(
                                                      color: Colors.grey[300],
                                                      height: 20,
                                                    ),
                                                    Text(
                                                      "dcv.minat.txt_bahasa_dikuasai"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    infoCVController
                                                            .penguasaanBahasaList
                                                            .isEmpty
                                                        ? Text("-",
                                                            style: TextStyle(
                                                                fontSize: 12,
                                                                color: Colors
                                                                    .grey[500]))
                                                        : Container(
                                                            alignment: Alignment
                                                                .centerLeft, // Pastikan wrap mulai dari kiri
                                                            child: Wrap(
                                                              alignment:
                                                                  WrapAlignment
                                                                      .start, // Memastikan wrap mulai dari kiri
                                                              crossAxisAlignment:
                                                                  WrapCrossAlignment
                                                                      .start, // Menjaga elemen tetap di atas
                                                              spacing:
                                                                  8, // Jarak horizontal antar button
                                                              runSpacing:
                                                                  8, // Jarak antar baris button
                                                              children: [
                                                                for (var item
                                                                    in infoCVController
                                                                        .penguasaanBahasaList)
                                                                  Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: Colors
                                                                          .white, // Warna latar belakang container
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: ColorAsset
                                                                            .primaryColor, // Warna border yang diinginkan
                                                                        width:
                                                                            0.5, // Ketebalan border
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              6), // Opsional: border melengkung
                                                                    ),
                                                                    padding:
                                                                        EdgeInsets
                                                                            .all(5),
                                                                    child: Text(
                                                                      item.bahasa,
                                                                      style: const TextStyle(
                                                                          fontSize:
                                                                              12),
                                                                    ),
                                                                  ),
                                                              ],
                                                            ),
                                                          ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.txt_kelebihan2"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    cardKelebihanPribadi(),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.txt_kekurangan2"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Container(
                                                      alignment: Alignment
                                                          .centerLeft, // Pastikan wrap mulai dari kiri
                                                      child: Wrap(
                                                        alignment: WrapAlignment
                                                            .start, // Memastikan wrap mulai dari kiri
                                                        crossAxisAlignment:
                                                            WrapCrossAlignment
                                                                .start, // Menjaga elemen tetap di atas
                                                        spacing:
                                                            8, // Jarak horizontal antar button
                                                        runSpacing:
                                                            8, // Jarak antar baris button
                                                        children: [
                                                          cardKekuranganPribadi(),
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.komputerisasi.judul2"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    cardKomputerisasiPribadi(),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.jml_org.judul2"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    if ((infoCVController
                                                                .rhList[0]
                                                                .memimpinTim ??
                                                            '')
                                                        .split(',')
                                                        .map((e) =>
                                                            int.tryParse(
                                                                e.trim()))
                                                        .whereType<int>()
                                                        .map((id) =>
                                                            id -
                                                            1) // <- Kurangi 1 agar sesuai index Dart
                                                        .where((id) =>
                                                            id >= 0 &&
                                                            id <
                                                                minatKonsepController
                                                                    .jmlOrangOptions
                                                                    .length)
                                                        .isEmpty)
                                                      Text('-',
                                                          style: TextStyle(
                                                              fontSize: 12,
                                                              color: Colors
                                                                  .grey[500]!))
                                                    else
                                                      ...(infoCVController
                                                                  .rhList[0]
                                                                  .memimpinTim ??
                                                              '')
                                                          .split(',')
                                                          .map((e) =>
                                                              int.tryParse(
                                                                  e.trim()))
                                                          .whereType<int>()
                                                          .map((id) =>
                                                              id -
                                                              1) // <- Kurangi 1 agar sesuai index Dart
                                                          .where((id) =>
                                                              id >= 0 &&
                                                              id <
                                                                  minatKonsepController
                                                                      .jmlOrangOptions
                                                                      .length)
                                                          .map((id) {
                                                        return Text(
                                                          minatKonsepController
                                                              .jmlOrangOptions[id],
                                                          style: TextStyle(
                                                              fontSize: 12,
                                                              color: Colors
                                                                  .grey[500]!),
                                                        );
                                                      }),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.txt_penilaian2"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    if ((infoCVController
                                                                .rhList[0]
                                                                .kemampuanPresentasi ??
                                                            '')
                                                        .split(',')
                                                        .map((e) =>
                                                            int.tryParse(
                                                                e.trim()))
                                                        .whereType<int>()
                                                        .map((id) =>
                                                            id -
                                                            1) // <- Kurangi 1 agar sesuai index Dart
                                                        .where((id) =>
                                                            id >= 0 &&
                                                            id <
                                                                minatKonsepController
                                                                    .penilaianOptions
                                                                    .length)
                                                        .isEmpty)
                                                      Text('-',
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            color: Colors
                                                                .grey[500]!,
                                                          ))
                                                    else
                                                      ...(infoCVController
                                                                  .rhList[0]
                                                                  .kemampuanPresentasi ??
                                                              '')
                                                          .split(',')
                                                          .map((e) =>
                                                              int.tryParse(
                                                                  e.trim()))
                                                          .whereType<int>()
                                                          .map((id) =>
                                                              id -
                                                              1) // <- Kurangi 1 agar sesuai index Dart
                                                          .where((id) =>
                                                              id >= 0 &&
                                                              id <
                                                                  minatKonsepController
                                                                      .penilaianOptions
                                                                      .length)
                                                          .map((id) {
                                                        return Text(
                                                          minatKonsepController
                                                              .penilaianOptions[id],
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            color: Colors
                                                                .grey[500]!,
                                                          ),
                                                        );
                                                      }),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      "dcv.minat.ruang_lingkup.judul"
                                                          .tr,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Container(
                                                      alignment: Alignment
                                                          .centerLeft, // Pastikan wrap mulai dari kiri
                                                      child: Wrap(
                                                        alignment: WrapAlignment
                                                            .start, // Memastikan wrap mulai dari kiri
                                                        crossAxisAlignment:
                                                            WrapCrossAlignment
                                                                .start, // Menjaga elemen tetap di atas
                                                        spacing:
                                                            8, // Jarak horizontal antar button
                                                        runSpacing:
                                                            8, // Jarak antar baris button
                                                        children: [
                                                          for (var item
                                                              in (infoCVController
                                                                          .rhList[
                                                                              0]
                                                                          .lingkupPekerjaanNama ??
                                                                      '-')
                                                                  .split(',')
                                                                  .map((e) =>
                                                                      e.trim())
                                                                  .toList())
                                                            Container(
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: Colors
                                                                    .white, // Warna latar belakang container
                                                                border:
                                                                    Border.all(
                                                                  color: Colors
                                                                          .grey[
                                                                      600]!, // Warna border yang diinginkan
                                                                  width:
                                                                      0.5, // Ketebalan border
                                                                ),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            6), // Opsional: border melengkung
                                                              ),
                                                              padding:
                                                                  EdgeInsets
                                                                      .all(5),
                                                              child: Text(
                                                                TranslationService
                                                                    .translateBetweenLangs(
                                                                  item,
                                                                  "dcv.minat.ruang_lingkup",
                                                                  "id",
                                                                  Get.locale
                                                                          ?.languageCode
                                                                          .toLowerCase() ??
                                                                      "id",
                                                                ),
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]!,
                                                                ),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              GestureDetector(
                                                onTap: () async {
                                                  var result =
                                                      await Get.toNamed(
                                                    Routes.minatKonsep,
                                                    arguments: {
                                                      "bahasa": infoCVController
                                                          .penguasaanBahasaList,
                                                      "kelebihan":
                                                          (infoCVController
                                                                      .rhList[0]
                                                                      .kelebihan ??
                                                                  '')
                                                              .split(',')
                                                              .map((e) =>
                                                                  e.trim())
                                                              .toList(),
                                                      "kekurangan":
                                                          (infoCVController
                                                                      .rhList[0]
                                                                      .kekurangan ??
                                                                  '')
                                                              .split(',')
                                                              .map((e) =>
                                                                  e.trim())
                                                              .toList(),
                                                      "komputerisasi":
                                                          (infoCVController
                                                                      .rhList[0]
                                                                      .ilmuKomputerisasi ??
                                                                  '')
                                                              .split(',')
                                                              .map((e) =>
                                                                  e.trim())
                                                              .toList(),
                                                      "memimpin":
                                                          infoCVController
                                                              .rhList[0]
                                                              .memimpinTim,
                                                      "penilaian": infoCVController
                                                          .rhList[0]
                                                          .kemampuanPresentasi,
                                                      "lingkup": (infoCVController
                                                                  .rhList[0]
                                                                  .lingkupPekerjaan ??
                                                              '')
                                                          .split(',')
                                                          .map((e) => e.trim())
                                                          .toList(),
                                                    },
                                                  );

                                                  if (result == true) {
                                                    infoCVController.loadRH();
                                                    infoCVController
                                                        .loadPenguasaanBahasa();
                                                  }
                                                },
                                                child: Icon(
                                                  Icons.edit,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.secodaryColor,
                                                ),
                                              ),
                                            ],
                                          ))))),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container cardKomputerisasiPribadi() {
    return Container(
      alignment: Alignment.centerLeft, // Pastikan wrap mulai dari kiri
      child: Wrap(
        alignment: WrapAlignment.start, // Memastikan wrap mulai dari kiri
        crossAxisAlignment:
            WrapCrossAlignment.start, // Menjaga elemen tetap di atas
        spacing: 8, // Jarak horizontal antar button
        runSpacing: 8, // Jarak antar baris button
        children: [
          if ((infoCVController.rhList[0].ilmuKomputerisasi ?? '')
              .split(',')
              .map((e) => int.tryParse(e.trim()))
              .whereType<int>()
              .where((id) =>
                  minatKonsepController.komputerisasiOptions.containsKey(id))
              .isEmpty)
            Text("-", style: const TextStyle(fontSize: 12))
          else
            ...(infoCVController.rhList[0].ilmuKomputerisasi ?? '')
                .split(',')
                .map((e) => int.tryParse(e.trim()))
                .whereType<int>()
                .where((id) =>
                    minatKonsepController.komputerisasiOptions.containsKey(id))
                .map((id) {
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.grey[600]!,
                    width: 0.5,
                  ),
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: EdgeInsets.all(5),
                child: Text(
                  minatKonsepController.komputerisasiOptions[id] ?? '-',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]!),
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget cardKelebihanPribadi() {
    return Container(
      alignment: Alignment.centerLeft, // Pastikan wrap mulai dari kiri
      child: Wrap(
        alignment: WrapAlignment.start, // Memastikan wrap mulai dari kiri
        crossAxisAlignment:
            WrapCrossAlignment.start, // Menjaga elemen tetap di atas
        spacing: 8, // Jarak horizontal antar button
        runSpacing: 8, // Jarak antar baris button
        children: [
          if ((infoCVController.rhList[0].kelebihan ?? '')
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .isEmpty)
            Text("-", style: const TextStyle(fontSize: 12))
          else
            for (var i = 0;
                i <
                    (infoCVController.rhList[0].kelebihan ?? '')
                        .split(',')
                        .length;
                i++)
              if ((infoCVController.rhList[0].kelebihan ?? '')
                  .split(',')[i]
                  .trim()
                  .isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: Colors.grey[600]!,
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: EdgeInsets.all(5),
                  child: Text(
                    (infoCVController.rhList[0].kelebihan ?? '')
                        .split(',')[i]
                        .trim(),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                )
        ],
      ),
    );
  }

  Widget cardKekuranganPribadi() {
    return Container(
      alignment: Alignment.centerLeft, // Pastikan wrap mulai dari kiri
      child: Wrap(
        alignment: WrapAlignment.start, // Memastikan wrap mulai dari kiri
        crossAxisAlignment:
            WrapCrossAlignment.start, // Menjaga elemen tetap di atas
        spacing: 8, // Jarak horizontal antar button
        runSpacing: 8, // Jarak antar baris button
        children: [
          if ((infoCVController.rhList[0].kekurangan ?? '')
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .isEmpty)
            Text("-", style: const TextStyle(fontSize: 12))
          else
            for (var i = 0;
                i <
                    (infoCVController.rhList[0].kekurangan ?? '')
                        .split(',')
                        .length;
                i++)
              if ((infoCVController.rhList[0].kekurangan ?? '')
                  .split(',')[i]
                  .trim()
                  .isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: Colors.grey[600]!,
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: EdgeInsets.all(5),
                  child: Text(
                    (infoCVController.rhList[0].kekurangan ?? '')
                        .split(',')[i]
                        .trim(),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                )
        ],
      ),
    );
  }
}
