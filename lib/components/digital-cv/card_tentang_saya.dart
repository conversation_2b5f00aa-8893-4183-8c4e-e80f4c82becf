import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardTentangSaya extends StatelessWidget {
  const CardTentangSaya({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  void _showEditTentangSayaModal(BuildContext context) {
    final TextEditingController textController = TextEditingController();

    // Set initial text from controller
    textController.text = infoCVController.tentangSaya.value;

    showModalBottomSheet(
      context: context,
      // isScrollControlled: true,
      // isDismissible: true,
      // enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "dcv.tentang_saya".tr,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              // Text area
              Expanded(
                child: TextField(
                  controller: textController,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                  decoration: InputDecoration(
                    hintText: "Ceritakan tentang diri Anda...",
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: ColorAsset.primaryColor),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Obx(() => SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () async {
                        if (infoCVController.isLoadingTentangSaya.value) return;
                        infoCVController.tentangSaya.value =
                            textController.text;
                        var res = await infoCVController.postTentangSaya();
                        if (res == true) {
                          infoCVController.loadRH();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50),
                        ),
                        textStyle: const TextStyle(fontSize: 14),
                      ),
                      child: infoCVController.isLoadingTentangSaya.value
                          ? SizedBox(
                              height: 30,
                              width: 30,
                              child: CircularProgressIndicator(
                                color: Colors.black,
                              ),
                            )
                          : Text(
                              "tombol.simpan".tr,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  )),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              // ✅ Ganti `Expanded` dengan `Flexible`
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Material(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        side: BorderSide(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          children: [
                            Obx(
                              () => (infoCVController
                                      .tentangSaya.value.isNotEmpty
                                  ? Column(
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.person,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.primaryColor,
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Text(
                                                  "dcv.tentang_saya".tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                GestureDetector(
                                                  onTap: () async {
                                                    // var result =
                                                    //     await Get.toNamed(
                                                    //   Routes.pendidikan,
                                                    //   arguments: {
                                                    //     "pendidikan": null,
                                                    //     "diploma":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .diploma
                                                    //             : "",
                                                    //     "pendidikan_terakhir":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .pendidikanTerakhir
                                                    //             : "",
                                                    //   },
                                                    // );

                                                    // if (result == true) {
                                                    //   infoCVController.loadRH();
                                                    //   infoCVController
                                                    //       .loadRiwayatPendidikan();
                                                    // }
                                                    _showEditTentangSayaModal(
                                                        context);
                                                  },
                                                  child: Icon(
                                                    Icons.edit,
                                                    size: 18,
                                                    color: ColorAsset
                                                        .secodaryColor,
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    )
                                  : SizedBox.shrink()),
                            ),
                            Obx(() => Skeletonizer(
                                enabled:
                                    infoCVController.isLoadingTentangSaya.value,
                                child: (!infoCVController
                                        .tentangSaya.value.isEmpty
                                    ? Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      infoCVController
                                                          .tentangSaya.value,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.normal,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.person,
                                                      size: 18,
                                                      color: ColorAsset
                                                          .primaryColor,
                                                    ),
                                                    SizedBox(
                                                      width: 8,
                                                    ),
                                                    Text(
                                                      "dcv.tentang_saya".tr,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () async {
                                              _showEditTentangSayaModal(
                                                  context);
                                            },
                                            child: Icon(
                                              Icons.add,
                                              size: 18,
                                              color: ColorAsset.secodaryColor,
                                            ),
                                          ),
                                        ],
                                      )))),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
