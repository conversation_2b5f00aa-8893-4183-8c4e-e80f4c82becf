import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/services/form_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:file_picker/file_picker.dart';
import 'package:open_file/open_file.dart';
import 'dart:io';

class CardInformasiPribadi extends StatelessWidget {
  CardInformasiPribadi({
    super.key,
    this.currentRoute,
    required this.infoCVController,
    required this.timelineData,
    required this.profileController,
  });

  // Reactive variables for file selection
  static final RxString selectedFileName = ''.obs;
  static final RxString selectedFileSize = ''.obs;
  static final RxBool isFileSelected = false.obs;
  static File? selectedFile;
  final String? currentRoute;
  final GetInfoCVController infoCVController;
  final List<Map<String, String>> timelineData;
  final ProfileController profileController;
  final snackbar = Get.find<SnackBarService>();

  @override
  Widget build(BuildContext context) {
    if (currentRoute == "" || currentRoute == null) {
      return buildCardRH();
    } else {
      return buildCardCVATS();
    }
  }

  Widget buildCardRH() {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Obx(
                            () => Skeletonizer(
                              enabled: infoCVController.isLoadingRH.value,
                              child: infoCVController.rhList.isEmpty
                                  ? Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              timelineData[0]["group"]!,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            var result = infoCVController
                                                    .rhList.isNotEmpty
                                                ? await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                    arguments: infoCVController
                                                        .rhList[0])
                                                : await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                  );
                                            if (result == true) {
                                              infoCVController.loadRH();
                                            }
                                          },
                                          child: Icon(
                                            Icons.add,
                                            size: 18,
                                            color: ColorAsset.secodaryColor,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              timelineData[0]["group"]!,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: const BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  boxShadow: []),
                                              child: CircleAvatar(
                                                radius: 60,
                                                child: ClipOval(
                                                  child: profileController
                                                              .rxImage.value !=
                                                          ""
                                                      ? Image.network(
                                                          profileController
                                                              .rxImage.value,
                                                          fit: BoxFit.cover,
                                                          width: 50,
                                                          height: 50,
                                                          errorBuilder:
                                                              (context, error,
                                                                  stackTrace) {
                                                            return const Icon(
                                                              Icons.person,
                                                              size: 20,
                                                            );
                                                          },
                                                        )
                                                      : const Icon(
                                                          Icons.person,
                                                          size: 20,
                                                        ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    infoCVController
                                                            .rhList[0].nama ??
                                                        '-',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: 14,
                                                        color: Colors.grey[500],
                                                      ),
                                                      SizedBox(width: 4),
                                                      Text(
                                                        infoCVController
                                                                .rhList[0]
                                                                .tempatLahirFormatted ??
                                                            '-',
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            color: Colors
                                                                .grey[500]),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                    arguments: infoCVController
                                                        .rhList[0]);
                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                }
                                              },
                                              child: Icon(
                                                Icons.edit,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_lahir"
                                                      .tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  infoCVController.rhList[0]
                                                          .tglLahirFormatted ??
                                                      '-',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                )
                                              ],
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_jk".tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  infoCVController.rhList[0]
                                                          .jenisKelamin ??
                                                      '-',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_email2"
                                                      .tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  profileController
                                                      .rxEmail.value,
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "dcv.info_pribadi.txt_handphone2"
                                                  .tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              profileController.noTelp.value,
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.normal,
                                                  color: Colors.grey[500]),
                                            ),
                                          ],
                                        )
                                        // SizedBox(
                                        //   height: 10,
                                        // ),
                                        // Text(
                                        //   "dcv.info_pribadi.txt_ktp2".tr,
                                        //   style: TextStyle(
                                        //     fontSize: 12,
                                        //     fontWeight: FontWeight.bold,
                                        //   ),
                                        // ),
                                        // Text(
                                        //   infoCVController.rhList[0].ktp,
                                        //   style: TextStyle(
                                        //     fontSize: 12,
                                        //     fontWeight: FontWeight.normal,
                                        //   ),
                                        //   softWrap: true,
                                        // ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildCardCVATS() {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Obx(
                            () => Skeletonizer(
                              enabled: infoCVController.isLoadingRH.value,
                              child: infoCVController.rhList.isEmpty
                                  ? Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              "dcv.info_pribadi.judul2".tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            var result = infoCVController
                                                    .rhList.isNotEmpty
                                                ? await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                    arguments: infoCVController
                                                        .rhList[0])
                                                : await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                  );
                                            if (result == true) {
                                              infoCVController.loadRH();
                                            }
                                          },
                                          child: Icon(
                                            Icons.add,
                                            size: 18,
                                            color: ColorAsset.secodaryColor,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              "dcv.info_pribadi.judul2".tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: const BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  boxShadow: []),
                                              child: CircleAvatar(
                                                radius: 60,
                                                child: ClipOval(
                                                  child: profileController
                                                              .rxImage.value !=
                                                          ""
                                                      ? Image.network(
                                                          profileController
                                                              .rxImage.value,
                                                          fit: BoxFit.cover,
                                                          width: 50,
                                                          height: 50,
                                                          errorBuilder:
                                                              (context, error,
                                                                  stackTrace) {
                                                            return const Icon(
                                                              Icons.person,
                                                              size: 20,
                                                            );
                                                          },
                                                        )
                                                      : const Icon(
                                                          Icons.person,
                                                          size: 20,
                                                        ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    infoCVController
                                                            .rhList[0].nama ??
                                                        '-',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    infoCVController.rhList[0]
                                                            .minatPosisi ??
                                                        '-',
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.normal,
                                                        color:
                                                            Colors.grey[500]),
                                                  )
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "currentRoute":
                                                          currentRoute ?? "",
                                                    },
                                                    arguments: infoCVController
                                                        .rhList[0]);
                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                }
                                              },
                                              child: Icon(
                                                Icons.edit,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            ItemPesonalData(
                                                title:
                                                    "dcv.info_pribadi.txt_email2"
                                                        .tr,
                                                profileController:
                                                    profileController
                                                        .rxEmail.value),
                                            ItemPesonalData(
                                                title:
                                                    "dcv.info_pribadi.txt_handphone2"
                                                        .tr,
                                                profileController:
                                                    profileController
                                                        .noTelp.value),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: ItemPesonalData(
                                                  title:
                                                      "dcv.info_pribadi.txt_tautan_portfolio"
                                                          .tr,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .linkPortofolio ??
                                                          '-'),
                                            ),
                                            Flexible(
                                              child: ItemPesonalData(
                                                  title:
                                                      "dcv.info_pribadi.txt_alamat2"
                                                          .tr,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .alamat ??
                                                          '-'),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: ItemPesonalDataIcon(
                                                  iconData:
                                                      FontAwesomeIcons.linkedin,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .linkLinkedin ??
                                                          '-'),
                                            ),
                                            SizedBox(width: 20),
                                            Flexible(
                                              child: ItemPesonalDataIcon(
                                                  iconData: FontAwesomeIcons
                                                      .instagram,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .akunInstagram ??
                                                          '-'),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: ItemPesonalDataIcon(
                                                  iconData:
                                                      FontAwesomeIcons.xTwitter,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .akunX ??
                                                          '-'),
                                            ),
                                            SizedBox(width: 20),
                                            Flexible(
                                              child: ItemPesonalDataIcon(
                                                  iconData:
                                                      FontAwesomeIcons.facebook,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .akunFb ??
                                                          '-'),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: ItemPesonalDataIcon(
                                                  iconData:
                                                      FontAwesomeIcons.tiktok,
                                                  profileController:
                                                      infoCVController.rhList[0]
                                                              .akunTiktok ??
                                                          '-'),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: ElevatedButton(
                                                  onPressed: () {
                                                    _showUploadCVModal(
                                                        Get.context!);
                                                  },
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                          FontAwesomeIcons
                                                              .upload,
                                                          color: Colors.black),
                                                      SizedBox(width: 5),
                                                      Text(
                                                        "dcv.info_pribadi.txt_unggah_cv"
                                                            .tr,
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            color:
                                                                Colors.black),
                                                      ),
                                                    ],
                                                  )),
                                            ),
                                            SizedBox(width: 20),
                                            Flexible(
                                              child: ElevatedButton(
                                                  onPressed: _downloadCv,
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                          FontAwesomeIcons.file,
                                                          color: Colors.black),
                                                      SizedBox(width: 5),
                                                      Text(
                                                        "dcv.info_pribadi.txt_download_cv"
                                                            .tr,
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            color:
                                                                Colors.black),
                                                      ),
                                                    ],
                                                  )),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUploadCVModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildUploadCVModal(context),
    );
  }

  Widget _buildUploadCVModal(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "dcv.form_upload".tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Icon(
                  Icons.close,
                  color: Colors.grey[600],
                  size: 24,
                ),
              ),
            ],
          ),
          SizedBox(height: 30),

          // Drag and drop area
          Obx(() => GestureDetector(
                onTap: _pickFile,
                child: Container(
                  width: double.infinity,
                  height: 150,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isFileSelected.value
                          ? Colors.green[400]!
                          : Colors.blue[400]!,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: isFileSelected.value
                        ? Colors.green[50]
                        : Colors.grey[50],
                  ),
                  child: CustomPaint(
                    painter: DashedBorderPainter(
                      color: isFileSelected.value
                          ? Colors.green[400]!
                          : Colors.blue[400]!,
                      strokeWidth: 2,
                      dashWidth: 8,
                      dashSpace: 4,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: 150,
                      child: isFileSelected.value
                          ? _buildSelectedFileView()
                          : _buildEmptyFileView(),
                    ),
                  ),
                ),
              )),
          SizedBox(height: 12),

          // File size limit text
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "* Batas ukuran file 5 MB",
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
          SizedBox(height: 30),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: Text(
                    "tombol.dismiss".tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Obx(() => ElevatedButton(
                      onPressed: isFileSelected.value ? _uploadFile : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isFileSelected.value
                            ? ColorAsset.primaryColor
                            : Colors.grey[300],
                        foregroundColor: isFileSelected.value
                            ? Colors.black
                            : Colors.grey[600],
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                      child: Text(
                        "tombol.upload".tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )),
              ),
            ],
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildEmptyFileView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.description,
          size: 48,
          color: Colors.blue[400],
        ),
        SizedBox(height: 12),
        Text(
          "dcv.info_upload".tr,
          style: TextStyle(
            fontSize: 14,
            color: Colors.blue[600],
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSelectedFileView() {
    return Stack(
      children: [
        Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                size: 48,
                color: Colors.green[600],
              ),
              SizedBox(height: 12),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  selectedFileName.value,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green[700],
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: 4),
              Text(
                selectedFileSize.value,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[600],
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                "dcv.info_upload2".tr,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.green[500],
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: _clearSelectedFile,
            child: Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 16,
                color: Colors.red[600],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        File file = File(result.files.single.path!);

        // Check file size (5MB limit)
        int fileSizeInBytes = file.lengthSync();
        double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

        if (fileSizeInMB > 5) {
          snackbar.showError("File size exceeds 5MB limit");
          return;
        }

        // Update reactive variables
        selectedFile = file;
        selectedFileName.value = result.files.single.name;
        selectedFileSize.value = _formatFileSize(fileSizeInBytes);
        isFileSelected.value = true;

        LogService.log.i("File selected: ${result.files.single.name}");
      }
    } catch (e) {
      LogService.log.e("Error picking file: $e");
      snackbar.showError("$e");
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  void _clearSelectedFile() {
    selectedFile = null;
    selectedFileName.value = '';
    selectedFileSize.value = '';
    isFileSelected.value = false;
  }

  Future<void> _uploadFile() async {
    if (selectedFile == null) {
      snackbar.showError("No file selected");
      return;
    }

    try {
      // Show loading dialog
      Get.dialog(
        Center(
          child: SpinKitThreeBounce(
            size: 35,
            color: ColorAsset.primaryColor,
          ),
        ),
        barrierDismissible: false,
      );

      // Upload file
      final formService = FormService();
      final response = await formService.uploadCv(selectedFile!);

      // Close loading dialog
      Get.back();
      Get.back();

      if (response.statusCode == 200) {
        snackbar.showSuccess("CV uploaded successfully");

        // Clear selected file after successful upload
        _clearSelectedFile();
        infoCVController.loadRH();

        LogService.log.i("CV uploaded successfully: ${response.data}");
      } else {
        snackbar.showError("Failed to upload CV: ${response.data}");
        LogService.log.e("Upload failed: ${response.data}");
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      snackbar.showError("Upload failed: $e");
      LogService.log.e("Upload error: $e");
    }
  }

  Future<void> _downloadCv() async {
    try {
      // Show loading dialog
      Get.dialog(
        Center(
          child: SpinKitThreeBounce(
            size: 35,
            color: ColorAsset.primaryColor,
          ),
        ),
        barrierDismissible: false,
      );

      // Download CV
      final formService = FormService();
      final filePath = await formService.downloadCv();

      // Close loading dialog
      Get.back();

      // Show success message
      snackbar.showSuccess("CV downloaded successfully");

      // Open the downloaded file
      await OpenFile.open(filePath);

      LogService.log.i("CV downloaded and opened: $filePath");
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      snackbar.showError("Download failed: $e");
      LogService.log.e("Download error: $e");
    }
  }
}

class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(8),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashWidth;
        final extractPath = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(extractPath, paint);
        distance = nextDistance + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class ItemPesonalData extends StatelessWidget {
  const ItemPesonalData({
    super.key,
    required this.profileController,
    required this.title,
  });

  final String profileController;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          profileController == "" ? "-" : profileController,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
              color: Colors.grey[500]),
        ),
      ],
    );
  }
}

class ItemPesonalDataIcon extends StatelessWidget {
  const ItemPesonalDataIcon({
    super.key,
    required this.profileController,
    required this.iconData,
  });

  final String profileController;
  final IconData iconData;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          iconData,
          size: 20,
          color: Colors.black,
        ),
        SizedBox(width: 20),
        Text(
          profileController == "" ? "-" : profileController,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
              color: Colors.grey[500]),
        ),
      ],
    );
  }
}
