import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardPengalamanOrganisasi extends StatelessWidget {
  const CardPengalamanOrganisasi({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            children: [
                              Obx(
                                () => infoCVController.organisasiList.isEmpty
                                    ? SizedBox()
                                    : Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.person,
                                                    size: 18,
                                                    color:
                                                        ColorAsset.primaryColor,
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text(
                                                    timelineData[5]["group"]!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              GestureDetector(
                                                onTap: () async {
                                                  var result =
                                                      await Get.toNamed(
                                                    Routes.pengalamanOrganisasi,
                                                    arguments: {
                                                      "organisasi": null,
                                                      "confirm": true,
                                                    },
                                                  );

                                                  if (result == true) {
                                                    infoCVController
                                                        .loadRiwayatOrganisasi();
                                                  }
                                                },
                                                child: Icon(
                                                  Icons.add,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.secodaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Divider(
                                            thickness: 1,
                                            height: 1,
                                            color: Colors.grey[300],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                        ],
                                      ),
                              ),
                              Obx(() => Skeletonizer(
                                  enabled: infoCVController
                                      .isLoadingOrganisasi.value,
                                  child: (infoCVController
                                          .organisasiList.isEmpty
                                      ? Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.person,
                                                        size: 18,
                                                        color: ColorAsset
                                                            .primaryColor,
                                                      ),
                                                      SizedBox(width: 8),
                                                      Text(
                                                        "konfirmasi.tidak_ada_organisasi"
                                                            .tr,
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = await Get.toNamed(
                                                  Routes.pengalamanOrganisasi,
                                                  arguments: {
                                                    "organisasi": null,
                                                    "confirm": false,
                                                  },
                                                );

                                                if (result == true) {
                                                  infoCVController
                                                      .loadRiwayatOrganisasi();
                                                }
                                              },
                                              child: Icon(
                                                Icons.add,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Column(
                                          children: List.generate(
                                            infoCVController
                                                .organisasiList.length,
                                            (index) {
                                              final item = infoCVController
                                                  .organisasiList[index];
                                              return Column(
                                                children: [
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              item.nama,
                                                              style: TextStyle(
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                            Text(
                                                              item.jabatan,
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                            Text(
                                                              "${item.tahun} - ${item.tempat}",
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                        onTap: () async {
                                                          var result =
                                                              await Get.toNamed(
                                                            Routes
                                                                .pengalamanOrganisasi,
                                                            arguments: {
                                                              "organisasi":
                                                                  item,
                                                              "confirm": true,
                                                            },
                                                          );

                                                          if (result == true) {
                                                            infoCVController
                                                                .loadRiwayatOrganisasi();
                                                          }
                                                        },
                                                        child: Icon(
                                                          Icons.edit,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .secodaryColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  // Divider antar item, jangan ditambahkan di item terakhir
                                                  if (index !=
                                                      infoCVController
                                                              .organisasiList
                                                              .length -
                                                          1)
                                                    Divider(
                                                        height: 16,
                                                        color:
                                                            Colors.grey[300]),
                                                ],
                                              );
                                            },
                                          ),
                                        ))))
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
