import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/formater.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardInformasiPekerjaan extends StatelessWidget {
  const CardInformasiPekerjaan({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Obx(() => Skeletonizer(
                              enabled: infoCVController.isLoadingRH.value,
                              child: (infoCVController.rhList.isEmpty
                                  ? Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.person,
                                                    size: 18,
                                                    color:
                                                        ColorAsset.primaryColor,
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text(
                                                    timelineData[4]["group"]!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            final result = await Get.toNamed(
                                                Routes.infoPekerjaan,
                                                arguments: null);
                                            if (result == true) {
                                              infoCVController.loadRH();
                                            }
                                          },
                                          child: Icon(
                                            Icons.add,
                                            size: 18,
                                            color: ColorAsset.secodaryColor,
                                          ),
                                        ),
                                      ],
                                    )
                                  : infoCVController.rhList[0].minatGaji ==
                                              "" ||
                                          infoCVController
                                                  .rhList[0].perjalananDinas ==
                                              "" ||
                                          infoCVController
                                                  .rhList[0].minatLokasiKerja ==
                                              ""
                                      ? Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons.person,
                                                        size: 18,
                                                        color: ColorAsset
                                                            .primaryColor,
                                                      ),
                                                      SizedBox(width: 8),
                                                      Text(
                                                        timelineData[4]
                                                            ["group"]!,
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                final result =
                                                    await Get.toNamed(
                                                        Routes.infoPekerjaan,
                                                        arguments: null);
                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                }
                                              },
                                              child: Icon(
                                                Icons.add,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons.person,
                                                        size: 18,
                                                        color: ColorAsset
                                                            .primaryColor,
                                                      ),
                                                      SizedBox(width: 8),
                                                      Text(
                                                        timelineData[4]
                                                            ["group"]!,
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Divider(
                                                    color: Colors.grey[300],
                                                    height: 20,
                                                  ),
                                                  Text(
                                                    "dcv.info_kerja.txt_dinas2"
                                                        .tr,
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    TranslationService
                                                        .translateBetweenLangs(
                                                      infoCVController.rhList[0]
                                                              .perjalananDinas ??
                                                          '-',
                                                      "konfirmasi2",
                                                      "id",
                                                      Get.locale?.languageCode
                                                              .toLowerCase() ??
                                                          "id",
                                                    ),
                                                    style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Colors.grey[500]),
                                                  ),
                                                  SizedBox(
                                                    height: 5,
                                                  ),
                                                  Text(
                                                    "dcv.info_kerja.hint_gaji"
                                                        .tr,
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    "Rp ${formatRupiah(infoCVController.rhList[0].minatGaji ?? '-')}",
                                                    style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Colors.grey[500]),
                                                  ),
                                                  SizedBox(
                                                    height: 5,
                                                  ),
                                                  Text(
                                                    "dcv.info_kerja.txt_lokasi"
                                                        .tr,
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    infoCVController.rhList[0]
                                                            .minatLokasiKerja ??
                                                        '-',
                                                    style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Colors.grey[500]),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = await Get.toNamed(
                                                  Routes.infoPekerjaan,
                                                  arguments: {
                                                    "dinas": infoCVController
                                                        .rhList[0]
                                                        .perjalananDinas,
                                                    "gaji": infoCVController
                                                        .rhList[0].minatGaji,
                                                    "lokasi": infoCVController
                                                        .rhList[0]
                                                        .minatLokasiKerja
                                                  },
                                                );

                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                }
                                              },
                                              child: Icon(
                                                Icons.edit,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        )))),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
