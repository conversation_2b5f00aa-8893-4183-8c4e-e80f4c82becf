import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/formater.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardPelatihanKursus extends StatelessWidget {
  const CardPelatihanKursus({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            children: [
                              Obx(
                                () => infoCVController.kursusList.isEmpty
                                    ? SizedBox()
                                    : Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.person,
                                                    size: 18,
                                                    color:
                                                        ColorAsset.primaryColor,
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text(
                                                    timelineData[2]["group"]!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  GestureDetector(
                                                    onTap: () async {
                                                      var result =
                                                          await Get.toNamed(
                                                              Routes.pelatihan,
                                                              arguments: {
                                                            "kursus": null,
                                                            "confirm": true
                                                          });

                                                      if (result == true) {
                                                        infoCVController
                                                            .loadRH();
                                                        infoCVController
                                                            .loadRiwayatKursus();
                                                      }
                                                    },
                                                    child: Icon(
                                                      Icons.add,
                                                      size: 18,
                                                      color: ColorAsset
                                                          .secodaryColor,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Divider(
                                            thickness: 1,
                                            height: 1,
                                            color: Colors.grey[300],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                        ],
                                      ),
                              ),
                              Obx(
                                () => Skeletonizer(
                                  enabled:
                                      infoCVController.isLoadingKursus.value,
                                  child: infoCVController.kursusList.isEmpty
                                      ? Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  infoCVController
                                                          .rhList.isNotEmpty
                                                      ? (infoCVController
                                                                  .rhList[0]
                                                                  .kursus ==
                                                              ""
                                                          ? Row(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Icon(
                                                                  Icons.person,
                                                                  size: 18,
                                                                  color: ColorAsset
                                                                      .primaryColor,
                                                                ),
                                                                SizedBox(
                                                                    width: 8),
                                                                Text(
                                                                  timelineData[
                                                                          2][
                                                                      "group"]!,
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                              ],
                                                            )
                                                          : infoCVController
                                                                      .rhList[0]
                                                                      .kursus ==
                                                                  "Tidak"
                                                              ? Row(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .center,
                                                                  children: [
                                                                    Icon(
                                                                      Icons
                                                                          .person,
                                                                      size: 18,
                                                                      color: ColorAsset
                                                                          .primaryColor,
                                                                    ),
                                                                    SizedBox(
                                                                        width:
                                                                            8),
                                                                    Text(
                                                                      "dcv.pelatihan.tidak_ada_kursus"
                                                                          .tr,
                                                                      style:
                                                                          TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                      ),
                                                                    )
                                                                  ],
                                                                )
                                                              : Text(
                                                                  infoCVController
                                                                          .rhList[
                                                                              0]
                                                                          .kursus ??
                                                                      '-',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ))
                                                      : Row(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Icon(
                                                              Icons.person,
                                                              size: 18,
                                                              color: ColorAsset
                                                                  .primaryColor,
                                                            ),
                                                            SizedBox(width: 8),
                                                            Text(
                                                              timelineData[2]
                                                                  ["group"]!,
                                                              style: TextStyle(
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                var result = await Get.toNamed(
                                                    Routes.pelatihan,
                                                    arguments: {
                                                      "kursus": null,
                                                      "confirm": false
                                                    });

                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                  infoCVController
                                                      .loadRiwayatKursus();
                                                }
                                              },
                                              child: Icon(
                                                Icons.add,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Column(
                                          children: List.generate(
                                            infoCVController.kursusList.length,
                                            (index) {
                                              final item = infoCVController
                                                  .kursusList[index];
                                              return Column(
                                                children: [
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              item.nama,
                                                              style: TextStyle(
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                            Text(
                                                              item.tempat,
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                            Text(
                                                              "(${ubahFormatDate(item.tglMulai)} - ${ubahFormatDate(item.tglSelesai)})",
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                            Text(
                                                              "${"dcv.pelatihan.txt_sertifikat_view".tr} ${item.sertifikat}",
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  color: Colors
                                                                          .grey[
                                                                      500]),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                        onTap: () async {
                                                          var result =
                                                              await Get.toNamed(
                                                                  Routes
                                                                      .pelatihan,
                                                                  arguments: {
                                                                "kursus": item,
                                                                "confirm": true
                                                              });

                                                          if (result == true) {
                                                            infoCVController
                                                                .loadRH();
                                                            infoCVController
                                                                .loadRiwayatKursus();
                                                          }
                                                        },
                                                        child: Icon(
                                                          Icons.edit,
                                                          size: 18,
                                                          color: ColorAsset
                                                              .secodaryColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  // Divider antar item, jangan ditambahkan di item terakhir
                                                  if (index !=
                                                      infoCVController
                                                              .pendidikanList
                                                              .length -
                                                          1)
                                                    Divider(
                                                      height: 16,
                                                      color: Colors.grey[300],
                                                    ),
                                                ],
                                              );
                                            },
                                          ),
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
