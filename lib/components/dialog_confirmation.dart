import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DialogConfirmation extends StatefulWidget {
  final String? title;
  final String? message;
  final String? buttonText;
  final String? cancelText;
  const DialogConfirmation(
      {super.key, this.title, this.message, this.buttonText, this.cancelText});

  @override
  State<DialogConfirmation> createState() => _DialogConfirmationState();
}

class _DialogConfirmationState extends State<DialogConfirmation> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.0)),
      child: Column(
        mainAxisSize: MainAxisSize.min, // penting biar tinggi menyesuaikan isi
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: <PERSON>um<PERSON>(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  (widget.title ?? "Konfirmasi").toUpperCase(),
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Text(
                  widget.message ?? "",
                  style: const TextStyle(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10), // jarak sebelum tombol
          Align(
            alignment: Alignment.bottomRight,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    (widget.cancelText ?? "tombol.batal".tr).toUpperCase(),
                    style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: Colors.red),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    (widget.buttonText ?? "konfirmasi.iya".tr).toUpperCase(),
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Future<bool?> showConfirmationDialog(BuildContext context, String title,
    String message, String btnText, String cancelText) async {
  return await showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return DialogConfirmation(
          title: title,
          message: message,
          buttonText: btnText,
          cancelText: cancelText);
    },
  );
}
