import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class KonfirmasiLogin extends StatelessWidget {
  const KonfirmasiLogin({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              ImageAssets.logo,
              width: MediaQuery.of(context).size.width * 0.4,
              color: ColorAsset.primaryColor,
            ),
            Text(
              "controller.judul_belum_login".tr,
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 10,
            ),
            SizedBox(
              // width: Get.width,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  Get.offAllNamed('/login');
                },
                style: ElevatedButton.styleFrom(
                    elevation: 0,
                    backgroundColor: ColorAsset.primaryColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 100,
                      vertical: 6,
                    ),
                    minimumSize: const Size.fromHeight(50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    textStyle: TextStyle(fontSize: 14)),
                child: Text("login.txt_btn_login".tr,
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
              ),
            ),
            SizedBox(
              height: 10,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "login.txt_belum_punya_akun".tr,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigasi ke halaman daftar
                    Get.toNamed(Routes.register);
                  },
                  child: Text(
                    "login.txt_btn_daftar".tr,
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorAsset.secodaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
