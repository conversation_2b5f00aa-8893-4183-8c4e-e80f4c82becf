import 'dart:convert';

import 'package:digital_cv_mobile/controllers/filter_beranda_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/models/filter_beranda_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FilterBerandaDialog extends StatefulWidget {
  const FilterBerandaDialog({super.key});

  @override
  State<FilterBerandaDialog> createState() => _FilterBerandaDialogState();
}

class _FilterBerandaDialogState extends State<FilterBerandaDialog> {
  final FilterBerandaController filterBerandaController =
      Get.put(FilterBerandaController());
  FilterBerandaModel filter = FilterBerandaModel();
  String? selectedWaktu;

  // final waktuOptions = <String>{
  //   "waktu_posting.txt_kapan_saja".tr,
  //   "waktu_posting.txt_hari_ini".tr,
  //   "waktu_posting.txt_3_hari".tr,
  //   "waktu_posting.txt_7_hari".tr,
  //   "waktu_posting.txt_14_hari".tr,
  //   "waktu_posting.txt_30_hari".tr,
  // };

  final waktuOptions = <String, String>{
    "waktu_posting.txt_kapan_saja": "waktu_posting.txt_kapan_saja".tr,
    "waktu_posting.txt_hari_ini": "waktu_posting.txt_hari_ini".tr,
    "waktu_posting.txt_3_hari": "waktu_posting.txt_3_hari".tr,
    "waktu_posting.txt_7_hari": "waktu_posting.txt_7_hari".tr,
    "waktu_posting.txt_14_hari": "waktu_posting.txt_14_hari".tr,
    "waktu_posting.txt_30_hari": "waktu_posting.txt_30_hari".tr,
  };

  @override
  void initState() {
    super.initState();
    selectedWaktu = "waktu_posting.txt_kapan_saja";
    loadFilter();
  }

  Future<void> loadFilter() async {
    final prefs = await SharedPreferences.getInstance();
    final filterString = prefs.getString('filter_beranda');

    if (filterString != null) {
      final jsonMap = jsonDecode(filterString);
      final loadedFilter = FilterBerandaModel.fromJson(jsonMap);

      setState(() {
        filter = loadedFilter;
        if (loadedFilter.waktuPosting != null) {
          selectedWaktu = loadedFilter.waktuPosting;
        } else {
          selectedWaktu = "waktu_posting.txt_kapan_saja";
        }
        filterBerandaController.selectedJenisPekerjaan.value = [
          ...loadedFilter.jenisPekerjaan
        ];
        filterBerandaController.selectedSpesialisasi.value = [
          ...loadedFilter.spesialisasi
        ];
        filterBerandaController.selectedPendidikan.value = [
          ...loadedFilter.pendidikan
        ];
      });
    } else {
      filter = FilterBerandaModel(); // kosong
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Stack(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(bottom: 70), // Beri ruang untuk tombol
            child: SingleChildScrollView(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Indikator drag
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(width: 50),
                        Container(
                          width: 50,
                          height: 5,
                          margin: const EdgeInsets.only(bottom: 10),
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          "beranda.filter".tr,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () async {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.remove('filter_beranda');

                            setState(() {
                              filter = FilterBerandaModel();
                              filterBerandaController.selectedJenisPekerjaan
                                  .clear();
                              filterBerandaController.selectedSpesialisasi
                                  .clear();
                              filterBerandaController.selectedPendidikan
                                  .clear();
                              selectedWaktu = '';
                            });
                          },
                          child: Text(
                            "beranda.reset".tr,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Divider(thickness: 1),
                    // Expansion Tile
                    ...[
                      Theme(
                        data: Theme.of(context).copyWith(
                          dividerColor: Colors.transparent,
                          splashColor: Colors.black,
                        ),
                        child: Obx(
                          () => ExpansionTile(
                            initiallyExpanded: filterBerandaController
                                .selectedJenisPekerjaan.isNotEmpty,
                            tilePadding: EdgeInsets.zero,
                            childrenPadding: EdgeInsets.zero,
                            title: Text(
                              "beranda.jenis_pekerjaan".tr,
                              style: const TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  alignment: WrapAlignment.start,
                                  crossAxisAlignment: WrapCrossAlignment.start,
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: [
                                    for (var item in [
                                      "jenis_pekerjaan.txt_full_time",
                                      "jenis_pekerjaan.txt_part_time",
                                      "jenis_pekerjaan.txt_internship",
                                      "jenis_pekerjaan.txt_contract",
                                    ])
                                      SizedBox(
                                        height: 30,
                                        child: OutlinedButton(
                                          onPressed: () {
                                            if (filter.jenisPekerjaan
                                                .contains(item)) {
                                              filter.jenisPekerjaan
                                                  .remove(item);
                                            } else {
                                              filter.jenisPekerjaan.add(item);
                                            }
                                            setState(() {
                                              if (filterBerandaController
                                                  .selectedJenisPekerjaan
                                                  .contains(item)) {
                                                filterBerandaController
                                                    .selectedJenisPekerjaan
                                                    .remove(item);
                                              } else {
                                                filterBerandaController
                                                    .selectedJenisPekerjaan
                                                    .add(item);
                                              }
                                            });
                                          },
                                          style: filterBerandaController
                                                  .selectedJenisPekerjaan
                                                  .contains(item)
                                              ? OutlinedButton.styleFrom(
                                                  backgroundColor:
                                                      ColorAsset.primaryColor,
                                                  minimumSize:
                                                      const Size(100, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                )
                                              : OutlinedButton.styleFrom(
                                                  minimumSize:
                                                      const Size(100, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                  side: BorderSide(
                                                    color: Colors.grey.shade400,
                                                    width: 1,
                                                  ),
                                                ),
                                          child: Text(
                                            item.tr,
                                            style: TextStyle(
                                                fontSize: 14,
                                                color: filterBerandaController
                                                        .selectedJenisPekerjaan
                                                        .contains(item)
                                                    ? Colors.black
                                                    : Colors.grey.shade500,
                                                fontWeight: FontWeight.w500),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Divider(thickness: 1),
                      Theme(
                        data: Theme.of(context).copyWith(
                          dividerColor: Colors.transparent,
                          splashColor: Colors.black,
                        ),
                        child: Obx(
                          () => ExpansionTile(
                            initiallyExpanded: filterBerandaController
                                .selectedSpesialisasi.isNotEmpty,
                            tilePadding: EdgeInsets.zero,
                            childrenPadding: EdgeInsets.zero,
                            title: Text(
                              "beranda.spesialisasi".tr,
                              style: const TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  alignment: WrapAlignment.start,
                                  crossAxisAlignment: WrapCrossAlignment.start,
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: [
                                    for (var item in [
                                      "spesialisasi.txt_administrasi",
                                      "spesialisasi.txt_akuntansi",
                                      "spesialisasi.txt_edukasi",
                                      "spesialisasi.txt_energi",
                                      "spesialisasi.txt_hukum",
                                      "spesialisasi.txt_keamanan",
                                      "spesialisasi.txt_kesehatan",
                                      "spesialisasi.txt_keuangan",
                                      "spesialisasi.txt_konstruksi",
                                      "spesialisasi.txt_manufaktur",
                                      "spesialisasi.txt_pemasaran",
                                      "spesialisasi.txt_pemerintaan",
                                      "spesialisasi.txt_perhotelan",
                                      "spesialisasi.txt_periklanan",
                                      "spesialisasi.txt_penjualan",
                                      "spesialisasi.txt_pertanian",
                                      "spesialisasi.txt_ritel",
                                      "spesialisasi.txt_seni",
                                      "spesialisasi.txt_teknologi_informasi",
                                      "spesialisasi.txt_telekomunikasi",
                                      "spesialisasi.txt_transporatsi",
                                    ])
                                      SizedBox(
                                        height: 30,
                                        child: OutlinedButton(
                                          onPressed: () {
                                            if (filter.spesialisasi
                                                .contains(item)) {
                                              filter.spesialisasi.remove(item);
                                            } else {
                                              filter.spesialisasi.add(item);
                                            }
                                            setState(() {
                                              if (filterBerandaController
                                                  .selectedSpesialisasi
                                                  .contains(item)) {
                                                filterBerandaController
                                                    .selectedSpesialisasi
                                                    .remove(item);
                                              } else {
                                                filterBerandaController
                                                    .selectedSpesialisasi
                                                    .add(item);
                                              }
                                            });
                                          },
                                          style: filterBerandaController
                                                  .selectedSpesialisasi
                                                  .contains(item)
                                              ? OutlinedButton.styleFrom(
                                                  backgroundColor:
                                                      ColorAsset.primaryColor,
                                                  minimumSize:
                                                      const Size(100, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                )
                                              : OutlinedButton.styleFrom(
                                                  minimumSize:
                                                      const Size(100, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                  side: BorderSide(
                                                    color: Colors.grey.shade400,
                                                    width: 1,
                                                  ),
                                                ),
                                          child: Text(
                                            item.tr,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: filterBerandaController
                                                      .selectedSpesialisasi
                                                      .contains(item)
                                                  ? Colors.black
                                                  : Colors.grey.shade500,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Divider(thickness: 1),
                      Theme(
                        data: Theme.of(context).copyWith(
                          dividerColor: Colors.transparent,
                          splashColor: Colors.black,
                        ),
                        child: Obx(
                          () => ExpansionTile(
                            initiallyExpanded: filterBerandaController
                                .selectedPendidikan.isNotEmpty,
                            tilePadding: EdgeInsets.zero,
                            childrenPadding: EdgeInsets.zero,
                            title: Text(
                              "beranda.pendidikan".tr,
                              style: const TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  alignment: WrapAlignment.start,
                                  crossAxisAlignment: WrapCrossAlignment.start,
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: [
                                    for (var item in [
                                      "dcv.pendidikan.jenjang1",
                                      "dcv.pendidikan.jenjang2",
                                      "dcv.pendidikan.jenjang8",
                                      "dcv.pendidikan.jenjang9",
                                      "dcv.pendidikan.jenjang10",
                                      "dcv.pendidikan.jenjang11",
                                      "dcv.pendidikan.jenjang12",
                                      "dcv.pendidikan.jenjang13",
                                      "dcv.pendidikan.jenjang5",
                                      "dcv.pendidikan.jenjang6",
                                      "dcv.pendidikan.jenjang7",
                                    ])
                                      SizedBox(
                                        height: 30,
                                        child: OutlinedButton(
                                          onPressed: () {
                                            if (filter.pendidikan
                                                .contains(item)) {
                                              filter.pendidikan.remove(item);
                                            } else {
                                              filter.pendidikan.add(item);
                                            }
                                            setState(() {
                                              if (filterBerandaController
                                                  .selectedPendidikan
                                                  .contains(item)) {
                                                filterBerandaController
                                                    .selectedPendidikan
                                                    .remove(item);
                                              } else {
                                                filterBerandaController
                                                    .selectedPendidikan
                                                    .add(item);
                                              }
                                            });
                                          },
                                          style: filterBerandaController
                                                  .selectedPendidikan
                                                  .contains(item)
                                              ? OutlinedButton.styleFrom(
                                                  backgroundColor:
                                                      ColorAsset.primaryColor,
                                                  minimumSize:
                                                      const Size(60, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(horizontal: 5),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                )
                                              : OutlinedButton.styleFrom(
                                                  minimumSize:
                                                      const Size(60, 30),
                                                  padding: const EdgeInsets
                                                      .symmetric(horizontal: 5),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                  side: BorderSide(
                                                    color: Colors.grey.shade400,
                                                    width: 1,
                                                  ),
                                                ),
                                          child: Text(
                                            item.tr,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: filterBerandaController
                                                      .selectedPendidikan
                                                      .contains(item)
                                                  ? Colors.black
                                                  : Colors.grey.shade500,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Divider(thickness: 1),
                      Theme(
                        data: Theme.of(context).copyWith(
                          dividerColor: Colors.transparent,
                          splashColor: Colors.black,
                        ),
                        child: ExpansionTile(
                          initiallyExpanded: selectedWaktu != "",
                          tilePadding: EdgeInsets.zero,
                          childrenPadding: EdgeInsets.zero,
                          title: Text(
                            "beranda.waktu".tr,
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.bold),
                          ),
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              alignment: Alignment.centerLeft,
                              child: Wrap(
                                children: waktuOptions.entries.map((entry) {
                                  final key = entry.key;
                                  final label = entry.value;

                                  return Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Radio<String>(
                                        value: key,
                                        groupValue: selectedWaktu,
                                        onChanged: (String? value) {
                                          filter.waktuPosting = value;
                                          setState(() {
                                            selectedWaktu = value!;
                                          });
                                        },
                                        visualDensity: const VisualDensity(
                                            horizontal: -4, vertical: -4),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      Text(label,
                                          style: const TextStyle(fontSize: 14)),
                                    ],
                                  );
                                }).toList(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          // Tombol Simpan floating di bawah
          Positioned(
            left: 16,
            right: 16,
            bottom: 20,
            child: Container(
              width: double.infinity,
              height: 45,
              color: Colors.white,
              child: ElevatedButton(
                onPressed: () async {
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.remove('filter_beranda');
                  await prefs.setString(
                      'filter_beranda', jsonEncode(filter.toJson()));
                  Get.back(result: filter);
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 100, vertical: 6),
                  minimumSize: const Size.fromHeight(45),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                  textStyle: const TextStyle(fontSize: 14),
                ),
                child: Text(
                  "tombol.filter_beranda".tr,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// void showDialogFilterBeranda(BuildContext context) {
//   showModalBottomSheet(
//     context: context,
//     isScrollControlled: true,
//     isDismissible: false,
//     enableDrag: true,
//     backgroundColor: Colors.transparent,
//     builder: (context) => FilterBerandaDialog(),
//   );
// }
