import 'package:flutter/material.dart';

class BottomSheetDialog extends StatefulWidget {
  const BottomSheetDialog({super.key});

  @override
  State<BottomSheetDialog> createState() => _BottomSheetDialogState();
}

class _BottomSheetDialogState extends State<BottomSheetDialog> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: () {
          showThreeQuarterBottomSheet(context);
        },
        child: const Text("Show Bottom Sheet"),
      ),
    );
  }

  void showThreeQuarterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true, // Bisa ditutup dengan tap di luar
      enableDrag: true, // Bisa ditutup dengan swipe ke bawah
      backgroundColor: Colors.transparent, // Supaya tap di luar bisa terdeteksi
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.75, // 3/4 layar
        minChildSize: 0.5, // Bisa diperkecil ke 50%
        maxChildSize: 0.75, // Maksimal tetap 3/4 layar
        expand: false,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Indikator drag
                Container(
                  width: 50,
                  height: 5,
                  margin: const EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const Text(
                  "3/4 Screen Bottom Sheet",
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    itemCount: 20,
                    itemBuilder: (context, index) => ListTile(
                      title: Text("Item ${index + 1}"),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
