import 'package:digital_cv_mobile/controllers/lowongan_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/image_asset.dart';
import 'package:digital_cv_mobile/helpers/str_assets.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CardLowongan extends StatelessWidget {
  final dynamic item;
  final LowonganController lowonganController;
  final void Function() onFavoriteChanged;

  const CardLowongan({
    Key? key,
    required this.item,
    required this.lowonganController,
    required this.onFavoriteChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Get.toNamed(Routes.lowonganDetail, arguments: item);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo
                  ClipRRect(
                    borderRadius: BorderRadius.circular(100),
                    child: (isValidImageUrl(item.logoURL) &&
                            item.logoURL.isNotEmpty)
                        ? Image.network(
                            item.logoURL,
                            fit: BoxFit.cover,
                            width: 70,
                            height: 70,
                            errorBuilder: (context, error, stackTrace) {
                              return Image.asset(
                                ImageAssets.logo2,
                                fit: BoxFit.cover,
                                width: 70,
                                height: 70,
                              );
                            },
                          )
                        : Image.asset(
                            ImageAssets.logo2,
                            fit: BoxFit.cover,
                            width: 70,
                            height: 70,
                          ),
                  ),
                  const SizedBox(width: 10),
                  // Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Posisi
                        Text(
                          item.posisi,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Perusahaan
                        Text(
                          item.perusahaan,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[500]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // Lamar
                        if (item.isLamar)
                          Row(
                            children: [
                              const Icon(
                                Icons.check_circle_outline,
                                color: Colors.green,
                                size: 14,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                "beranda.lamar".tr,
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  // Favorite
                  IconButton(
                    onPressed: () async {
                      item.isFavorit = !item.isFavorit;
                      onFavoriteChanged();
                      final success = await lowonganController.saveLowongan(
                        item.tempKode,
                        item.isFavorit,
                      );
                      if (success) {
                        await Future.delayed(const Duration(milliseconds: 300));
                        lowonganController.triggerRefreshTersimpan();
                      }
                    },
                    icon: Icon(
                      item.isFavorit
                          ? Icons.bookmark_added
                          : Icons.bookmark_added_outlined,
                      color: item.isFavorit ? ColorAsset.primaryColor : null,
                    ),
                  ),
                ],
              ),
              Divider(
                color: Colors.grey[300],
                height: 20,
              ),
              Row(
                children: [
                  // Spacer for logo
                  const SizedBox(width: 80),
                  // Lokasi dan tipe pekerjaan
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Lokasi kerja
                        Text(
                          item.lokasiKerja2,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[500],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 10),
                        // Tipe pekerjaan
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            TranslationService.translateBetweenLangs(
                                item.tipePekerjaan,
                                "jenis_pekerjaan",
                                "id",
                                Get.locale?.languageCode.toLowerCase() ?? "id"),
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
