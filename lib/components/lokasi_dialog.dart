import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LokasiDialog extends StatefulWidget {
  const LokasiDialog({super.key});

  @override
  State<LokasiDialog> createState() => _LokasiDialogState();
}

class _LokasiDialogState extends State<LokasiDialog> {
  int pageSize = 10;
  int page = 1;
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    controller.getLokasi("", page, pageSize);
  }

  void _onSearchChanged(String query) {
    if (query.length < 3) {
      return;
    }
    if (query.isEmpty) {
      controller.getLokasi("", page, pageSize);
    } else {
      controller.getLokasi(query, page, pageSize);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("lokasi.pilih_lokasi".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "lokasi.cari_lokasi".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (searchController.text.length < 3) {
                  return Center(child: Text("pencarian".tr));
                } else {
                  if (controller.lokasiList.isEmpty) {
                    return Center(child: Text("tidak_ada".tr));
                  }
                }

                // if (controller.lokasiList.isEmpty) {
                //   return const Center(child: Text("Tidak ada data"));
                // }

                return ListView.separated(
                  itemCount: controller.lokasiList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final lokasi = controller.lokasiList[index];
                    return ListTile(
                      title: Text(lokasi.nama),
                      onTap: () {
                        Get.back(result: lokasi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
