import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UrutanDialog extends StatefulWidget {
  const UrutanDialog({super.key});

  @override
  State<UrutanDialog> createState() => _UrutanDialogState();
}

class _UrutanDialogState extends State<UrutanDialog> {
  int pageSize = 10;
  int page = 1;
  // final LocationController controller = Get.find();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("urutan.txt_urutan".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            Expanded(
              child: ListView(
                children: [
                  ListTile(
                    title: Text("urutan.txt_relevan".tr),
                    onTap: () {
                      Get.back(result: "urutan.txt_relevan".tr);
                    },
                  ),
                  ListTile(
                    title: Text("urutan.txt_baru".tr),
                    onTap: () {
                      Get.back(result: "urutan.txt_baru".tr);
                    },
                  ),
                  ListTile(
                    title: Text("urutan.txt_fresh_graduate".tr),
                    onTap: () {
                      Get.back(result: "urutan.txt_fresh_graduate".tr);
                    },
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
