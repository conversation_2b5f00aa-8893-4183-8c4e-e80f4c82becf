import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class DateSeparatorWidget extends StatelessWidget {
  final DateTime date;

  const DateSeparatorWidget({
    super.key,
    required this.date,
  });

  String _formatDateLabel(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final inputDate = DateTime(date.year, date.month, date.day);

    // Calculate difference in days
    final difference = today.difference(inputDate).inDays;

    if (difference == 0) {
      return "Hari Ini";
    } else if (difference == 1) {
      return 'Kemarin';
    } else if (difference < 7) {
      // Show day name for this week: "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", etc.
      return DateFormat('EEEE', 'id_ID').format(date);
    } else if (date.year == now.year) {
      // Same year: "2 September"
      return DateFormat('d MMMM', 'id_ID').format(date);
    } else {
      // Different year: "2 September 2024"
      return DateFormat('d MMMM yyyy', 'id_ID').format(date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: Colors.grey.shade400,
              thickness: 1,
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Text(
                _formatDateLabel(date),
                style: TextStyle(
                  fontSize: Get.width * 0.035,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          Expanded(
            child: Divider(
              color: Colors.grey.shade400,
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }
}
