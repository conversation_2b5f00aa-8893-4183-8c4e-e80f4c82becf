import 'package:digital_cv_mobile/main.dart';
import 'package:flutter/material.dart';
import 'package:another_flushbar/flushbar.dart';
import 'package:get/get.dart';

class SnackBarService {
  void showSuccess(String message) {
    final context = navigatorKey.currentState?.overlay?.context;
    if (context != null) {
      showAnimatedSnackbar(message, Colors.green, Icons.check_circle);
    }
  }

  void showError(String message) {
    final context = navigatorKey.currentState?.overlay?.context;
    if (context != null) {
      showAnimatedSnackbar(message, Colors.red, Icons.error);
    }
  }

  void showInfo(String message) {
    final context = navigatorKey.currentState?.overlay?.context;
    if (context != null) {
      showAnimatedSnackbar(message, Colors.blue, Icons.info);
    }
  }

  void showWarning(String message) {
    final context = navigatorKey.currentState?.overlay?.context;
    if (context != null) {
      showAnimatedSnackbar(message, Colors.orange, Icons.warning);
    }
  }

  void showAnimatedSnackbar(String message, Color color, IconData icon) {
    Get.snackbar(
      '',
      '', // title dan message dikosongkan karena kita pakai `messageText` kustom
      backgroundColor: color,
      snackPosition: SnackPosition.BOTTOM,
      snackStyle: SnackStyle.FLOATING,
      margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
      padding: EdgeInsets.only(
          top: 0, bottom: 5, left: 12, right: 12), // Hapus padding default
      borderRadius: 8,
      duration: const Duration(seconds: 8),
      isDismissible: true,
      animationDuration: const Duration(milliseconds: 500),
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      titleText: const SizedBox.shrink(), // menghilangkan title space

      messageText: Align(
        alignment: Alignment.center,
        child: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
            TextButton(
              onPressed: () {
                if (Get.isSnackbarOpen) Get.back(); // Tutup snackbar manual
              },
              child: Text(
                "tombol.dismiss".tr,
                style: TextStyle(color: Colors.yellow),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void showAnimatedSnackbarError(BuildContext context, String message) {
  Flushbar(
    messageText: Row(
      children: [
        Icon(
          Icons.error,
          color: Colors.white,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ],
    ),
    backgroundColor: Colors.red,
    duration: const Duration(seconds: 4),
    flushbarPosition: FlushbarPosition.BOTTOM, // Muncul dari bawah
    animationDuration: const Duration(milliseconds: 500),
    forwardAnimationCurve: Curves.easeOutBack,
    reverseAnimationCurve: Curves.easeInBack, // Saat menghilang
    margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
    borderRadius: BorderRadius.circular(8),
    isDismissible: true,
    dismissDirection: FlushbarDismissDirection.VERTICAL,
    mainButton: TextButton(
      onPressed: () {
        Navigator.of(context).pop(true);
      },
      child: Text(
        "tombol.dismiss".tr,
        style: TextStyle(color: Colors.yellow),
      ),
    ),
  ).show(context);
}

void showAnimatedSnackbarSuccess(BuildContext context, String message) {
  Flushbar(
    messageText: Row(
      children: [
        Icon(
          Icons.check_circle,
          color: Colors.white,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ],
    ),
    backgroundColor: Color.fromARGB(255, 96, 206, 102),
    duration: const Duration(seconds: 4),
    flushbarPosition: FlushbarPosition.BOTTOM, // Muncul dari bawah
    animationDuration: const Duration(milliseconds: 500),
    forwardAnimationCurve: Curves.easeOutBack,
    reverseAnimationCurve: Curves.easeInBack, // Saat menghilang
    margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
    borderRadius: BorderRadius.circular(8),
    isDismissible: true,
    dismissDirection: FlushbarDismissDirection.VERTICAL,
    mainButton: TextButton(
      onPressed: () {
        Navigator.of(context).pop(true);
      },
      child: Text(
        "tombol.dismiss".tr,
        style: TextStyle(color: Colors.yellow),
      ),
    ),
  ).show(context);
}

void showAnimatedSnackbarInfo(BuildContext context, String message) {
  Flushbar(
    messageText: Row(
      children: [
        Icon(
          Icons.info,
          color: Colors.white,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ],
    ),
    backgroundColor: Color.fromARGB(255, 60, 135, 255),
    duration: const Duration(seconds: 4),
    flushbarPosition: FlushbarPosition.BOTTOM, // Muncul dari bawah
    animationDuration: const Duration(milliseconds: 500),
    forwardAnimationCurve: Curves.easeOutBack,
    reverseAnimationCurve: Curves.easeInBack, // Saat menghilang
    margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
    borderRadius: BorderRadius.circular(8),
    isDismissible: true,
    dismissDirection: FlushbarDismissDirection.VERTICAL,
    mainButton: TextButton(
      onPressed: () {
        Navigator.of(context).pop(true);
      },
      child: Text(
        "tombol.dismiss".tr,
        style: TextStyle(color: Colors.yellow),
      ),
    ),
  ).show(context);
}

void showAnimatedSnackbarWarning(BuildContext context, String message) {
  Flushbar(
    messageText: Row(
      children: [
        Icon(
          Icons.warning,
          color: Colors.black,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            message,
            style: const TextStyle(color: Colors.black, fontSize: 14),
          ),
        ),
      ],
    ),
    backgroundColor: Colors.amber,
    duration: const Duration(seconds: 4),
    flushbarPosition: FlushbarPosition.BOTTOM, // Muncul dari bawah
    animationDuration: const Duration(milliseconds: 500),
    forwardAnimationCurve: Curves.easeOutBack,
    reverseAnimationCurve: Curves.easeInBack, // Saat menghilang
    margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
    borderRadius: BorderRadius.circular(8),
    isDismissible: true,
    dismissDirection: FlushbarDismissDirection.VERTICAL,
    mainButton: TextButton(
      onPressed: () {
        Navigator.of(context).pop(true);
      },
      child: Text(
        "tombol.dismiss".tr,
        style: TextStyle(color: Colors.black),
      ),
    ),
  ).show(context);
}

void showAnimatedSnackbar(String message, Color color, IconData icon) {
  Get.snackbar(
    '',
    '', // title dan message dikosongkan karena kita pakai `messageText` kustom
    backgroundColor: color,
    snackPosition: SnackPosition.BOTTOM,
    snackStyle: SnackStyle.FLOATING,
    margin: const EdgeInsets.fromLTRB(12, 0, 12, 70),
    padding: EdgeInsets.only(
        top: 0, bottom: 5, left: 12, right: 12), // Hapus padding default
    borderRadius: 8,
    duration: const Duration(seconds: 4),
    isDismissible: true,
    animationDuration: const Duration(milliseconds: 500),
    forwardAnimationCurve: Curves.easeOutBack,
    reverseAnimationCurve: Curves.easeInBack,
    titleText: const SizedBox.shrink(), // menghilangkan title space

    messageText: Align(
      alignment: Alignment.center,
      child: Row(
        children: [
          Icon(icon, color: Colors.white),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
          TextButton(
            onPressed: () {
              if (Get.isSnackbarOpen) Get.back(); // Tutup snackbar manual
            },
            child: Text(
              "tombol.dismiss".tr,
              style: TextStyle(color: Colors.yellow),
            ),
          ),
        ],
      ),
    ),
  );
}
