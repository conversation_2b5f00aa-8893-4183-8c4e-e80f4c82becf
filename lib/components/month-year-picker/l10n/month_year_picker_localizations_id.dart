import 'month_year_picker_localizations.dart';

/// The translations for Indonesian (`id`).
class MonthYearPickerLocalizationsId extends MonthYearPickerLocalizations {
  MonthYearPickerLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get helpText => 'PILIH BULAN/TAHUN';

  @override
  String get okButtonLabel => 'OK';

  @override
  String get cancelButtonLabel => 'BATAL';
}
