import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DiaologChooseBahasa extends StatefulWidget {
  const DiaologChooseBahasa({super.key});

  @override
  State<DiaologChooseBahasa> createState() => _DiaologChooseBahasaState();
}

class _DiaologChooseBahasaState extends State<DiaologChooseBahasa> {
  String codeLanguage = 'id';

  @override
  void initState() {
    super.initState();
    _loadLanguage();
  }

  // Fungsi untuk mengambil bahasa yang tersimpan
  Future<void> _loadLanguage() async {
    final pref = await SharedPreferences.getInstance();
    setState(() {
      codeLanguage = pref.getString("codeLanguage") ?? 'id';
    });
  }

  Future<void> _saveLanguage() async {
    final pref = await SharedPreferences.getInstance();
    await pref.setString("codeLanguage", codeLanguage);

    // Ubah bahasa di GetX
    Get.updateLocale(Locale(codeLanguage));

    // Tutup modal
    Navigator.pop(context);
  }

  Widget _buildLanguageItem(String label, String langCode) {
    final isSelected = codeLanguage == langCode;
    return SizedBox(
      height: 40,
      child: InkWell(
        onTap: () {
          setState(() {
            codeLanguage = langCode;
          });
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? ColorAsset.primaryColor : Colors.black,
              ),
            ),
            if (isSelected) Icon(Icons.check, color: ColorAsset.primaryColor),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.5,
      maxChildSize: 0.5,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 50,
                height: 5,
                margin: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.close),
                  ),
                  Text(
                    "pengaturan.bahasa".tr,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const Divider(thickness: 1, height: 1),
              SizedBox(height: 16),

              // // Bahasa Indonesia
              // Container(
              //   height: 40,
              //   child: Material(
              //     child: InkWell(
              //       onTap: () async {
              //         setState(() {
              //           isBahasa = true; // Perbarui state secara langsung
              //         });
              //       },
              //       child: Row(
              //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //         children: [
              //           Text(
              //             "Bahasa Indonesia",
              //             style: TextStyle(
              //               fontSize: 12,
              //               color: isBahasa
              //                   ? ColorAsset.primaryColor
              //                   : Colors.black,
              //             ),
              //           ),
              //           if (isBahasa)
              //             Icon(
              //               Icons.check,
              //               color: ColorAsset.primaryColor,
              //             ),
              //         ],
              //       ),
              //     ),
              //   ),
              // ),

              // // Bahasa Inggris
              // Container(
              //   height: 40,
              //   child: Material(
              //     child: InkWell(
              //       onTap: () async {
              //         setState(() {
              //           isBahasa = false; // Perbarui state secara langsung
              //         });
              //       },
              //       child: Row(
              //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //         children: [
              //           Text(
              //             "English",
              //             style: TextStyle(
              //               fontSize: 12,
              //               color: !isBahasa
              //                   ? ColorAsset.primaryColor
              //                   : Colors.black,
              //             ),
              //           ),
              //           if (!isBahasa)
              //             Icon(
              //               Icons.check,
              //               color: ColorAsset.primaryColor,
              //             ),
              //         ],
              //       ),
              //     ),
              //   ),
              // ),

              // // Bahasa China
              // Container(
              //   height: 40,
              //   child: Material(
              //     child: InkWell(
              //       onTap: () async {
              //         setState(() {
              //           isBahasa = false; // Perbarui state secara langsung
              //         });
              //       },
              //       child: Row(
              //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //         children: [
              //           Text(
              //             "中文",
              //             style: TextStyle(
              //               fontSize: 12,
              //               color: !isBahasa
              //                   ? ColorAsset.primaryColor
              //                   : Colors.black,
              //             ),
              //           ),
              //           if (!isBahasa)
              //             Icon(
              //               Icons.check,
              //               color: ColorAsset.primaryColor,
              //             ),
              //         ],
              //       ),
              //     ),
              //   ),
              // ),

              // Pilihan Bahasa
              _buildLanguageItem("Bahasa Indonesia", "id"),
              _buildLanguageItem("English", "en"),
              _buildLanguageItem("中文", "cn"),

              Spacer(),

              Align(
                alignment: Alignment.bottomCenter,
                child: ElevatedButton(
                  onPressed: () async {
                    _saveLanguage();
                  },
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    minimumSize: const Size.fromHeight(45),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    textStyle: const TextStyle(fontSize: 14),
                  ),
                  child: Text(
                    "tombol.simpan".tr,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

void showDialogBahasa(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    isDismissible: true,
    enableDrag: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DiaologChooseBahasa(),
  );
}
