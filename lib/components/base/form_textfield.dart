import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FormTextFiled extends StatelessWidget {
  final TextEditingController controller;
  final TextInputType keyboardType;
  final String hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? errorText;
  final String? type;
  final ValueChanged<String>? onCountryCodeChanged;
  final String? title;
  final bool? isRequired;
  final bool isReadOnly;
  final int? maxLines;
  final int? minLines;
  final bool? expands;
  final Function()? onTap;
  final bool? obscureText;
  final String? tooltipMessage;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String)? onChanged;

  FormTextFiled(
      {super.key,
      required this.controller,
      required this.keyboardType,
      this.hintText = "",
      this.prefixIcon,
      this.suffixIcon,
      this.type,
      this.errorText,
      this.onCountryCodeChanged,
      this.selectedCountryCode = "+62",
      this.title,
      this.isRequired = false,
      this.isReadOnly = false,
      this.maxLines,
      this.minLines,
      this.expands = false,
      this.onTap,
      this.obscureText = false,
      this.inputFormatters,
      this.onChanged,
      this.tooltipMessage});

  final String selectedCountryCode;

  void _emitCountryCodeChanged(String value) {
    if (onCountryCodeChanged != null) {
      onCountryCodeChanged!(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  Text(
                    title!,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  if (isRequired == true)
                    Text(
                      '*',
                      style: TextStyle(
                        color: Colors.red,
                      ),
                    ),
                  if (tooltipMessage != null)
                    SizedBox(
                      width: 5,
                    ),
                  // if (tooltipMessage != null)
                  //   TapTooltip(
                  //     message: tooltipMessage ?? '',
                  //     child: Icon(
                  //       Icons.info_outline,
                  //       size: 15,
                  //     ),
                  //   ),
                ],
              ),
            ),
          ),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.only(
              topLeft: type == 'otp' ? Radius.circular(0) : Radius.circular(8),
              topRight: type == 'total_pengalaman'
                  ? Radius.circular(0)
                  : Radius.circular(8),
              bottomLeft:
                  type == 'otp' ? Radius.circular(0) : Radius.circular(8),
              bottomRight: type == 'total_pengalaman'
                  ? Radius.circular(0)
                  : Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              if (type == 'phone')
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      return DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          menuMaxHeight: 500,
                          value: selectedCountryCode,
                          items: Utilities.countryCodes.map((country) {
                            return DropdownMenuItem<String>(
                              value: country['code'],
                              child: Row(
                                children: [
                                  Text(country['flag'] ?? ''),
                                  SizedBox(width: 8),
                                  Text(country['code'] ?? ''),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            _emitCountryCodeChanged(value!);
                          },
                        ),
                      );
                    },
                  ),
                ),
              Expanded(
                child: TextFormField(
                  controller: controller,
                  style: TextStyle(fontSize: 14),
                  inputFormatters: inputFormatters,
                  keyboardType: keyboardType,
                  readOnly: isReadOnly,
                  minLines: minLines ?? 1,
                  obscureText: obscureText ?? false,
                  onTap: onTap,
                  maxLines: maxLines ?? 1,
                  onChanged: onChanged,
                  expands: expands ?? false,
                  textInputAction: TextInputAction.next,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(fontSize: 14),
                    prefixIcon: prefixIcon,
                    suffixIcon: suffixIcon,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: type == "phone"
                            ? 20
                            : suffixIcon != null
                                ? 10
                                : 10),
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Icon(
                    Icons.error,
                    color: Colors.red,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      errorText!,
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
