import 'package:flutter/material.dart';

/// Widget wrapper yang memastikan tidak terpengaruh oleh text scaling sistem
class NoTextScaleWrapper extends StatelessWidget {
  final Widget child;
  final double textScaleFactor;

  const NoTextScaleWrapper({
    Key? key,
    required this.child,
    this.textScaleFactor = 1.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        textScaler: TextScaler.linear(textScaleFactor),
      ),
      child: child,
    );
  }
}

/// Widget untuk teks yang tidak terpengaruh scaling
class FixedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? softWrap;

  const FixedText(
    this.text, {
    Key? key,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.softWrap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return NoTextScaleWrapper(
      child: Text(
        text,
        style: style?.copyWith(inherit: false) ??
            TextStyle(inherit: false, fontFamily: 'Poppins'),
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
        softWrap: softWrap,
      ),
    );
  }
}

/// Widget untuk rich text yang tidak terpengaruh scaling
class FixedRichText extends StatelessWidget {
  final InlineSpan text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? softWrap;
  final StrutStyle? strutStyle;

  const FixedRichText({
    Key? key,
    required this.text,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.softWrap,
    this.strutStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return NoTextScaleWrapper(
      child: RichText(
        text: text,
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.clip,
        textAlign: textAlign ?? TextAlign.start,
        softWrap: softWrap ?? true,
        strutStyle: strutStyle,
      ),
    );
  }
}

/// Scaffold yang tidak terpengaruh text scaling
class FixedScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;
  final bool? resizeToAvoidBottomInset;

  const FixedScaffold({
    Key? key,
    this.appBar,
    this.body,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.resizeToAvoidBottomInset,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return NoTextScaleWrapper(
      child: Scaffold(
        appBar: appBar,
        body: body,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
        drawer: drawer,
        endDrawer: endDrawer,
        backgroundColor: backgroundColor,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      ),
    );
  }
}
