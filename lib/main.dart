import 'dart:async';
import 'dart:io';

import 'package:android_intent_plus/android_intent.dart';
import 'package:android_intent_plus/flag.dart';
import 'package:app_links/app_links.dart';
import 'package:app_settings/app_settings.dart';
import 'package:digital_cv_mobile/components/month-year-picker/l10n/month_year_picker_localizations.dart';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/firebase_options.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/notification_service.dart';
import 'package:digital_cv_mobile/helpers/ssl_pinning_override.dart';
import 'package:digital_cv_mobile/helpers/translations.dart';
import 'package:digital_cv_mobile/helpers/translator_service.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:digital_cv_mobile/services/security_service.dart';
import 'package:digital_cv_mobile/services/websocket_service.dart';
import 'package:digital_cv_mobile/views/location_permission_screen.dart';
import 'package:digital_cv_mobile/views/reset_password_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:upgrader/upgrader.dart';

//generate keystore
// keytool -genkey -v -keystore $env:USERPROFILE\upload-keystore.jks `
//         -storetype JKS -keyalg RSA -keysize 2048 -validity 10000 `
//         -alias upload
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
const kWindowsScheme = 'sample';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform);
      if (kDebugMode) {
        print('Firebase initialized in background handler');
      }
    } else {
      if (kDebugMode) {
        print('Firebase already initialized in background handler');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('Firebase background initialization error: $e');
    }
    return;
  }

  if (kDebugMode) {
    print("Handling a background message: ${message.messageId}");
    print('Message data: ${message.data}');
    print('Message notification: ${message.notification?.title}');
    print('Message notification: ${message.notification?.body}');
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // SSL Pinning: load trusted certificate
  // ByteData certData = await rootBundle.load('assets/ca/lets-encrypt-r3.pem');
  // SecurityContext.defaultContext
  //     .setTrustedCertificatesBytes(certData.buffer.asUint8List());

  // Optional: override badCertificateCallback for dev/testing only

  await Upgrader.clearSavedSettings();

  HttpOverrides.global = MyHttpOverrides();

  Get.put(SnackBarService());
  Get.put(FlutterSecureStorage());
  Get.put(WebSocketService()); // Initialize WebSocket service
  await AppTranslations
      .loadTranslations(); // Muat JSON sebelum menjalankan aplikasi
  await TranslationService.init();

  // Initialize Firebase first, before any Firebase-related operations
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform);
      if (kDebugMode) {
        print('Firebase initialized successfully with options');
      }
    } else {
      if (kDebugMode) {
        print('Firebase already initialized in main');
      }
    }
  } catch (e) {
    if (e is FirebaseException && e.code == 'duplicate-app') {
      if (kDebugMode) {
        print('Duplicate app error caught, using existing instance');
      }
      // Do not return, just use existing instance
    } else {
      if (kDebugMode) {
        print('Firebase initialization error: $e');
      }
      // Don't continue if Firebase fails to initialize
      return;
    }
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  final prefs = await SharedPreferences.getInstance();
  Get.put(
      prefs); // Simpan instance SharedPreferences di GetX// ✅ Tambahkan ini agar plugin siap

  final isSecure = await isDeviceSecure();
  if (!isSecure) {
    // Bisa keluar dari app, tunjukkan peringatan, dll.
    runApp(JailbreakWarningApp());

    return;
  }
  runApp(ShowCaseWidget(
    builder: (context) {
      return MyApp();
    },
  ));
}

// Simple warning app for jailbroken/rooted devices
class JailbreakWarningApp extends StatelessWidget {
  const JailbreakWarningApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      builder: (context, widget) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            devicePixelRatio: 0.5, // Lock device pixel ratio
            textScaler: TextScaler.linear(0.9), // Lock text scale to 0.7
          ),
          child: widget!,
        );
      },
      home: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.warning, color: Colors.red, size: 64),
                const SizedBox(height: 24),
                const Text(
                  'Device Security Warning',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Text(
                  'This device appears to be rooted. For your security, the app cannot be used on this device.',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: () {
                        if (Platform.isAndroid) {
                          final intent = AndroidIntent(
                            action:
                                'android.settings.APPLICATION_DEVELOPMENT_SETTINGS',
                            flags: <int>[Flag.FLAG_ACTIVITY_NEW_TASK],
                          );

                          intent.launch();
                        } else {
                          AppSettings.openAppSettings();
                        }
                        LogService.log.i(
                            "⚠️ User attempted to use app on rooted/jailbroken device.");
                      },
                      style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: ColorAsset.primaryColor,
                          padding: const EdgeInsets.symmetric(
                            // horizontal: 100,
                            vertical: 6,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                          textStyle: TextStyle(fontSize: 14)),
                      child: Text('Disable Developer Mode',
                          style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold))),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  NotificationServices notificationServices = NotificationServices();
  late final AppLinks _appLinks;
  late final StreamSubscription<Uri>? _linkSubscription;
  Uri? _initialUri;
  final prefs = FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    initLocation();
    _appLinks = AppLinks();
    _linkSubscription = _appLinks.uriLinkStream.listen((Uri uri) {
      _initialUri = uri;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleIncomingLink();
      });
    }); // tetap seperti biasa

    notificationServices.requestNotificationPermission();
    _initializeMessaging();
    _loadLanguage();
  }

  void initLocation() async {
    if (Platform.isAndroid) {
      bool isLocationActive = await _checkLocationEnabled();
      if (!isLocationActive) {
        Get.to(() => LocationPermissionScreen());
      }
    }
  }

  Future<bool> _checkLocationEnabled() async {
    // Check if location services are enabled on device
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    // Check location permission
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  Future<void> _initializeMessaging() async {
    try {
      if (Platform.isIOS) {
        // For iOS, we need to ensure APNS token is set first
        final settings = await FirebaseMessaging.instance.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        if (settings.authorizationStatus == AuthorizationStatus.authorized) {
          // Wait for APNS token
          final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
          if (kDebugMode) {
            print('APNS Token: $apnsToken');
          }

          // Only get FCM token after APNS token is set
          if (apnsToken != null) {
            final fcmToken = await FirebaseMessaging.instance.getToken();
            if (fcmToken != null && mounted) {
              if (kDebugMode) {
                print('FCM Token: $fcmToken');
              }
              prefs.write(key: "fcm_token", value: fcmToken);
            }
          }
        }
      } else {
        // For Android, we can get the token directly
        final fcmToken = await FirebaseMessaging.instance.getToken();
        if (fcmToken != null && mounted) {
          if (kDebugMode) {
            print('FCM Token: $fcmToken');
          }
          prefs.write(key: "fcm_token", value: fcmToken);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing messaging: $e');
      }
    }
  }

  Future<void> _loadLanguage() async {
    final pref = await SharedPreferences.getInstance();
    String codeLanguage = pref.getString("codeLanguage") ?? "id";
    Get.updateLocale(Locale(codeLanguage));
    await pref.setBool('hasShownTutorial', false);
  }

  void _handleIncomingLink() async {
    if (_initialUri != null) {
      if (_initialUri?.path == "/candidate/reset-password.php") {
        final token = _initialUri?.queryParameters["token"];
        if (token != null) {
          // Tunggu hingga build selesai
          await Future.delayed(Duration(milliseconds: 500));
          Get.to(() => ResetPasswordScreen(token: token));
        }
      }
      _initialUri = null;
    }
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final upgrader = Upgrader(
      debugLogging: true,
    );

    return UpgradeAlert(
      upgrader: upgrader,
      showIgnore: false, // sembunyikan tombol "abaikan"
      showLater: false,
      showReleaseNotes: true,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent, // Transparan atau warna tertentu
          statusBarIconBrightness: Brightness.dark, // Teks hitam
          statusBarBrightness: Brightness.light, // Untuk iOS
        ),
        child: GetMaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          initialRoute: Routes.home, // Halaman pertama
          getPages: AppRoutes.pages, // Daftar rute
          initialBinding: BindingsBuilder(() {
            Get.put(SnackBarService());
            Get.put(FlutterSecureStorage());
            // Get.put<SharedPreferences>(prefs);
          }),
          translations:
              AppTranslations(), // Menggunakan JSON sebagai sumber teks
          locale: Locale('id'), // Default ke Bahasa Indonesia
          localizationsDelegates: const [
            GlobalWidgetsLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            MonthYearPickerLocalizations.delegate,
          ],
          fallbackLocale: Locale('en'),
          // Nonaktifkan text scaling dari sistem
          builder: (context, widget) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                devicePixelRatio: 0.5, // Lock device pixel ratio
                textScaler: TextScaler.linear(0.9), // Lock text scale to 0.7
              ),
              child: widget!,
            );
          },
          theme: ThemeData(
            fontFamily: "Poppins", // Pastikan sesuai dengan `pubspec.yaml`
            textTheme: const TextTheme(
              bodyLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              bodyMedium: TextStyle(fontSize: 16),
              bodySmall: TextStyle(fontSize: 14),
            ),
            visualDensity: VisualDensity.standard,
            primaryColor: ColorAsset.primaryColor, // Warna utama
            colorScheme: ColorScheme.fromSeed(
              seedColor: ColorAsset.primaryColor, // Warna dasar
              primary: ColorAsset.primaryColor, // Warna tombol utama
              secondary: Colors.orange, // Warna aksen/sekunder
            ),

            // Warna default untuk AppBar
            appBarTheme: const AppBarTheme(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white, // Warna teks
            ),

            // Warna tombol
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorAsset.primaryColor, // Warna tombol
                foregroundColor: Colors.white, // Warna teks
              ),
            ),
          ),
        ),
      ),
    );
  }
}
