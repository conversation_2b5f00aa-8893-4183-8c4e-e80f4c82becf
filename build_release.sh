#!/bin/bash

# Flutter Build Script with Obfuscation
# Usage: ./build_release.sh [platform]
# Platforms: android, ios, web, windows, linux, macos

PLATFORM=${1:-android}
BUILD_DIR="build/debug_info"

echo "Building Flutter app for $PLATFORM with obfuscation..."

# Create debug info directory if it doesn't exist
mkdir -p $BUILD_DIR

case $PLATFORM in
  "android")
    echo "Building Android APK with obfuscation..."
    flutter build apk --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  "android-bundle")
    echo "Building Android App Bundle with obfuscation..."
    flutter build appbundle --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  "ios")
    echo "Building iOS with obfuscation..."
    echo "Note: This will build for simulator. For device/App Store, use Xcode."
    flutter build ios --release --obfuscate --split-debug-info=$BUILD_DIR --simulator
    ;;
  "ios-device")
    echo "Building iOS for device with runtime signature validation..."
    echo "Building IPA for App Store distribution..."
    
    # Build IPA with proper debug info path
    flutter build ipa \
      --release \
      --obfuscate \
      --split-debug-info="$BUILD_DIR" \
      --export-method=app-store
    
    echo "✅ IPA build completed. You can find the IPA in: build/ios/ipa/"
    
    # Additional security measures for iOS build
    echo "Configuring runtime signature validation..."
    
    # Check if entitlements file exists
    if [ ! -f "ios/Runner/Runner.entitlements" ]; then
        echo "⚠️  Warning: Entitlements file not found. Runtime signature validation may not work properly."
    fi
    
    # Validate project configuration
    if grep -q "CODE_SIGN_ENTITLEMENTS" ios/Runner.xcodeproj/project.pbxproj; then
        echo "✅ Entitlements configuration found in project"
    else
        echo "⚠️  Warning: Entitlements not configured in project"
    fi
    
    open ios/Runner.xcworkspace
    echo "Please configure signing in Xcode and archive manually"
    echo "Important: Ensure 'Automatic' signing is enabled for runtime signature validation"
    exit 0
    ;;
  "web")
    echo "Building Web with obfuscation..."
    flutter build web --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  "windows")
    echo "Building Windows with obfuscation..."
    flutter build windows --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  "linux")
    echo "Building Linux with obfuscation..."
    flutter build linux --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  "macos")
    echo "Building macOS with obfuscation..."
    flutter build macos --release --obfuscate --split-debug-info=$BUILD_DIR
    ;;
  *)
    echo "Unknown platform: $PLATFORM"
    echo "Available platforms: android, android-bundle, ios, ios-device, web, windows, linux, macos"
    exit 1
    ;;
esac

echo "Build completed! Debug info saved to: $BUILD_DIR"
echo "Remember to keep the debug info files for crash reporting and debugging."
