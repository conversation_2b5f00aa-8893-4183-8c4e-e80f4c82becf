{"data_kosong": "Data kosong!", "form_kosong": "Silahkan lengkapi data diri anda terlebih dahulu!", "login": {"txt_title": "<PERSON><PERSON><PERSON> ke Akun Anda", "txt_email": "Email", "txt_pass": "<PERSON><PERSON>", "txt_forgot_pass": "Lupa Password?", "txt_btn_login": "MASUK", "txt_btn_daftar": "<PERSON><PERSON><PERSON>", "txt_divider": "Atau <PERSON>", "txt_btn_login_google": "MASUK DENGAN GOOGLE", "txt_belum_punya_akun": "Belum punya akun?", "txt_error_email": "Email tidak boleh kosong", "txt_error_password": "Password tidak boleh kosong", "txt_remember_me": "<PERSON>gat saya", "txt_explore_jobs": "<PERSON><PERSON><PERSON><PERSON>"}, "daftar": {"judul": "<PERSON><PERSON><PERSON>", "txt_nama": "<PERSON><PERSON>", "txt_email": "Email", "txt_email_baru": "Masukkan email baru", "txt_OTP": "Masukkan OTP yang dikirim lewat email", "txt_OTP_baru": "Masukkan OTP yang dikirim lewat email baru", "txt_no_handphone": "Nomor Handphone", "txt_password": "<PERSON><PERSON>", "txt_confirm_pass": "<PERSON>n<PERSON><PERSON><PERSON>", "txt_konfirm_tnc1": "<PERSON><PERSON>", "txt_konfirm_tnc2": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "txt_konfirm_tnc3": "kami", "txt_btn_daftar": "DAFTAR", "txt_error_nama": "<PERSON>a leng<PERSON>p tidak boleh kosong", "txt_error_email": "Email tidak boleh kosong", "txt_error_OTP": "OTP tidak boleh kosong", "txt_error_no_handphone": "Nomor handphone tidak boleh kosong", "txt_error_password": "Kata sandi tidak boleh kosong", "txt_error_confirm_pass": "Konfirmasi kata sandi tidak boleh kosong", "txt_pass_tidak_sama": "Kata sandi tidak sesuai", "txt_error_konfirm_tnc": "Setujui/ceklis terlebih dahulu ketentuan dan kebijakan kami!", "txt_error_email_hp": "Email dan No Handphone Harus Diisi Terlebih Dahulu!", "format_email_invalid": "Format email tidak valid!", "no_hanphone_invalid": "Format nomor handphone tidak valid!", "length_password_invalid": "Kata sandi harus lebih dari 6 karakter!"}, "drawer": {"beranda": "Be<PERSON><PERSON>", "tersimpan": "<PERSON><PERSON><PERSON><PERSON>", "riwayat": "Riwayat", "profil": "Profil", "selamat_pagi": "<PERSON><PERSON><PERSON>", "selamat_siang": "Selamat <PERSON>ang", "selamat_sore": "Selamat Sore", "selamat_malam": "Selamat Malam"}, "beranda": {"hint_searching": "<PERSON>i nama p<PERSON>/per<PERSON><PERSON>an", "filter": "Filter", "reset": "Reset", "jenis_pekerjaan": "<PERSON><PERSON>", "spesialisasi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendidikan": "Pendidikan", "waktu": "<PERSON><PERSON><PERSON>", "filter_dipilih": "Filter dipilih:", "lamar": "<PERSON><PERSON>", "data_kosong": "Tidak ada lowongan yang di<PERSON>n", "data_kosong_desc": "Silahkan ubah filter pencarian atau coba lagi nanti"}, "tersimpan": {"judul": "<PERSON><PERSON><PERSON>", "data_kosong": "Tidak ada <PERSON>an tersimpan", "data_kosong_desc": "Anda belum menyimpan lowongan apapun"}, "riwayat": {"judul": "<PERSON><PERSON><PERSON><PERSON>", "subjudul1": "lamaran dalam", "subjudul2": "hari te<PERSON><PERSON>", "data_kosong": "Tidak ada riwayat lamaran kerja", "data_kosong_desc": "Anda belum melamar peker<PERSON>an a<PERSON>n"}, "profil": {"judul1": "Profil dan <PERSON><PERSON>", "judul2": "Lain-lain", "profildcv": "Profil digitalcv", "cv": "CV ATS", "preferensi": "Preferensi Kerja", "upgrade": "Upgrade Plan", "rh": "Personal File", "info_kontak": "Informasi Kontak"}, "pengaturan": {"judul": "<PERSON><PERSON><PERSON><PERSON>", "akun": "<PERSON><PERSON><PERSON>", "rating": "<PERSON><PERSON>", "masukan": "<PERSON><PERSON>", "bantuan": "<PERSON><PERSON><PERSON> dan <PERSON>", "privasi": "<PERSON><PERSON><PERSON><PERSON>", "penggunaan": "<PERSON><PERSON><PERSON><PERSON>", "bahasa": "Bahasa / Language", "versi": "Versi aplikasi ", "rating_masukan": "<PERSON><PERSON> dan <PERSON>", "ketentuan_privasi": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>"}, "akun": {"judul": "<PERSON><PERSON><PERSON>", "email": "Email", "password": "<PERSON><PERSON>", "whatsapp": "WhatsApp", "noTelp": "Nomor Telepon", "visibilitas": "Pengaturan visibilitas profil", "visibilitas_title": "Da<PERSON>t dicari o<PERSON>h <PERSON>", "status_kerja": "Status Ke<PERSON><PERSON><PERSON>", "visibilitas_subtitle": "<PERSON><PERSON><PERSON><PERSON> Anda bersedia jika profil anda dicari oleh per<PERSON>an lain dengan peluang kerja sesuai dengan kriteria yang Anda miliki.", "visibilitas_aktif_desc": "<PERSON><PERSON><PERSON><PERSON> dapat mengakses profil dan resume saya serta dapat menghubungi saya secara langsung atau melalui digitalcv.", "visibilitas_deaktif_desc": "Perusahaan tidak dapat mencari saya. Profil saya hanya dapat dilihat oleh perusahaan sebagai bagian dari lamaran saya.", "visibilitas_recommend": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "visibilitas_aktif": "Bersedia", "visibilitas_deaktif": "Tidak Bersedia", "status_kerja_subtitle": "Bagaimana status Anda terkait pencarian kerja saat ini ?", "status_kerja_aktif": "Sedang aktif mencari kerja", "status_kerja_aktif_desc": "Kandidat akan menerima email tawaran kerja secara aktif, termasuk seluruh informasi lowongan terbaru sesuai dengan profil yang terdaftar.", "status_kerja_terbuka": "Terbuka untuk peluang kerja", "status_kerja_terbuka_desc": "Kandidat tidak secara aktif menerima email lowongan, namun tetap akan mendapatkan rekomendasi lowongan di platform digitalcv.", "status_kerja_tidak_aktif": "Tidak mencari kerja", "status_kerja_tidak_aktif_desc": "Kandidat tidak akan menerima informasi terkait lowongan kerja, baik melalui email maupun rekomendasi di platform digitalcv."}, "ubah_password": {"judul": "Ubah Kata Sandi", "reset": "Reset <PERSON>", "hint1": "<PERSON><PERSON>kkan kata sandi baru", "hint2": "Konfirmasi kata sandi baru", "hint0": "<PERSON><PERSON><PERSON><PERSON> kata sandi lama", "hint0_blank": "Kata sandi lama tidak boleh kosong!", "hint1_blank": "Kata sandi baru tidak boleh kosong!", "hint2_blank": "Konfirmasi kata sandi baru tidak boleh kosong!", "validator_equal": "Kata sandi baru tidak sesuai!"}, "ubah_email": {"judul": "Ubah Email"}, "ubah_no_telp": {"judul": "Ubah No Telepon", "subjudul": "No Telepon", "hint1": "Contoh: 82123456789", "hint1_blank": "No Telepon harus diisi!"}, "lupa_password": {"judul": "Lupa Kata Sandi", "subjudul": "Silahkan masukan Email Anda untuk mengganti kata sandi", "email": "Email", "email_blank": "Email tidak boleh kosong!"}, "profildcv": "Profil digitalcv", "dialog_out": {"judul": "Konfirma<PERSON>", "subjudul": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin logout?", "explore_judul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "explore_subjudul": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus login terle<PERSON><PERSON>u.", "explore_simpan_lowongan": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus login terle<PERSON>h da<PERSON>u.", "explore_notifikasi": "Sebel<PERSON> melihat not<PERSON>, <PERSON><PERSON> harus login terle<PERSON>h da<PERSON>u.", "explore_ubah_email": "Sebelum mengubah email, <PERSON><PERSON> harus login terlebih dahulu.", "explore_ubah_password": "Sebelum mengubah kata sandi, <PERSON><PERSON> harus login terle<PERSON>h dahulu.", "explore_ubah_no_telp": "Sebelum mengubah nomor telepon, <PERSON><PERSON> harus login terlebih dahulu.", "explore_profile_visibilitas": "Sebelum mengubah pengaturan visibilitas profil, <PERSON><PERSON> harus login terle<PERSON>h da<PERSON>u.", "explore_hapus_akun": "Sebel<PERSON> mengh<PERSON>us akun, <PERSON><PERSON> harus login terlebih dahulu / membuat akun terlebih dahulu."}, "konfirmasi": {"ada": "Ada", "tidak_ada": "Tidak Ada", "tidak_ada_pengalaman": "Tidak Ada Pengalaman Kerja", "freshgrad": "Fresh Graduate", "iya": "Ya", "iya2": "<PERSON><PERSON>", "tidak": "Tidak", "tidak_ada_organisasi": "Tidak Ada Pengalaman Organisasi"}, "konfirmasi2": {"iya": "Ya", "tidak": "Tidak"}, "apply": {"confirmation_title": "<PERSON>", "confirmation_cancel": "<PERSON><PERSON><PERSON><PERSON> Anda yakin membatalkan lamaran lowongan pekerjaan ini?", "confirmation_yes": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melamar lowongan pekerjaan ini?", "screening_cv": "Screening CV", "txt0": "<PERSON>a memahami sepenuhnya dan setuju:", "txt1": "Data yang akan diisi, foto dan atau dokumen lampiran lainnya yang diberikan adalah benar, akurat dan sesuai / asli sepenuhnya menjadi tanggung jawab saya.", "txt2": "Pengisian data yang akan saya lakukan adalah untuk", "txt3": "<PERSON><PERSON><PERSON>", "txt4": "berhak mengakses dan menggunakannya untuk proses rekrutmen.", "title1": "Lanjutkan proses pendaftaran Anda untuk lowongan posisi", "di": "di", "pendaftaran_di": "Pendaftaran di", "digitalcv": "menggunakan digitalcv", "pertanyaan_kandidat": "<PERSON><PERSON><PERSON>: ", "hint_jawaban": "<PERSON><PERSON>...", "validator_jawaban": "<PERSON>lom ini tidak boleh kosong!"}, "dcv": {"tentang_saya": "<PERSON><PERSON><PERSON>", "form_upload": "Form Upload CV", "info_upload2": "Klik untuk ganti file", "info_upload": "Klik untuk pilih file", "riwayat_hidup": "Riwayat Hidup", "keahlian": "<PERSON><PERSON><PERSON>", "kursus": "Kursus / Pelatihan", "organisasi": "Organisasi", "generate_analisa_info": "<PERSON>lisa AI ini mungkin tidak sepenuhnya akurat. Gunakan hanya sebagai referensi tambahan.", "judul_generate_analisa": "Form Analisa", "generate_ai_analisa": "Analisa CV Menggunakan digitalcv AI", "generate_hasil_analisa": "<PERSON><PERSON>", "judul": "Digital CV", "subjudul1": "Lengkapi data diri Anda untuk bisa mendapatkan kesempatan direkrut lebih tinggi!", "subjudul2": "Lengkapi data diri Anda untuk kesempatan direkrut lebih tinggi!", "info_pribadi": {"txt_download_cv": "Download CV", "txt_unggah_cv": "Unggah CV", "txt_linkedIn": "LinkedIn", "txt_tautan_portfolio": "<PERSON><PERSON>", "txt_instagram": "Instagram", "txt_twitter": "Twitter-X", "txt_facebook": "Facebook", "txt_tiktok": "Tiktok", "txt_posisi_kerjaan_blank": "Posisi Pekerjaan <PERSON>!", "posisi_kerjaan": "<PERSON><PERSON><PERSON>", "judul": "Informasi Pribadi", "judul2": "Pesonal Data", "txt_nama": "<PERSON><PERSON>", "txt_tempat": "Tempat Lahir", "txt_lahir": "<PERSON><PERSON>", "txt_jk": "<PERSON><PERSON>", "txt_lk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "txt_pr": "Perempuan", "txt_ktp": "Nomor KTP", "txt_ktp2": "No. KTP", "txt_alamat": "<PERSON><PERSON><PERSON> (Alamat Surat)", "txt_alamat2": "<PERSON><PERSON><PERSON>", "txt_handphone": "Nomor Handphone", "txt_handphone2": "No. Handphone", "txt_email": "<PERSON><PERSON><PERSON>", "txt_email2": "Email", "txt_pernikahan": "Status Pernikahan", "txt_marital1": "<PERSON><PERSON>", "txt_marital2": "<PERSON><PERSON><PERSON>", "txt_marital3": "<PERSON><PERSON>", "txt_marital4": "<PERSON><PERSON>", "txt_sim": "Kepemilikian Lisensi Berkendara", "txt_provinsi": "<PERSON><PERSON><PERSON>", "txt_kota": "Kota/Kabupaten", "txt_kecamatan": "Kecamatan", "txt_provinsi_choose": "<PERSON><PERSON><PERSON>", "txt_kota_choose": "Pilih Kota/Kabupaten", "txt_kecamatan_choose": "<PERSON><PERSON><PERSON>", "txt_rt": "RT", "txt_rw": "RW", "txt_alamat_tinggal": "<PERSON><PERSON><PERSON>", "txt_kode_pos": "Kode POS", "txt_marital_blank": "<PERSON><PERSON>", "txt_marital_blank2": "Status Pernikahan Harus Diisi!", "txt_nama_blank": "<PERSON><PERSON>!", "txt_tempat_blank": "Tempat Lahir Harus Diisi!", "txt_lahir_blank": "Tanggal Lahir Ha<PERSON>!", "txt_jk_blank": "<PERSON><PERSON>!", "txt_lk_blank": "<PERSON><PERSON>-laki <PERSON>!", "txt_pr_blank": "Perempuan Harus Diisi!", "txt_ktp_blank": "Nomor KTP Harus Diisi!", "txt_alamat_blank": "<PERSON><PERSON><PERSON>!", "txt_handphone_blank": "Nomor Handphone Harus Diisi!", "txt_email_blank": "<PERSON><PERSON><PERSON>!", "txt_pernikahan_blank": "Status Pernikahan Harus Diisi!", "txt_sim_blank": "Minimal pilih 1 jenis SIM", "txt_provinsi_blank": "<PERSON><PERSON><PERSON>!", "txt_provinsi_blank2": "<PERSON><PERSON><PERSON> harus dipilih terlebih dahulu!", "txt_kota_blank": "Kota/Kabupaten Harus Diisi!", "txt_kota_blank2": "Kota/Kabupaten harus dipilih terlebih dahulu!", "txt_kecamatan_blank": "Kecamatan Harus Diisi!", "txt_rt_blank": "RT Harus Diisi!", "txt_rw_blank": "RW Harus Diisi!", "txt_alamat_tinggal_blank": "<PERSON><PERSON><PERSON>!", "txt_kode_pos_blank": "Kode POS Harus Diisi!"}, "pendidikan": {"txt_tahun_lulus_validation": "Tahun <PERSON> T<PERSON>k boleh kurang dari <PERSON>hun <PERSON>!", "judul": "Riwayat Pendidikan", "txt_pendidikan_terakhir": "Jen<PERSON>g Pendidikan <PERSON>", "txt_pendidikan_terakhir_hint": "<PERSON><PERSON><PERSON>di<PERSON>kan <PERSON>", "txt_jenjang": "Jen<PERSON><PERSON>", "txt_jenjang_hint": "<PERSON><PERSON><PERSON>", "txt_nama": "<PERSON><PERSON> Pendi<PERSON>", "txt_jurusan": "<PERSON><PERSON><PERSON>", "txt_masuk": "<PERSON><PERSON>", "txt_lulus": "<PERSON><PERSON>", "txt_ket": "Keterangan", "txt_ket_hint": "<PERSON><PERSON><PERSON>", "txt_pendidikan_terakhir_blank": "Jen<PERSON>g Pendidikan <PERSON>", "txt_jenjang_blank": "Jenjang Pendidikan Harus Diisi!", "txt_nama_blank": "Nama Instansi Pendidikan Harus Diisi!", "txt_jurusan_blank": "Jurusan <PERSON>!", "txt_lulus_blank": "<PERSON><PERSON>!", "txt_masuk_blank": "<PERSON><PERSON>!", "txt_ket_blank": "Keterangan Harus Diisi!", "txt_diploma": "Melalui Diploma?", "txt_diploma_blank": "<PERSON><PERSON> pilih apakah anda melalui diploma atau tidak!", "ket1": "<PERSON><PERSON>", "ket2": "<PERSON><PERSON>", "ket3": "<PERSON><PERSON>", "ket4": "<PERSON><PERSON><PERSON><PERSON>", "ket5": "<PERSON><PERSON>", "jenjang1": "SD", "jenjang2": "SMP", "jenjang3": "SMA/Sederajat", "jenjang4": "Diploma", "jenjang5": "S1", "jenjang6": "S2", "jenjang7": "S3", "jenjang8": "SMA", "jenjang9": "SMK", "jenjang10": "D1", "jenjang11": "D2", "jenjang12": "D3", "jenjang13": "D4"}, "pelatihan": {"judul": "Pelatihan/Kursus", "txt_nama": "<PERSON><PERSON>/Kursus", "txt_instansi": "Tempat Pelatihan/Kursus", "txt_tahun": "<PERSON><PERSON>", "txt_konfirm_pelatihan": "<PERSON><PERSON><PERSON><PERSON> Anda memiliki pelatihan/kursus?", "txt_tgl_mulai": "<PERSON><PERSON>", "txt_tgl_selesai": "<PERSON><PERSON>", "txt_sertifikat": "Apakah Ada Sertifikat?", "txt_sertifikat_view": "Sertifikat:", "txt_tgl_mulai_error": "<PERSON>gal mulai tidak boleh lebih dari tanggal selesai!", "txt_sertifikat_blank": "Sertifikat Harus Diisi!", "txt_nama_blank": "<PERSON><PERSON>/<PERSON><PERSON><PERSON>!", "txt_instansi_blank": "Tempat Pelatihan/<PERSON><PERSON><PERSON>!", "txt_tahun_blank": "<PERSON><PERSON>!", "txt_tgl_mulai_blank": "<PERSON><PERSON>!", "txt_tgl_selesai_blank": "Tanggal Selesai Ha<PERSON> Diisi!", "ada": "Ada", "tidak_ada": "Tidak Ada", "tidak_ada_kursus": "Tidak ada kursus"}, "pengalaman_kerja": {"judul": "Pengalaman <PERSON>", "txt_confirm_pengalaman": "<PERSON><PERSON><PERSON><PERSON> Anda memiliki pengalaman kerja?", "txt_total": "Total Pengalaman Kerja", "txt_posisi_sama": "Pengalaman Kerja <PERSON> Posisi yang <PERSON>", "txt_nama": "<PERSON><PERSON>", "txt_jabatan": "Jabatan", "txt_status": "Status Kerja", "txt_gaji": "Gaji <PERSON> (Rp)", "txt_thn_masuk": "<PERSON><PERSON>", "txt_thn_keluar": "<PERSON><PERSON>", "txt_still_work": "<PERSON><PERSON><PERSON> bekerja hingga saat ini", "txt_alasan_berhenti": "<PERSON><PERSON><PERSON>", "txt_total_blank": "Total Pengalaman Kerja <PERSON> Diisi!", "txt_posisi_sama_blank": "Pengalaman Kerja di Posisi yang <PERSON>a Ha<PERSON> Diisi!", "txt_nama_blank": "<PERSON><PERSON>!", "txt_jabatan_blank": "Jabatan Harus Diisi!", "txt_status_blank": "Status Kerja <PERSON>!", "txt_gaji_blank": "<PERSON><PERSON><PERSON>!", "txt_thn_masuk_blank": "<PERSON><PERSON>!", "txt_thn_keluar_blank": "<PERSON><PERSON>!", "txt_alasan_berhenti_blank": "Alasan <PERSON>!", "status1": "<PERSON><PERSON><PERSON>", "status2": "Freelance (Pek<PERSON><PERSON>/Pekerja Tidak Terikat Kontrak)", "status3": "Full Time (<PERSON><PERSON><PERSON><PERSON> yang <PERSON> dan Memilik<PERSON> W<PERSON>)"}, "info_kerja": {"judul": "Informasi <PERSON>", "txt_dinas": "<PERSON><PERSON><PERSON><PERSON> Anda bersedia melakukan perjalanan dinas?", "txt_dinas2": "Bersedia Melakukan <PERSON>?", "txt_gaji": "Sebutkan Gaji yang <PERSON>a Inginkan", "hint_gaji": "<PERSON><PERSON><PERSON> yang <PERSON>", "txt_lokasi": "Lok<PERSON> ya<PERSON>", "txt_gaji_blank": "<PERSON><PERSON>i yang <PERSON>!", "txt_lokasi_blank": "Lokasi Kerja yang <PERSON> Ha<PERSON> Diisi!"}, "pengalaman_org": {"judul": "Pengalaman Organisasi", "txt_konfirm_org": "<PERSON><PERSON><PERSON><PERSON> Anda memiliki pengalaman organisasi?", "txt_nama": "<PERSON><PERSON>", "txt_tempat": "Tempat Organisasi", "txt_jabatan": "<PERSON><PERSON><PERSON>", "txt_tahun": "<PERSON><PERSON>", "txt_nama_blank": "Nama Organisasi Ha<PERSON>!", "txt_tempat_blank": "Tempat Organisasi Harus Diisi!", "txt_jabatan_blank": "Jabatan Organisasi Harus Diisi!", "txt_tahun_blank": "Tahun <PERSON>!", "tidak_ada_organisasi": "Tidak ada pengalaman"}, "minat": {"judul": "<PERSON><PERSON> dan <PERSON>", "txt_konfirm_bahasa": "<PERSON><PERSON><PERSON><PERSON>?", "txt_bahasa_dikuasai": "Bahasa <PERSON><PERSON> yang <PERSON>", "txt_bahasa_dikuasai_blank": "Bahasa Asing <PERSON>!", "txt_kelebihan": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "txt_kelebihan2": "<PERSON><PERSON><PERSON><PERSON>", "txt_kekurangan": "Kekurangan yang <PERSON>", "txt_kekurangan2": "Kekurangan", "txt_baca": "Membaca", "txt_menulis": "<PERSON><PERSON><PERSON>", "txt_mendengar": "<PERSON><PERSON><PERSON>", "txt_bicara": "Berbicara", "txt_baca_blank": "Penilaian membaca bahasa asing tidak boleh kosong!", "txt_menulis_blank": "<PERSON><PERSON><PERSON> menulis bahasa asing tidak boleh kosong!", "txt_mendengar_blank": "<PERSON><PERSON><PERSON> mendengar bahasa asing tidak boleh kosong!", "txt_bicara_blank": "<PERSON><PERSON><PERSON> berb<PERSON>ra bahasa asing tidak boleh kosong!", "txt_kelebihan_blank": "Kelebihan Harus Diisi!", "txt_kekurangan_blank": "Kekurangan Harus Diisi!", "komputerisasi": {"judul": "Pengetahuan di Bidang Komputerisasi", "judul2": "<PERSON><PERSON><PERSON><PERSON>", "opt1": "<PERSON><PERSON><PERSON><PERSON> dasar dan pengoperasian Ms. Office (Ms. <PERSON>, Ms. Excel, Ms. PPT, dll)", "opt2": "<PERSON><PERSON><PERSON> dan <PERSON> pengoperasian Ms. Office (Ms. <PERSON>, Ms. Excel, Ms. PPT, dll)", "opt3": "Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya", "opt4": "<PERSON><PERSON><PERSON> dan <PERSON>", "opt5": "Spesial<PERSON>", "opt6": "Tidak Diperlukan", "validation": "<PERSON><PERSON><PERSON><PERSON>!"}, "txt_komputerisasi_blank": "<PERSON><PERSON><PERSON><PERSON>!", "jml_org": {"judul": "<PERSON><PERSON><PERSON> yang <PERSON>", "judul2": "Jml. <PERSON><PERSON> yang <PERSON> Pimpin", "opt1": "Tidak Pernah/Tidak Perlu/Tidak Ada", "opt2": "1-3 orang", "opt3": "4-10 orang", "opt4": "11-50 orang", "opt5": "Lebih dari 50 orang", "validation": "Jumlah Orang Harus Diisi!"}, "txt_jml_orang_blank": "Jml. <PERSON><PERSON>!", "txt_penilaian": "Pen<PERSON>ian <PERSON> And<PERSON>lam Prestasi / Berbicara di Depan Umum", "txt_penilaian2": "Pen<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "txt_penilaian_blank": "Penila<PERSON> Ha<PERSON>!", "ruang_lingkup": {"judul": "<PERSON><PERSON> yang <PERSON>", "opt1": "Ma<PERSON>u dalam perencanaan", "opt2": "Aktivitas yang berhubungan dengan orang", "opt3": "<PERSON><PERSON><PERSON> instr<PERSON><PERSON>", "opt4": "<PERSON><PERSON><PERSON><PERSON>", "opt5": "Aktivitas flexible", "opt6": "Aktivitas didalam ruangan", "opt7": "Aktivitas dengan target", "opt8": "Aktivitas yang rutin", "opt9": "Aktivitas dengan jadwal", "opt10": "Ma<PERSON><PERSON> da<PERSON> problem solving", "opt11": "<PERSON><PERSON><PERSON> dalam pengaturan keuangan", "opt12": "Membutuhkan inisiatif dan kreatifitas", "opt13": "Aktivitas yang dinamis", "opt14": "Aktivitas administrasi", "opt15": "Aktivitas diluar ruangan", "opt16": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang terulang-ulang/monoton/clarical", "opt17": "Aktivitas yang berhubungan dengan mesin", "opt18": "<PERSON><PERSON> instruksi", "validation": "<PERSON><PERSON>!"}, "txt_ruang_lingkup_blank": "Ruang Lingkup Ha<PERSON> Diisi!"}}, "detail_lowongan": {"perhatian": "PERHATIAN", "perhatian_desc": " tidak memungut biaya apapun selama proses pendaftaran dan seleksi karir berl<PERSON>g", "desc_pekerjaan": "<PERSON><PERSON><PERSON><PERSON>", "kualifikasi": "<PERSON><PERSON><PERSON>", "kriteria": "<PERSON><PERSON><PERSON>", "tentang_perusahaan": "<PERSON><PERSON><PERSON>", "alamat": "<PERSON><PERSON><PERSON>", "diposting": "Diposting", "traveling": "<PERSON><PERSON><PERSON><PERSON>", "pendidikan": "Minimal <PERSON>", "jurusan": "<PERSON><PERSON><PERSON>", "usia": "Rentang <PERSON>", "var_usia": "tahun", "sim": "SIM", "jk": "<PERSON><PERSON>", "p_kerja": "Pengalaman <PERSON>", "p_kerja_bidang": "Pengalaman Kerja <PERSON> yang <PERSON>", "status": "Status", "tahun": "tahun", "bahasa": "Bahasa yang <PERSON>", "komputerisasi": "<PERSON><PERSON><PERSON><PERSON>", "memimpin": "Kemamp<PERSON>", "presentasi": "Kemampuan Berbicara di Depan Umum", "rlp": "<PERSON><PERSON>", "traveling_ya": "Ya", "traveling_tidak": "Tidak", "traveling_kebutuhan": "<PERSON><PERSON><PERSON>"}, "perusahaan": {"alamat": "<PERSON><PERSON><PERSON>", "industri": "Industry", "link": "<PERSON>", "tentang": "<PERSON><PERSON><PERSON>", "lowongan_tersedia": "<PERSON><PERSON><PERSON> yang <PERSON> di ", "tanggal_daftar": "<PERSON>gal <PERSON>ftaran", "lowongan_di": "<PERSON><PERSON><PERSON> di"}, "timeline": {"judul": "Timeline", "tanggal_daftar": "<PERSON>gal <PERSON>ftaran", "tahap": "<PERSON><PERSON><PERSON>", "tahap1": "<PERSON><PERSON>", "tahap2": "Proses Screening CV", "tahap3": "Test Psikotest", "tahap4": "Wawancara <PERSON>gan <PERSON>", "tahap5": "Medical Checkup", "tahap6": "Kandidat <PERSON>", "tahap7": "Kandidat Tidak Diterima", "tahap8": "Ditolak Digital CV"}, "tombol": {"upload": "Upload", "filter_beranda": "<PERSON><PERSON><PERSON><PERSON>", "lihat_detail": "<PERSON><PERSON>", "lokasi": "<PERSON><PERSON>", "beranda": "Be<PERSON><PERSON>", "tersimpan": "<PERSON><PERSON><PERSON><PERSON>", "riwayat": "Riwayat", "profil": "Profil", "lamar": "<PERSON>", "sudah_lamar": "<PERSON><PERSON>", "lowongan_lainnya": "<PERSON><PERSON>", "timeline": "Timeline", "filter": "Filter", "ubah": "UBAH", "simpan": "Simpan", "hapus": "Hapus", "hapus_akun": "Hapus Akun", "peringatan": "Peringatan", "peringatan_hapus_akun": "Tindakan ini akan menghapus akun Anda secara permanen dan tidak dapat dibatalkan.", "keluar": "<PERSON><PERSON><PERSON>", "tambah_data": "Tambah Data", "selanjutnya": "Selanjutnya", "kembali": "Kembali", "lamarUp": "LAMAR", "kirim": "<PERSON><PERSON>", "apply": "Apply", "ok": "OK", "crop": "Crop", "batal": "<PERSON><PERSON>", "cari_foto": "<PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON>"}, "jenis_pekerjaan": {"txt_full_time": "Full-Time", "txt_part_time": "Part-Time", "txt_internship": "<PERSON><PERSON><PERSON>", "txt_contract": "Kontrak"}, "spesialisasi": {"txt_akuntansi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "txt_administrasi": "Administrasi & Dukungan Perkantoran", "txt_edukasi": "<PERSON><PERSON><PERSON>", "txt_energi": "Energi & Sumber Daya", "txt_seni": "Seni & Media", "txt_keuangan": "<PERSON><PERSON><PERSON>", "txt_konstruksi": "Konstruksi", "txt_pemasaran": "<PERSON><PERSON><PERSON><PERSON>", "txt_penjualan": "Penjualan", "txt_teknologi_informasi": "Teknologi Informasi", "txt_manufaktur": "Manufaktur", "txt_hukum": "Hukum & Konsultasi", "txt_periklanan": "<PERSON><PERSON><PERSON><PERSON>", "txt_pertanian": "Pertanian", "txt_perhotelan": "Pariwisata & Perhotelan", "txt_kesehatan": "<PERSON><PERSON><PERSON><PERSON>", "txt_ritel": "Ritel & Perdagangan", "txt_telekomunikasi": "Telekomunikasi", "txt_transporatsi": "Transportasi & Logistik", "txt_keamanan": "<PERSON><PERSON><PERSON>", "txt_pemerintaan": "Pemerintahan & LSM"}, "urutan": {"txt_urutan": "Urutan", "txt_relevan": "<PERSON><PERSON>", "txt_baru": "<PERSON><PERSON>", "txt_fresh_graduate": "<PERSON><PERSON> baru"}, "waktu_posting": {"txt_kapan_saja": "<PERSON><PERSON>", "txt_hari_ini": "<PERSON>", "txt_3_hari": "3 <PERSON>", "txt_7_hari": "7 <PERSON>", "txt_14_hari": "14 <PERSON>", "txt_30_hari": "30 <PERSON>"}, "tnc": {"judul": "<PERSON><PERSON><PERSON> dan <PERSON>", "txt_berlaku": "Diber<PERSON><PERSON><PERSON> sejak", "txt_unggah": "<PERSON><PERSON><PERSON><PERSON> sejak", "txt_no_dok": "No. Dokumen", "txt_tgl": "12 Januari 2021", "txt_pengenalan": "Pengenalan", "tncPengenalan": "Platform digitalcv merupakan program yang merevolusi proses rekrutmen konvensional menjadi lebih sistematis. Sistem digitalcv memungkinkan perusahaan penyedia lowongan kerja serta kandidatnya dapat melakukan proses rekrutmen dimana saja, kapan saja, dan data kandidat akan diterima secara real time dengan keamanan data yang dilindungi oleh teknologi tinggi.", "txt_info_umum": "Informasi Umum", "tncInfoUmum1": "Data pribadi yang diminta oleh digitalcv, <PERSON><PERSON> wajib menyetujui pemrosesan informasi pribadi Anda oleh digitalcv. Jika Anda tidak setuju untuk memberikan informasi pribadi ini dan/atau tidak setuju untuk kami proses dengan cara yang ditetapkan dalam Pemberitahuan Privasi ini, maka digitalcv tidak akan melanjutkan proses rekrutmen <PERSON>a.", "tncInfoUmum2": "Beberapa klien digitalcv mungkin juga meminta Anda menjawab daftar pertanyaan yang mereka sudah sediakan sebagai bagian dari lamaran Anda. <PERSON><PERSON> menjawab pertanyaan lamaran, <PERSON><PERSON> menye<PERSON><PERSON> bahwa digitalcv dapat memberikan jawaban Anda kepada klien digitalcv dan dapat menggunakan jawaban Anda untuk melengkapi riwayat hidup pada digitalcv Anda.", "tncInfoUmum3": "Kebijakan Privasi ini menjelaskan informasi/data pribadi yang kami dapat dari kandidat atau yang kandidat berikan melalui situs web resmi kami, serta pihak ketiga yang secara resmi bekerja sama dengan digitalcv dan bagaimana informasi/data tersebut akan digunakan atau diungkapkan oleh kami.", "txt_ket_layanan": "<PERSON><PERSON><PERSON><PERSON>", "tncKetentuanLayanan1": "Ketentuan Layanan ini mengatur penggunaan digitalcv, kecuali jika kami secara tegas menyatakan bahwa ketentuan terpisah (dan bukan ini) yang berlak<PERSON>, dan memberikan informasi tentang Sistem digitalcv.", "tncKetentuanLayanan2": "Sistem digitalcv tidak membebankan biaya kepada Anda atas pengunaan digitalcv atau produk dan layanan lainnya yang tercakup dalam Ketentuan ini, kecuali kami menyatakan sebaliknya. Dengan menggunakan digitalcv, <PERSON><PERSON> setuju bahwa kami dapat menampilkan iklan yang menurut kami mungkin relevan dengan Anda. Kami tidak menjual data pribadi Anda kepada pengiklan, dan kami tidak membagikan informasi Anda kepada pengiklan.", "tncKetentuanLayanan3": "Syarat dan ketentuan <PERSON>anan dan <PERSON><PERSON> Privasi ini dibuat dalam dua Bahasa yaitu Bahasa Indonesia dan Bahasa Inggris. Apabila terjadi sengketa atau klaim antara pengguna dengan digitalcv. <PERSON>un terjadi perbedaan penafsiran antara Bahasa Indonesia dan <PERSON>hasa Inggris, maka yang akan digunakan adalah Bahasa Indonesia.", "txt_data_pribadi": "Data Pribadi", "tncDataPribadi1": "Anda akan memberikan informasi pribadi ketika mendaftar dan/atau menggunakan layanan pada situs situs web digitalcv. Berikut ini adalah contoh informasi pribadi yang diterima digitalcv langsung dari Anda seperti swafoto identitas, minat dan preferensi pribadi, nomor telepon atau nomor ponsel, usia, pengalaman kerja, informasi lain yang terkait dengan resume untuk lamaran pekerja<PERSON>, nama akun sosial media, kontak darurat, nomor KTP (kartu tanda penduduk), kartu keluarga, jenis dan nomor SIM (A/B1/B2/C/D/SIO), akta k<PERSON>, pak<PERSON>ing, kualifikasi akademik, dokumen identitas yang diberikan oleh sekolah/perguruan tinggi/universitas/lembaga dan SKCK.", "tncDataPribadi2": "Beberapa Data Pribadi yang kami terima mungkin bersifat sensitif. Hal ini dapat termasuk Data Pribadi yang berkaitan dengan latar belakang diri Anda, informasi identitas nasional, keyakinan agama, data kesehatan, disabilitas, status perkawinan dan beberapa data lainnya berkaitan dengan Anda. Sistem digitalcv mengumpulkan Data Pribadi sensitif hanya dengan persetujuan Anda dan/atau sesuai dengan hukum yang berlaku. Anda akan menyatakan setuju untuk memberikan Data Pribadi sensitif tersebut kepada digitalcv, jika Anda memberikan dokumentasi atau informasi apapun kepada digitalcv sesuai dengan tujuan melengkapi Data Pribadi sensitif tersebut, kecuali pengolahan data yang berlaku tanpa persetujuan Anda diizinkan oleh Undang – Undang Privasi.", "tncDataPribadi3": "Adakalanya klien digitalcv meminta data pribadi rekan dan/atau kerabat, (seperti pasangan, anggota keluarga, atau rekan Anda) kepada digitalcv. Dengan Anda memberikan Data Pribadi mereka kepada digitalcv, <PERSON>a menyatakan dan menjamin bahwa Anda telah memperoleh persetujuan mereka agar Data Pribadi mereka disimpan untuk digitalcv dan sebagaimana diatur dalam Pemberitahuan Privasi ini.", "tncDataPribadi4": "Saat Anda mengirimkan lamaran Anda atau informasi lainnya kepada digitalcv, Anda mengizinkan digitalcv untuk menyediakan informasi lamaran tersebut kepada klien digitalcv. Anda selanjutnya menyetujui kinerja pemrosesan otomatis digitalcv terkait lamaran <PERSON>, karena pem<PERSON> tersebut adalah bagian penting dalam <PERSON> ini. Saat Anda meminta digitalcv untuk mengirimkan lamaran atau menyimpan lamaran tersebut, <PERSON><PERSON> memahami bahwa hal tersebut tidak dijamin. <PERSON><PERSON> itu, Anda memahami dan setuju bahwa lamaran Anda dan respon yang dikirimkan kepada Anda oleh klien digitalcv melalui digitalcv akan diproses dan dianalisis oleh digitalcv sesuai Kebijakan Privasi digitalcv.", "tncDataPribadi5": "Sistem digitalcv akan menyimpan informasi Anda selama diperlukan dan untuk tujuan lain yang ditetapkan dalam Kebijakan Privasi ini. Kami juga menyimpan informasi apabila diperlukan untuk mematuhi kewajiban kontrak dan hukum formal yang berlaku, apabila kami memiliki kepentingan bisnis yang sah untuk melakukannya dan untuk pelaksanaan atau pembelaan secara hukum.", "txt_kelola_data": "Cara digitalcv Mengelola Data Anda", "tncKelolaData1": "Sistem digitalcv menyediakan dan menyajikan informasi yang diper<PERSON>, berhubungan dengan aktivitas rekrutmen sebagaimana dimaksud serta sesuai dengan adanya layanan yang disediakan oleh digitalcv baik untuk kandidat yang mencari lowongan pekerjaan dan khususnya untuk klien digitalcv sebagai penyedia lapangan kerja atau pencari kandidat.", "tncKelolaData2": "Sistem digitalcv menyimpan data selama yang diperlukan untuk proses rekrutmen, termasuk yang sudah dijelaskan di atas. Informasi terkait dengan akun Anda akan disimpan, kecuali kami tidak lagi memerlukan data Anda untuk proses rekrutmen. Sistem digitalcv juga menggunakan informasi untuk mengembangkan dan meningkatkan performa pada sistem digitalcv. Anda dapat mempelajari lebih lanjut tentang cara menonaktifkan akun Anda.", "tncKelolaData3": "Ketika Anda melamar lowongan peker<PERSON>an menggunakan digitalcv, maka digitalcv akan berupaya mengirimkan lamaran Anda melalui format digitalcv yang kami terima atau sesuai dengan permintaan dan akan diberikan kepada klien digitalcv.", "tncKelolaData4": "Sistem digitalcv memiliki kerja sama dengan banyak perusahaan yang mencari kandidat sesuai dengan kriteria yang dibutuhkan. Aktivitas seleksi yang terjadi secara digital melalui digitalcv terjadi/dilakukan secara langsung oleh sistem sesuai dengan lowongan pekerjaan yang kandidat pilih secara mandiri pada daftar pekerjaan yang disediakan oleh sistem atau yang ditampilkan pada halaman lowongan pekerjaan di situs web digitalcv.", "tncKelolaData5": "<PERSON><PERSON> keadaan yang mungkin di luar dugaan yang kami kira, digitalcv tidak akan bertanggung jawab kepada Anda untuk kerugian yang timbul secara langsung ataupun tidak langsung. Dikarenakan pihak digitalcv memiliki batas dalam hal bertanggung jawab seperti yang sudah dijelaskan dalam isi Kebijakan Privasi.", "tncKelolaData6": "Dengan menggunakan digitalcv dan menjawab beberapa pertanyaan dari kami, <PERSON><PERSON> menga<PERSON>i bahwa klien digitalcv mungkin telah menginstruksikan digitalcv untuk menjadwalkan wawancara dengan kandidat yang memenuhi kriteria. Anda setuju bahwa digitalcv tidak memiliki kewenangan dalam pengiriman atau penyimpanan undangan wawancara ini, bahwa pengiriman atau penyimpanan tidak dijamin, dan bahwa ketersediaan atau kriteria yang diberikan oleh klien digitalcv kepada digitalcv mungkin tidak akurat. Sistem digitalcv dapat meminta agar kandidat mengonfirmasi data yang diisi pada posisi tersebut sebelum menjadwalkan wawancara. Pada akhirnya hal tersebut merupakan tanggung jawab Anda dan klien digitalcv untuk mengonfirmasi apakah ada wawancara yang dijadwalkan atau untuk menjadwal ulang atau membatalkan wawancara jika perlu. Keputusan tentang siapa yang akan diwawancarai diputuskan semata-mata oleh perusahaan dan dapat memilih untuk mewawancarai kandidat kapapun.", "tncKelolaData7": "Sistem digitalcv juga merekomendasikan Anda sebagai kandidat kepada beberapa klien digitalcv. Seperti, digitalcv dapat merekomendasikan lamaran Anda untuk beberapa perusahaan yang merupakan klien digitalcv yang sesuai dengan kriteria Anda. Untuk mengh<PERSON>lkan kesesuaian, digitalcv menggunakan data yang kami terima dari Anda atau klien digitalcv. Anda setuju dan sepakat bahwa digitalcv dapat menggunakan informasi ini untuk menyajikan kemungkinan kesesuaian lamaran Anda dengan klien digitalcv.", "tncKelolaData8": "<PERSON><PERSON><PERSON><PERSON> ini merupakan keselu<PERSON>han perjanjian antara Anda dan digitalcv, dengan mengikuti aturan yang berlaku dan dapat dilakukan untuk meniadakan serta menggantikan semua pernya<PERSON>, p<PERSON><PERSON>, dan per<PERSON><PERSON>an sebelumnya atau pada saat yang bersa<PERSON>, baik tertulis maupun lisan oleh digitalcv.", "tncKelolaData9": "Perjanjian ini tidak dapat dimodifikasi atau diganti, kecuali dengan dokumen yang ditandatangani oleh perwakilan yang sah dari masing-masing pihak digitalcv. Komunikasi tertulis atau telepon antara Anda dan karyawan digitalcv bukanlah modifikasi atau amandemen perjanjian ini.", "tncKelolaData10": "Anda tidak diperkenankan untuk menyerahkan atau melimpahkan hak dan/atau kewajiban yang disebutkan dalam Kebijakan Privasi tanpa adanya persetujuan secara tertulis dari pihak digitalcv dan semua upaya seperti itu akan dianggap tidak sah. Sistem digitalcv dapat dengan bebas menyerahkan atau melimpahkan hak dan/atau kewajibannya yang disebutkan di sini tanpa memberi tahu Anda. Sistem digitalcv dan Anda tidak berada dalam kemitraan hukum atau hubungan kerja.", "tncKelolaData11": "Sistem digitalcv tidak akan menanggung proses apapun yang terjadi apabila klien digitalcv memutuskan untuk tidak memproses/memilih kandidat.", "txt_persetujuan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tncPersetujuan1": "Dengan mengakses situs web digitalcv dan menggunakan layanan kami, <PERSON><PERSON> menyetu<PERSON><PERSON>bijakan Privasi digitalcv untuk dapat melanjutkan proses pendaftaran akun digitalcv. Anda juga bersedia untuk memberikan informasi/data pribadi tentang diri Anda untuk membuat akun digitalcv.", "tncPersetujuan2": "Dengan Anda mengakui dan menyetu<PERSON><PERSON> Privasi ini, maka klien digitalcv dapat meminta lamaran <PERSON>, jawaban per<PERSON> sa<PERSON>, jawaban tes dan informasi lain yang Anda berikan kepada digitalcv dan Anda setuju bahwa dengan melakukan tindakan tersebut, digitalcv menyusun lamaran ini atas nama Anda.", "tncPersetujuan3": "<PERSON>a setuju bahwa klien digitalcv dapat memungkinkan penyedia layanan tersebut untuk mengakses dan melihat digitalcv kandidat.", "tncPersetujuan4": "<PERSON><PERSON><PERSON><PERSON> terjadi kes<PERSON>han dalam pengisian baik yang berhubungan dengan pengisian atau pengetikan oleh kandidat, hal itu bukan merupakan tanggung jawab digitalcv.", "tncPersetujuan5": "<PERSON><PERSON> tidak menjamin bahwa lamaran tersebut akan diakses, dibaca atau ditin<PERSON>lanjuti oleh perusa<PERSON>an. Sistem digitalcv juga tidak menjamin bahwa perusahaan akan memberi tahu, menga<PERSON><PERSON>, membaca atau menanggapi lamaran kandidat.", "tncPersetujuan6": "Sistem digitalcv juga tidak akan menanggung apabila klien digitalcv memutuskan untuk tidak memproses/memilih kandidat.", "tncPersetujuan7": "Jika Anda di bawah umur dan mendaftarkan diri untuk pembuatan akun serta menggunakan layanan kami, maka Anda dan/atau orang tua atau wali Anda menyatakan bahwa telah memahami dan menyetujui bahwa seluruh ketentuan Kebijakan Privasi ini.", "txt_info_teknik": "Informasi Teknik Pihak Ketiga", "tncInfoTeknik1": "Sistem digitalcv melakukan kerjasama dengan pihak ketiga yaitu AWS (Amazon Web Service) untuk membantu kami dalam melakukan penyimpanan data server, database dan cloud. Sistem digitalcv juga mempercayai Amazon Web Service (AWS) untuk mengamankan data server, database dan cloud. Amazon Web Service (AWS) memberikan jaminan untuk keamanan data yang kami gunakan dalam menyimpan data pada server Amazon Web Service (AWS).", "tncInfoTeknik2": "Layanan pada server AWS akan menerima informasi serta dokumen yang Anda berikan untuk disimpan pada jangka waktu tertentu oleh AWS (selama digitalcv bekerja sama dengan AWS).", "tncInfoTeknik3": "Namun tidak menutup kemungkinan digitalcv tidak lagi bekerja sama dengan AWS dan Anda dapat melihat perubahan itu pada notifikasi Kebijakan Privasi digitalcv pada situs web kami.", "txt_lama_data": "<PERSON>", "tncLamaData": "Sistem digitalcv memulai menyimpan informasi/data pribadi Anda pada saat Anda menggunakan dan/atau mendaftarkan diri dalam digitalcv. Informasi/data pribadi Anda akan disimpan selama Anda masih menggunakan digitalcv sampai dengan batas waktu sesuai dengan yang diatur dalam peraturan perundang-undangan.", "txt_keamanan_data": "Keamanan Data", "tncKeamananDat1": "Tujuan keamanan data yang dilakukan digitalcv yaitu untuk mendeteksi, mencegah atau menangani penipuan atau masalah teknis.", "tncKeamananDat2": "Langkah digitalcv untuk memastikan bahwa informasi Anda ditangani dengan aman dan sesuai dengan kebijakan ini. Namun informasi melalui internet tidak sepenuhnya aman. Setiap transmisi informasi pribadi adalah risiko Anda dan kami tidak dapat menjamin keamanan informasi pribadi Anda dikirim ke situs web resmi kami. Namun kami telah melakukan yang terbaik untuk mengamankan informasi pribadi Anda dari kerugian tak terduga dari akses yang tidak sah, pengguna<PERSON>, perubahan dan keterbukaan. Semua informasi yang Anda berikan kepada kami disimpan pada server kami yang aman.", "tncKeamananDat3": "Situs web dan integrasi pihak ketiga yang menggunakan situs web digitalcv, ketika Anda memilih menggunakan situs situs web digitalcv atau layanan pihak ketiga lainnya yang menggunakan atau terintegrasi dengan produk kami, mereka dapat menerima informasi tentang apa yang Anda posting atau bagikan. Informasi yang digunakan oleh layanan pihak ketiga tersebut tunduk pada ketentuan dan kebijakannya sendiri.", "tncKeamananDat4": "<PERSON><PERSON> bekerja sama dengan pihak ketiga yaitu klien digitalcv untuk proses rekrutmen dan menyediakan layanan gratis kepada kandidat-kandidat di seluruh dunia. Sistem digitalcv tidak menjual informasi Anda kepada orang lain dan digitalcv tidak akan pernah melakukannya. Sistem digitalcv juga menerapkan pembatasan ketat mengenai bagaimana klien kami dapat menggunakan dan mengungkapkan data yang kami berikan.", "tncKeamananDat5": "<PERSON><PERSON> keadaan yang mungkin di luar dugaan yang kami kira, pihak digitalcv tidak akan bertanggung jawab kepada Anda untuk kerugian yang timbul secara langsung ataupun tidak langsung. Dikarenakan pihak digitalcv memiliki batas dalam hal bertanggung jawab seperti yang sudah dijelaskan dalam isi Kebijakan Privasi.", "txt_web_lain": "<PERSON><PERSON><PERSON>", "tncLayananWebLain": "Sistem digitalcv dapat berisi tautan ke situs-situs lain. <PERSON><PERSON>, set<PERSON>h anda masuk pada situs tersebut lalu meninggalkan situs kami, anda harus mengetahui bahwa kami tidak memiliki kendali atas situs-situs lain yang Anda kunjungi. Seluruh pertanyaan atupun keluhan terkait dengan situs website lain bisa langsung ditanyakan pada website tersebut.", "txt_perubahan": "<PERSON><PERSON><PERSON>", "tncPerubahan": "Sistem digitalcv dapat mengubah Ketentuan Layanan dan <PERSON> Privasi ini kapan saja. Perubahan tersebut akan berlaku setelah ditampilkan pada website digitalcv. Tetapi menjadi tanggung jawab pengguna digitalcv untuk meninjau Ketentuan Layanan secara teratur dan setiap perubahan akan terikat pada pengguna digitalcv.", "txt_force_majeur": "Force Majeure (Peristiwa tidak dapat dihindari)", "tncForceMajeur1": "Sistem digitalcv dapat dibebaskan dari kewajiban apapun untuk melakukan isi Ketentuan <PERSON>n dan <PERSON>, baik sebagian dan/atau keselurahannya disebabkan karena adanya Force Majeure (peristiwa tidak dapat dihindari) beberapa unsur yang dapat menyebabkan kondisi tersebut, diantaranya :", "tncForceMajeur2": "<PERSON><PERSON><PERSON> yang tidak terduga;", "tncForceMajeur3": "Terdapat halangan yang menyebabkan suatu kewajiban tidak mungkin dilaksanakan;", "tncForceMajeur4": "Ketidakmampuan tersebut bukan disebabkan karena kesalahan digitalcv;", "tncForceMajeur5": "Ketidaksanggupan tersebut tidak bisa dibebankan risiko kepada digitalcv.", "tncForceMajeur6": "Beberapa contoh dari kejadian tersebut seperti bencana alam, perang, terorisme, gempa bumi, angin topan, tindakan pemeri<PERSON>h, ledakan, keb<PERSON><PERSON>, wabah penyakit, epidemi, perampokan, dan perampasan. Segala kerugian yang timbul sebagai akibat terjadinya Force Majeure (peristiwa tidak dapat dihindari) menjadi tanggung jawab masing – masing pengguna sistem digitalcv.", "txt_hukum": "<PERSON><PERSON><PERSON> yang <PERSON>", "tncHukuman": "Sistem digitalcv akan tunduk dan diatur dan/atau ditafsirkan sesuai dengan Hukum yang Berlaku di Indonesia dan memastikan standar perlindungan sesuai dengan ketentuan Undang – Undang Republik Indonesia. <PERSON><PERSON> dem<PERSON>, tidak adanya jaminan atau keamanan absolut tidak dapat dihindarkan.", "txt_kontak": "Kontak Kami", "tncKontak": "Jika Anda mengalami kesulitan atau memiliki pertanyaan untuk digitalcv Anda dapat mempelajari lebih lanjut melalui Q&A yang tercantum pada situs web kami. Apabila Anda membutuhkan informasi lebih lanjut maupun terdapat keluhan, Anda dapat menghubungi kami melalui kontak yang tersedia pada situs web digitalcv."}, "image": {"upload": "<PERSON><PERSON><PERSON>"}, "error": {"error": "Error", "crop_image": "Failed to crop image:", "detail_lowongan_empty": "Data lowongan tidak ditemukan", "location_empty": "Tidak dapat membuka lokasi. Pastikan aplikasi Maps atau browser tersedia.", "name_empty": "<PERSON>a kamu tidak ada", "jadwal_empty": "<PERSON><PERSON><PERSON> belum ditentukan"}, "share": {"cek": "Cek lowongan ini di Digital CV!", "terima_kasih": "Thank you for sharing!"}, "date_picker": {"pilih_tahun": "<PERSON><PERSON><PERSON>", "pilih_tahun_masuk": "<PERSON><PERSON><PERSON>", "pilih_tahun_lulus": "<PERSON><PERSON><PERSON>"}, "notifikasi": {"notifikasi": "Notif<PERSON><PERSON>", "notifikasi_empty": "Tidak ada notifikasi baru", "notifikasi_desc": "Anda tidak memiliki pemberitahuan saat ini", "ada": "Ada", "ada_desc": "notifikasi baru yang belum dibaca", "txt_baru": "<PERSON><PERSON>"}, "lokasi": {"pilih_lokasi": "<PERSON><PERSON><PERSON>", "cari_lokasi": "<PERSON><PERSON>...", "pilih_provinsi": "<PERSON><PERSON><PERSON>", "cari_provinsi": "<PERSON><PERSON>...", "pilih_kota": "Pilih Kota/Kab", "cari_kota": "Cari Kota/Kab...", "pilih_kecamatan": "<PERSON><PERSON><PERSON>", "cari_kecamatan": "<PERSON><PERSON>..."}, "bahasa": {"pilih_bahasa": "<PERSON><PERSON><PERSON>", "cari_bahasa": "Cari Bahasa...", "list": {"af": "Afrikaans", "ak": "<PERSON><PERSON>", "sq": "Albania", "am": "<PERSON><PERSON><PERSON>", "ar": "Arab", "hy": "Armenia", "as": "Assam", "ay": "Aymara", "az": "Azerbaijani", "bm": "Bambara", "eu": "Basque", "nl": "<PERSON><PERSON>", "be": "Belarusia", "bn": "Bengali", "bho": "B<PERSON>jpuri", "bs": "Bosnia", "bg": "Bulgaria", "my": "Burma", "ceb": "Cebuano", "cs": "Cheska", "zh": "China", "da": "Dansk", "dv": "Divehi", "doi": "<PERSON><PERSON>", "eo": "Esperanto", "et": "<PERSON><PERSON><PERSON>", "ee": "<PERSON><PERSON>", "fy": "Frisia Barat", "gd": "Gaelik Skotlandia", "gl": "Galisia", "lg": "Ganda", "ka": "Georgia", "gom": "Goan <PERSON>", "gn": "Guarani", "gu": "Gujarat", "ha": "Hausa", "haw": "Hawaii", "hi": "Hindi", "hmn": "Hmong", "hu": "Hungaria", "he": "<PERSON><PERSON><PERSON>", "ig": "Igbo", "ilo": "Iloko", "id": "Indonesia", "en": "<PERSON><PERSON><PERSON>", "ga": "Irlandia", "is": "Islandia", "it": "Italia", "ja": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON>", "kn": "Kannada", "ca": "Katalan", "kk": "Kazakh", "km": "Khmer", "rw": "Kinyarwanda", "ky": "<PERSON><PERSON><PERSON>", "ko": "Korea", "co": "<PERSON><PERSON><PERSON>", "ht": "Kreol Haiti", "kri": "<PERSON><PERSON>", "hr": "Kroasia", "ku": "<PERSON><PERSON>", "ckb": "<PERSON><PERSON>", "lo": "Lao", "la": "Latin", "lv": "<PERSON><PERSON><PERSON>", "ln": "Lingala", "lt": "<PERSON><PERSON><PERSON>", "lb": "Luksemburg", "mai": "<PERSON><PERSON><PERSON>", "mk": "Makedonia", "mg": "Malagasi", "ml": "Malayalam", "mt": "Malta", "mni": "Manipuri (<PERSON><PERSON><PERSON>)", "mi": "<PERSON><PERSON>", "mr": "Marathi", "ms": "<PERSON><PERSON><PERSON>", "lus": "<PERSON><PERSON>", "mn": "Mongolia", "ne": "Nepali", "no": "Norwegia", "ny": "<PERSON><PERSON><PERSON>", "or": "Oriya", "om": "Oromo", "ps": "Pashto", "fa": "Persia", "pl": "<PERSON><PERSON>", "pt": "Portugis", "fr": "<PERSON><PERSON><PERSON>", "pa": "Punjabi", "qu": "Quechua", "ro": "Rumania", "ru": "Rusia", "sm": "Samoa", "sa": "Sanskerta", "sr": "Serbia", "sn": "<PERSON><PERSON><PERSON>", "sd": "Sindhi", "si": "Sinhala", "sk": "Slovak", "sl": "Sloven", "so": "Somalia", "st": "<PERSON><PERSON><PERSON>", "nso": "<PERSON><PERSON><PERSON>", "es": "Spanyol", "su": "Sunda", "fi": "<PERSON><PERSON>", "sw": "Swahili", "sv": "Swedia", "tl": "Tagalog", "tg": "Tajik", "ta": "Tamil", "tt": "Tatar", "te": "Telugu", "th": "Thai", "ti": "<PERSON><PERSON><PERSON><PERSON>", "ts": "Tsonga", "tr": "<PERSON><PERSON><PERSON>", "tk": "Turkmen", "uk": "<PERSON><PERSON><PERSON>", "ur": "Urdu", "ug": "Uyghur", "uz": "Uzbek", "vi": "Vietnam", "cy": "Welsh", "xh": "Xhosa", "yi": "Yiddish", "yo": "Yoruba", "el": "<PERSON><PERSON>", "zu": "Zulu"}, "penilaian": {"bs": "Baik Sekali", "b": "Baik", "c": "<PERSON><PERSON><PERSON>", "k": "<PERSON><PERSON>", "ks": "<PERSON><PERSON>"}}, "tidak_ada": "Tidak ada data", "pencarian": "Masukkan minimal 3 karakter untuk pencarian", "status_marital": {"status1": "Tidak", "status2": "<PERSON><PERSON><PERSON>", "status3": "<PERSON><PERSON>", "status4": "Ya", "status5": "<PERSON><PERSON>", "status6": "<PERSON><PERSON>"}, "presentasi_detail": {"diperlukan": "<PERSON><PERSON><PERSON><PERSON>", "tidak": "Tidak Diperlukan", "sangat": "<PERSON><PERSON> (Ahli)"}, "presentasi": {"kurang": "<PERSON><PERSON>", "cukup": "<PERSON><PERSON><PERSON>", "sangat": "Sangat Baik"}, "status_lamaran": {"terima": "Terima Rekrutmen", "tolak": "<PERSON>lak <PERSON>ru<PERSON>", "proses_psikotes": "On Proccess P<PERSON>", "proses_dcv": "On Proccess Digitalcv", "proses_gestalt": "Psikotes Gestalt", "selesai_psikotes": "<PERSON><PERSON><PERSON>", "tolak_dcv": "Tolak Digitalcv", "dikirim": "<PERSON><PERSON>"}, "controller": {"cari_posisi_pekerjaan": "<PERSON><PERSON>", "pilih_posisi_pekerjaan": "<PERSON><PERSON><PERSON>", "gagal_mengirim_pesan": "<PERSON><PERSON> men<PERSON>m pesan", "chat": "<PERSON><PERSON>", "info-lowongan": "Lowongan ini sudah tidak aktif", "tahun_masuk_lebih": "<PERSON><PERSON> masuk tidak boleh melebihi tahun keluar", "judul_belum_login": "Untuk dapat mengakses fitur digitalcv, silakan login terlebih dahulu.", "unauthorized": "Anda tidak mempunyai akses", "server_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han pada server", "invalid_format": "Format data tidak valid", "apply_success": "<PERSON><PERSON>", "apply_failed": "<PERSON><PERSON> men<PERSON>m lamaran", "register_success": "Pendaft<PERSON> ber<PERSON>il, silah<PERSON> cek email untuk verifikasi akun.", "register_failed": "Pendaft<PERSON> gagal", "otp_success": "Permintaan OTP berhasil. Periksa email Anda untuk melihat kode verifikasi akun.", "otp_failed": "Gagal mengirim permintaan OTP", "save_success": "Data berhasil disimpan", "save_failed": "Gagal menyimpan data", "update_success": "Data berhasil diperbarui", "update_failed": "<PERSON><PERSON> memper<PERSON> data", "delete_success": "Data berhasil dihapus", "delete_failed": "Gagal menghapus data", "data_not_found": "Data tidak ditemukan", "show_data_failed": "Gagal menampilkan data", "login_success": "<PERSON><PERSON> ber<PERSON>il", "login_failed": "Silahkan masukan email yang sesuai", "check_email": "Silahkan periksa email <PERSON><PERSON>", "req_pass_failed": "Gagal mengirim permintaan ganti kata sandi", "reset_pass_success": "Reset kata sandi ber<PERSON>il", "reset_pass_failed": "Reset kata sandi gagal", "change_pass_success": "<PERSON><PERSON> kata sandi ber<PERSON>il", "change_pass_failed": "<PERSON><PERSON> kata sandi gagal", "change_email_success": "Ubah email berhasil", "change_email_failed": "Ubah email gagal", "change_phone_success": "Ubah nomor telepon berhasil", "change_phone_failed": "Ubah nomor telepon gagal", "upload_success": "<PERSON><PERSON><PERSON> be<PERSON>", "upload_failed": "<PERSON><PERSON>", "no_internet_connection": "Tidak ada koneksi internet", "logout_success": "Logout berhasil!", "wrong_otp": "OTP Salah!", "email_not_found": "Email belum terdaftar!", "email_is_exist": "Email sudah terdaftar!", "noTelp_is_exist": "No. Handphone sudah terdaftar!", "email_noTelp_is_exist": "Email dan No. Handphone sudah terdaftar!", "wrong_pass": "Email atau password salah", "token_used": "Token sudah terpakai. <PERSON><PERSON><PERSON> lakukan proses lupa password kembali.", "token_expired": "Token sudah tidak berlaku. Silakan lakukan proses lupa password kembali.", "token_not_found": "Token tidak terdaftar. <PERSON>lakan lakukan proses lupa password kembali.", "wrong_old_pass": "<PERSON>a sandi lama salah. <PERSON><PERSON><PERSON> se<PERSON> kembali.", "login_gagal": "<PERSON>kun terkunci karena terlalu banyak percobaan login gagal. Coba lagi dalam", "sisa_login": "<PERSON><PERSON> per<PERSON> login:", "kriteria_password": "Kriteria Password:", "min_length": "Minimal 8 karakter", "has_uppercase": "Mengandung huruf besar & huruf kecil", "has_number": "Mengandung angka", "has_special_char": "Mengandung karakter khusus", "validasi": {"password_kosong": "Password tidak boleh kosong", "min_password": "Password minimal 8 karakter", "has_uppercase": "Password harus mengandung huruf besar & huruf kecil", "has_number": "Password harus mengandung minimal 1 angka", "has_special_char": "Password harus mengandung minimal 1 karakter khusus"}, "change_visibility_success": "Ubah visibilitas berhasil", "change_visibility_failed": "Ubah visibilitas gagal", "change_status_kerja_success": "Ubah status kerja berhasil", "change_status_kerja_failed": "Ubah status kerja gagal"}, "location_disclosure": {"title": "<PERSON><PERSON>", "subtitle": "Mengapa kami memerlukan akses lokasi <PERSON>?", "description": "Digital CV meminta izin untuk mengakses lokasi Anda guna meningkatkan pengalaman pencarian kerja Anda. Data lokasi akan digunakan untuk tujuan-tujuan berikut:", "usage_title": "Penggunaan Data Lokasi:", "usage_1": "Menampilkan lowongan kerja yang relevan di sekitar lokasi <PERSON>a", "usage_2": "Menunjukkan informasi perusahaan terdekat yang sedang merekrut", "usage_3": "Membantu navigasi ke lokasi interview at<PERSON> kantor per<PERSON>an", "usage_4": "<PERSON><PERSON><PERSON> dan peningkatan layanan berbasis geografis", "data_sharing": "Data lokasi Anda akan disimpan dengan aman dan hanya dibagikan kepada mitra perusahaan yang relevan dengan persetujuan Anda untuk tujuan perekrutan.", "rights_title": "Hak Anda:", "rights_description": "Anda dapat menolak memberikan izin lokasi, namun beberapa fitur aplikasi mungkin tidak optimal. Anda juga dapat mengubah pengaturan izin kapan saja melalui pengaturan perangkat Anda.", "accept": "Setuju & Berikan <PERSON>", "decline": "Tidak <PERSON>ng"}, "nearby_jobs": {"find_nearby": "<PERSON><PERSON>", "searching": "<PERSON><PERSON><PERSON> di sekitar <PERSON>...", "no_jobs_found": "Tidak ada lowongan di sekitar lokasi Anda", "location_required": "<PERSON>kses lokasi diperlukan untuk fitur ini"}}